#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发测试keywordMatching接口
使用test_tasks.py中的任务数据，并发10个请求测试部署后的服务
"""

import asyncio
import aiohttp
import json
import time
import random
from typing import List, Dict, Any

# 直接导入任务数据
def load_tasks():
    """加载测试任务数据"""
    try:
        with open('test_tasks.py', 'r', encoding='utf-8') as f:
            content = f.read()
            # 执行文件内容来获取tasks变量
            local_vars = {}
            exec(content, {}, local_vars)
            return local_vars.get('tasks', [])
    except Exception as e:
        print(f"加载任务数据失败: {e}")
        # 返回一些示例任务作为备用
        return [
            {"keyword": "органайзер для проводов", "target_product_id": 303244457},
            {"keyword": "держатель для проводов", "target_product_id": 303244457},
            {"keyword": "люстра на потолок", "target_product_id": 253486273},
            {"keyword": "люстра светодиодная", "target_product_id": 253486273},
        ]

tasks = load_tasks()

# 服务配置
SERVICE_URL = "http://localhost:8001/keyword-matching"
GATEWAY_URL = "http://localhost/api/product-similarity/keyword-matching"
CONCURRENT_LIMIT = 10  # 并发数量
TEST_COUNT = 50  # 测试任务数量

class ConcurrentTester:
    def __init__(self, base_url: str = SERVICE_URL, concurrent_limit: int = CONCURRENT_LIMIT):
        self.base_url = base_url
        self.concurrent_limit = concurrent_limit
        self.semaphore = asyncio.Semaphore(concurrent_limit)
        self.results = []
        self.success_count = 0
        self.error_count = 0
        
    async def single_request(self, session: aiohttp.ClientSession, task_data: Dict[str, Any], task_id: int) -> Dict[str, Any]:
        """执行单个请求"""
        async with self.semaphore:
            start_time = time.time()
            try:
                print(f"🚀 [任务{task_id:02d}] 开始测试: {task_data['keyword']} -> {task_data['target_product_id']}")
                
                async with session.post(
                    self.base_url,
                    json=task_data,
                    timeout=aiohttp.ClientTimeout(total=400)  # 6分钟超时 + 缓冲
                ) as response:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        result = {
                            'task_id': task_id,
                            'keyword': task_data['keyword'],
                            'target_product_id': task_data['target_product_id'],
                            'status': 'success',
                            'processing_time': processing_time,
                            'similarity': data.get('data', {}).get('avg_similarity', 0),
                            'similar_count': data.get('data', {}).get('similar_count', 0),
                            'competitor_count': data.get('data', {}).get('competitor_count', 0),
                            'valid_scores': data.get('data', {}).get('valid_scores', 0),
                            'from_cache': data.get('data', {}).get('from_cache', False),
                            'response_data': data
                        }
                        self.success_count += 1
                        print(f"✅ [任务{task_id:02d}] 成功 - 相似度:{result['similarity']} 时间:{processing_time:.2f}s 缓存:{result['from_cache']}")
                    else:
                        error_text = await response.text()
                        result = {
                            'task_id': task_id,
                            'keyword': task_data['keyword'],
                            'target_product_id': task_data['target_product_id'],
                            'status': 'error',
                            'processing_time': processing_time,
                            'error_code': response.status,
                            'error_message': error_text[:200]
                        }
                        self.error_count += 1
                        print(f"❌ [任务{task_id:02d}] 失败 - 状态码:{response.status} 时间:{processing_time:.2f}s")
                        
            except asyncio.TimeoutError:
                end_time = time.time()
                processing_time = end_time - start_time
                result = {
                    'task_id': task_id,
                    'keyword': task_data['keyword'],
                    'target_product_id': task_data['target_product_id'],
                    'status': 'timeout',
                    'processing_time': processing_time,
                    'error_message': 'Request timeout (>400s)'
                }
                self.error_count += 1
                print(f"⏰ [任务{task_id:02d}] 超时 - 时间:{processing_time:.2f}s")
                
            except Exception as e:
                end_time = time.time()
                processing_time = end_time - start_time
                result = {
                    'task_id': task_id,
                    'keyword': task_data['keyword'],
                    'target_product_id': task_data['target_product_id'],
                    'status': 'exception',
                    'processing_time': processing_time,
                    'error_message': str(e)
                }
                self.error_count += 1
                print(f"💥 [任务{task_id:02d}] 异常 - {str(e)[:50]}... 时间:{processing_time:.2f}s")
                
            return result
    
    async def run_concurrent_test(self, test_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """运行并发测试"""
        print(f"🎯 开始并发测试")
        print(f"📊 测试配置: {len(test_tasks)}个任务, {self.concurrent_limit}个并发")
        print(f"🌐 服务地址: {self.base_url}")
        print("=" * 80)
        
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            # 创建所有任务
            tasks_coroutines = [
                self.single_request(session, task_data, i + 1)
                for i, task_data in enumerate(test_tasks)
            ]
            
            # 并发执行所有任务
            self.results = await asyncio.gather(*tasks_coroutines, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 处理异常结果
        processed_results = []
        for result in self.results:
            if isinstance(result, Exception):
                processed_results.append({
                    'status': 'exception',
                    'error_message': str(result),
                    'processing_time': 0
                })
            else:
                processed_results.append(result)
        
        self.results = processed_results
        
        # 打印测试结果
        self.print_summary(total_time)
        
        return self.results
    
    def print_summary(self, total_time: float):
        """打印测试总结"""
        print("\n" + "=" * 80)
        print("📊 测试结果总结")
        print("=" * 80)
        
        total_tasks = len(self.results)
        success_rate = (self.success_count / total_tasks * 100) if total_tasks > 0 else 0
        
        print(f"📈 总体统计:")
        print(f"   • 总任务数: {total_tasks}")
        print(f"   • 成功任务: {self.success_count}")
        print(f"   • 失败任务: {self.error_count}")
        print(f"   • 成功率: {success_rate:.1f}%")
        print(f"   • 总耗时: {total_time:.2f}秒")
        print(f"   • 平均耗时: {total_time/total_tasks:.2f}秒/任务")
        
        # 统计处理时间
        processing_times = [r.get('processing_time', 0) for r in self.results if r.get('processing_time')]
        if processing_times:
            print(f"\n⏱️ 处理时间统计:")
            print(f"   • 最快: {min(processing_times):.2f}秒")
            print(f"   • 最慢: {max(processing_times):.2f}秒")
            print(f"   • 平均: {sum(processing_times)/len(processing_times):.2f}秒")
        
        # 统计相似度
        successful_results = [r for r in self.results if r.get('status') == 'success']
        if successful_results:
            similarities = [r.get('similarity', 0) for r in successful_results]
            cache_hits = sum(1 for r in successful_results if r.get('from_cache', False))
            cache_rate = (cache_hits / len(successful_results) * 100) if successful_results else 0
            
            print(f"\n🎯 业务指标统计:")
            print(f"   • 相似度范围: {min(similarities)}-{max(similarities)}")
            print(f"   • 平均相似度: {sum(similarities)/len(similarities):.1f}")
            print(f"   • 缓存命中: {cache_hits}/{len(successful_results)} ({cache_rate:.1f}%)")
        
        # 统计错误类型
        error_results = [r for r in self.results if r.get('status') != 'success']
        if error_results:
            error_types = {}
            for r in error_results:
                error_type = r.get('status', 'unknown')
                error_types[error_type] = error_types.get(error_type, 0) + 1
            
            print(f"\n❌ 错误统计:")
            for error_type, count in error_types.items():
                print(f"   • {error_type}: {count}次")

def select_test_tasks(all_tasks: List[Dict], count: int = TEST_COUNT) -> List[Dict]:
    """从所有任务中选择测试任务"""
    if len(all_tasks) <= count:
        return all_tasks
    
    # 随机选择任务，但确保包含不同的产品ID
    unique_products = {}
    for task in all_tasks:
        product_id = task['target_product_id']
        if product_id not in unique_products:
            unique_products[product_id] = []
        unique_products[product_id].append(task)
    
    selected_tasks = []
    
    # 从每个产品ID中选择一些任务
    products = list(unique_products.keys())
    tasks_per_product = max(1, count // len(products))
    
    for product_id in products:
        product_tasks = unique_products[product_id]
        selected_count = min(tasks_per_product, len(product_tasks))
        selected_tasks.extend(random.sample(product_tasks, selected_count))
        
        if len(selected_tasks) >= count:
            break
    
    # 如果还不够，随机补充
    if len(selected_tasks) < count:
        remaining_tasks = [t for t in all_tasks if t not in selected_tasks]
        additional_count = count - len(selected_tasks)
        if remaining_tasks:
            selected_tasks.extend(random.sample(remaining_tasks, min(additional_count, len(remaining_tasks))))
    
    return selected_tasks[:count]

async def main():
    """主函数"""
    print("🔧 keywordMatching接口并发测试")
    print("=" * 80)
    
    # 选择测试任务
    test_tasks = select_test_tasks(tasks, TEST_COUNT)
    print(f"📋 已选择 {len(test_tasks)} 个测试任务")
    
    # 显示产品ID分布
    product_ids = {}
    for task in test_tasks:
        pid = task['target_product_id']
        product_ids[pid] = product_ids.get(pid, 0) + 1
    
    print(f"🎯 产品ID分布:")
    for pid, count in product_ids.items():
        print(f"   • {pid}: {count}个任务")
    
    print()
    
    # 测试直接访问
    print("🔗 测试1: 直接访问服务 (localhost:8001)")
    tester1 = ConcurrentTester(SERVICE_URL, CONCURRENT_LIMIT)
    await tester1.run_concurrent_test(test_tasks)
    
    # 可选：测试网关访问
    print("\n" + "=" * 80)
    print("🔗 测试2: 网关访问服务 (localhost/api/...)")
    tester2 = ConcurrentTester(GATEWAY_URL, CONCURRENT_LIMIT)
    await tester2.run_concurrent_test(test_tasks)

if __name__ == "__main__":
    asyncio.run(main())
