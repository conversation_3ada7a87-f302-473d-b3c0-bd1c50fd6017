#!/usr/bin/env python3
"""
修复缓存数据格式问题
"""
import asyncio
import json
import sys
sys.path.append('.')

from src.product_similarity.db import get_pool

async def fix_cache_data():
    """修复缓存数据格式"""
    
    print("🔧 修复缓存数据格式")
    print("=" * 50)
    
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            
            # 1. 查找所有字符串类型的缓存数据
            print("📊 查找需要修复的缓存数据...")
            rows = await conn.fetch("""
                SELECT cache_key, cache_value 
                FROM pj_similar.cache 
                WHERE jsonb_typeof(cache_value) = 'string'
            """)
            
            print(f"找到 {len(rows)} 条需要修复的缓存数据")
            
            if len(rows) == 0:
                print("✅ 没有需要修复的数据")
                return
            
            # 2. 逐条修复数据
            fixed_count = 0
            error_count = 0
            
            for row in rows:
                cache_key = row['cache_key']
                cache_value = row['cache_value']
                
                try:
                    # 如果cache_value是字符串，尝试解析为JSON
                    if isinstance(cache_value, str):
                        parsed_data = json.loads(cache_value)
                        
                        # 更新数据库中的记录
                        await conn.execute("""
                            UPDATE pj_similar.cache 
                            SET cache_value = $1::jsonb, updated_at = NOW()
                            WHERE cache_key = $2
                        """, parsed_data, cache_key)
                        
                        fixed_count += 1
                        print(f"✅ 修复缓存: {cache_key[:20]}...")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ 无法解析JSON: {cache_key[:20]}... - {e}")
                    error_count += 1
                except Exception as e:
                    print(f"❌ 修复失败: {cache_key[:20]}... - {e}")
                    error_count += 1
            
            print(f"\n📊 修复结果:")
            print(f"  成功修复: {fixed_count} 条")
            print(f"  修复失败: {error_count} 条")
            
            if fixed_count > 0:
                print("✅ 缓存数据格式修复完成！")
            
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")

if __name__ == "__main__":
    asyncio.run(fix_cache_data())
