#!/usr/bin/env python3
"""
并发关键词匹配测试脚本
使用 test_tasks.py 中的任务参数，并发测试 keywordMatching 函数
保持10个任务同时运行，当有任务结束时补充新任务
"""

import asyncio
import json
import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import traceback
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_tasks import tasks
from src.product_similarity.services.keyword_matching import keywordMatching

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('concurrent_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class ConcurrentKeywordTester:
    def __init__(self, max_concurrent: int = 10):
        self.max_concurrent = max_concurrent
        self.running_tasks = set()
        self.completed_tasks = []
        self.failed_tasks = []
        self.task_queue = []
        self.start_time = None
        self.stats = {
            'total_started': 0,
            'total_completed': 0,
            'total_failed': 0,
            'cache_hits': 0,
            'new_calculations': 0,
            'avg_similarity_sum': 0,
            'valid_similarity_count': 0
        }
        
    async def run_single_task(self, task_data: Dict[str, Any], task_id: int) -> Dict[str, Any]:
        """运行单个关键词匹配任务"""
        keyword = task_data['keyword']
        target_product_id = task_data['target_product_id']
        
        start_time = time.time()
        
        try:
            logger.info(f"🚀 [任务 {task_id}] 开始: {keyword} -> {target_product_id}")
            
            result = await keywordMatching(keyword, target_product_id)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 分析结果
            task_result = {
                'task_id': task_id,
                'keyword': keyword,
                'target_product_id': target_product_id,
                'duration': duration,
                'start_time': start_time,
                'end_time': end_time,
                'status': result.get('status', 'unknown'),
                'result': result
            }
            
            if result.get('status') == 'success':
                data = result.get('data', {})
                is_cached = data.get('from_cache', False)
                avg_similarity = data.get('avg_similarity', 0)
                
                # 更新统计
                self.stats['total_completed'] += 1
                if is_cached:
                    self.stats['cache_hits'] += 1
                else:
                    self.stats['new_calculations'] += 1
                
                if avg_similarity > 0:
                    self.stats['avg_similarity_sum'] += avg_similarity
                    self.stats['valid_similarity_count'] += 1
                
                logger.info(f"✅ [任务 {task_id}] 完成: {keyword} -> 相似度: {avg_similarity}, "
                          f"耗时: {duration:.2f}s, 缓存: {'是' if is_cached else '否'}")
                
                self.completed_tasks.append(task_result)
            else:
                self.stats['total_failed'] += 1
                error_msg = result.get('message', '未知错误')
                logger.error(f"❌ [任务 {task_id}] 失败: {keyword} -> {error_msg}, 耗时: {duration:.2f}s")
                self.failed_tasks.append(task_result)
            
            return task_result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            self.stats['total_failed'] += 1
            
            error_result = {
                'task_id': task_id,
                'keyword': keyword,
                'target_product_id': target_product_id,
                'duration': duration,
                'start_time': start_time,
                'end_time': end_time,
                'status': 'exception',
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            
            logger.error(f"💥 [任务 {task_id}] 异常: {keyword} -> {str(e)}, 耗时: {duration:.2f}s")
            logger.debug(f"异常详情: {traceback.format_exc()}")
            
            self.failed_tasks.append(error_result)
            return error_result
    
    async def task_wrapper(self, task_data: Dict[str, Any], task_id: int):
        """任务包装器，处理任务的生命周期"""
        try:
            result = await self.run_single_task(task_data, task_id)
            return result
        finally:
            # 任务完成后从运行集合中移除
            self.running_tasks.discard(task_id)
    
    def print_stats(self):
        """打印当前统计信息"""
        if self.start_time:
            elapsed = time.time() - self.start_time
            avg_similarity = (self.stats['avg_similarity_sum'] / self.stats['valid_similarity_count'] 
                            if self.stats['valid_similarity_count'] > 0 else 0)
            
            print(f"\n📊 实时统计 (运行时间: {elapsed:.1f}s)")
            print(f"   总启动: {self.stats['total_started']}")
            print(f"   已完成: {self.stats['total_completed']}")
            print(f"   失败数: {self.stats['total_failed']}")
            print(f"   运行中: {len(self.running_tasks)}")
            print(f"   队列中: {len(self.task_queue)}")
            print(f"   缓存命中: {self.stats['cache_hits']}")
            print(f"   新计算: {self.stats['new_calculations']}")
            print(f"   平均相似度: {avg_similarity:.1f}")
            
            if self.stats['total_completed'] > 0:
                success_rate = (self.stats['total_completed'] / 
                              (self.stats['total_completed'] + self.stats['total_failed']) * 100)
                print(f"   成功率: {success_rate:.1f}%")
    
    async def run_concurrent_test(self, test_tasks: List[Dict[str, Any]]):
        """运行并发测试"""
        self.start_time = time.time()
        self.task_queue = test_tasks.copy()
        task_id_counter = 0
        
        logger.info(f"🎯 开始并发测试: 总任务数 {len(test_tasks)}, 最大并发数 {self.max_concurrent}")
        
        try:
            # 启动初始任务
            while len(self.running_tasks) < self.max_concurrent and self.task_queue:
                task_data = self.task_queue.pop(0)
                task_id_counter += 1
                self.stats['total_started'] += 1
                
                # 创建并启动任务
                task = asyncio.create_task(self.task_wrapper(task_data, task_id_counter))
                self.running_tasks.add(task_id_counter)
                
                # 不等待任务完成，让它在后台运行
                asyncio.create_task(self._handle_task_completion(task, task_id_counter))
            
            # 监控循环
            last_stats_time = time.time()
            while self.running_tasks or self.task_queue:
                await asyncio.sleep(1)  # 每秒检查一次
                
                # 每10秒打印一次统计
                if time.time() - last_stats_time >= 10:
                    self.print_stats()
                    last_stats_time = time.time()
                
                # 补充新任务
                while len(self.running_tasks) < self.max_concurrent and self.task_queue:
                    task_data = self.task_queue.pop(0)
                    task_id_counter += 1
                    self.stats['total_started'] += 1
                    
                    task = asyncio.create_task(self.task_wrapper(task_data, task_id_counter))
                    self.running_tasks.add(task_id_counter)
                    asyncio.create_task(self._handle_task_completion(task, task_id_counter))
            
            logger.info("🏁 所有任务已完成")
            
        except KeyboardInterrupt:
            logger.info("⚠️ 收到中断信号，正在停止测试...")
            # 取消所有运行中的任务
            for task_id in list(self.running_tasks):
                # 这里我们不能直接取消任务，因为我们只有task_id
                # 实际的任务取消需要更复杂的实现
                pass
        
        # 最终统计
        self.print_final_stats()
        
        return {
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'stats': self.stats
        }
    
    async def _handle_task_completion(self, task: asyncio.Task, task_id: int):
        """处理任务完成"""
        try:
            await task
        except Exception as e:
            logger.error(f"任务 {task_id} 处理异常: {e}")
    
    def print_final_stats(self):
        """打印最终统计信息"""
        total_time = time.time() - self.start_time if self.start_time else 0
        total_tasks = self.stats['total_completed'] + self.stats['total_failed']
        
        avg_similarity = (self.stats['avg_similarity_sum'] / self.stats['valid_similarity_count'] 
                        if self.stats['valid_similarity_count'] > 0 else 0)
        
        print(f"\n🎉 测试完成！最终统计:")
        print(f"   总耗时: {total_time:.1f}s")
        print(f"   总任务数: {total_tasks}")
        print(f"   成功任务: {self.stats['total_completed']}")
        print(f"   失败任务: {self.stats['total_failed']}")
        print(f"   成功率: {(self.stats['total_completed']/total_tasks*100):.1f}%" if total_tasks > 0 else "N/A")
        print(f"   缓存命中: {self.stats['cache_hits']}")
        print(f"   新计算: {self.stats['new_calculations']}")
        print(f"   缓存命中率: {(self.stats['cache_hits']/total_tasks*100):.1f}%" if total_tasks > 0 else "N/A")
        print(f"   平均相似度: {avg_similarity:.1f}")
        print(f"   平均处理时间: {(total_time/total_tasks):.2f}s/任务" if total_tasks > 0 else "N/A")


async def main():
    """主函数"""
    print("🔧 并发关键词匹配测试")
    print(f"📋 加载任务数据: {len(tasks)} 个任务")
    
    # 创建测试器
    tester = ConcurrentKeywordTester(max_concurrent=10)
    
    try:
        # 运行测试
        results = await tester.run_concurrent_test(tasks)
        
        # 保存结果
        timestamp = int(time.time())
        result_file = f"concurrent_test_results_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                'test_config': {
                    'max_concurrent': 10,
                    'total_tasks': len(tasks),
                    'start_time': datetime.fromtimestamp(tester.start_time).isoformat()
                },
                'results': results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细结果已保存到: {result_file}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        logger.debug(traceback.format_exc())


if __name__ == "__main__":
    asyncio.run(main())
