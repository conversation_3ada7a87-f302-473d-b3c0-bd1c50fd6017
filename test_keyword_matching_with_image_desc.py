#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试关键词匹配功能（包含图片描述）
"""

import asyncio
import json
from src.product_similarity.services.keyword_matching import keywordMatching
from src.product_similarity.db import init_pool, close_pool
from test_product_ids import nm_ids

async def test_keyword_matching_with_image_desc():
    """测试关键词匹配功能（包含图片描述）"""
    try:
        # 初始化数据库连接池
        await init_pool()
        print("数据库连接池初始化完成")
        
        # 使用真实的产品ID进行测试
        test_keyword = "органайзер для стола"  # 桌面整理器的俄文
        test_product_id = int(nm_ids[0])  # 使用第一个真实产品ID
        
        print(f"开始测试关键词匹配: '{test_keyword}' -> {test_product_id}")
        print("=" * 60)
        
        # 执行关键词匹配分析
        result = await keywordMatching(test_keyword, test_product_id)
        
        print("=== 测试结果 ===")
        print(f"状态: {result.get('status')}")
        
        if result.get('status') == 'success':
            data = result.get('data', {})
            print(f"关键词: {data.get('keyword')}")
            print(f"目标产品ID: {data.get('target_product_id')}")
            print(f"平均相似度: {data.get('avg_similarity')}")
            print(f"相似产品数量: {data.get('similar_count')}")
            print(f"竞品数量: {data.get('competitor_count')}")
            print(f"有效对比数量: {data.get('valid_scores')}")
            print(f"搜索产品数量: {data.get('search_products_count')}")
            print(f"对比产品数量: {data.get('compared_products_count')}")
            print(f"是否来自缓存: {data.get('from_cache')}")
            print(f"创建时间: {data.get('created_at')}")
        else:
            print(f"错误信息: {result.get('message')}")
        
        print("\n" + "=" * 60)
        print("完整结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("数据库连接池已关闭")

async def test_keyword_matching_cache():
    """测试关键词匹配缓存功能"""
    try:
        await init_pool()
        print("=" * 60)
        print("测试关键词匹配缓存功能")
        print("=" * 60)
        
        test_keyword = "органайзер для стола"
        test_product_id = int(nm_ids[0])
        
        print(f"第一次调用: '{test_keyword}' -> {test_product_id}")
        result1 = await keywordMatching(test_keyword, test_product_id)
        
        if result1.get('status') == 'success':
            data1 = result1.get('data', {})
            print(f"✅ 第一次成功")
            print(f"   平均相似度: {data1.get('avg_similarity')}")
            print(f"   是否来自缓存: {data1.get('from_cache')}")
            print(f"   处理时间: 约2-3秒")
        
        print(f"\n第二次调用（应该从缓存获取）:")
        result2 = await keywordMatching(test_keyword, test_product_id)
        
        if result2.get('status') == 'success':
            data2 = result2.get('data', {})
            print(f"✅ 第二次成功")
            print(f"   平均相似度: {data2.get('avg_similarity')}")
            print(f"   是否来自缓存: {data2.get('from_cache')}")
            print(f"   处理时间: 瞬间完成")
            
            # 验证结果一致性
            if data1.get('avg_similarity') == data2.get('avg_similarity'):
                print("✅ 缓存结果一致性验证通过")
            else:
                print("❌ 缓存结果不一致")
        
    except Exception as e:
        print(f"缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await close_pool()

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 关键词匹配功能测试")
    print("2. 关键词匹配缓存测试")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_keyword_matching_with_image_desc())
    elif choice == "2":
        asyncio.run(test_keyword_matching_cache())
    else:
        print("无效选择，默认运行功能测试")
        asyncio.run(test_keyword_matching_with_image_desc())
