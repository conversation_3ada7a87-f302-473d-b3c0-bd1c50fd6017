#!/usr/bin/env python3
"""
测试重构后的智能Basket系统（无置信度版本）
验证_parse_product_url_smart和动态检测功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import (
    get_product_detail, 
    _parse_product_url_smart, 
    _detect_correct_basket,
    _save_basket_sample
)
from product_similarity.crud import get_basket_stats, add_basket_sample
from product_similarity.db import init_pool, close_pool, get_pool
from product_similarity.logging import log_success, log_error, log_info, log_warning
from test_product_ids import nm_ids

async def test_database_tables():
    """测试数据库表结构"""
    log_info("=== 测试数据库表结构 ===")
    
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            # 检查basket_samples表结构
            samples_columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'pj_similar' 
                AND table_name = 'basket_samples'
                ORDER BY ordinal_position
            """)
            
            log_info("basket_samples表结构:")
            for col in samples_columns:
                log_info(f"  {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
            
            # 检查basket_mapping表结构
            mapping_columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'pj_similar' 
                AND table_name = 'basket_mapping'
                ORDER BY ordinal_position
            """)
            
            log_info("basket_mapping表结构:")
            for col in mapping_columns:
                log_info(f"  {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
            
            # 测试存储过程
            result = await conn.fetchval("SELECT pj_similar.recalculate_basket_ranges()")
            log_info(f"重新计算范围结果: {result}")
            
            result = await conn.fetchval("SELECT pj_similar.cleanup_old_samples()")
            log_info(f"清理过期样本结果: {result}")
            
        log_success("数据库表结构测试完成")
        
    except Exception as e:
        log_error("数据库表结构测试失败", error=e)

async def test_smart_url_parsing():
    """测试智能URL解析"""
    log_info("=== 测试智能URL解析 ===")
    
    # 使用真实产品ID进行测试
    test_ids = nm_ids[:3]  # 取前3个进行测试
    
    for product_id in test_ids:
        try:
            log_info(f"测试产品ID: {product_id}")
            
            # 测试智能URL解析
            url_info = await _parse_product_url_smart(product_id)
            
            log_info(f"解析结果:")
            log_info(f"  basket: {url_info['basket']}")
            log_info(f"  source: {url_info['source']}")
            log_info(f"  short_id: {url_info['short_id']}")
            log_info(f"  product_url: {url_info['product_url']}")
            
        except Exception as e:
            log_error(f"智能URL解析失败: {product_id}", error=e)

async def test_basket_sample_operations():
    """测试basket样本操作"""
    log_info("=== 测试basket样本操作 ===")
    
    try:
        # 添加测试样本
        test_samples = [
            ("01", 1000),
            ("02", 2000),
            ("03", 3000),
        ]
        
        for basket, short_id in test_samples:
            success = await add_basket_sample(basket, short_id)
            if success:
                log_success(f"添加样本成功: basket={basket}, short_id={short_id}")
            else:
                log_warning(f"添加样本失败: basket={basket}, short_id={short_id}")
        
        # 获取统计信息
        stats = await get_basket_stats()
        log_info("Basket统计信息:")
        log_info(f"  样本统计: {stats.get('sample_stats', {})}")
        log_info(f"  映射统计: {stats.get('mapping_stats', {})}")
        
    except Exception as e:
        log_error("basket样本操作测试失败", error=e)

async def test_dynamic_detection():
    """测试动态basket检测"""
    log_info("=== 测试动态basket检测 ===")
    
    # 使用一个真实产品ID测试
    test_id = nm_ids[0] if nm_ids else 262205317
    
    try:
        log_info(f"测试动态检测: {test_id}")
        
        # 故意使用错误的basket进行测试
        wrong_basket = "99"  # 很可能是错误的basket
        
        correct_basket = await _detect_correct_basket(test_id, wrong_basket)
        
        if correct_basket:
            log_success(f"动态检测成功: {test_id}, 正确basket={correct_basket}")
        else:
            log_warning(f"动态检测失败: {test_id}")
            
    except Exception as e:
        log_error("动态检测测试失败", error=e)

async def test_full_product_detail():
    """测试完整的产品详情获取（集成测试）"""
    log_info("=== 测试完整产品详情获取 ===")
    
    # 使用真实产品ID测试
    test_ids = nm_ids[:2] if nm_ids else [262205317, 123456789]
    
    for product_id in test_ids:
        try:
            log_info(f"获取产品详情: {product_id}")
            
            product_detail = await get_product_detail(product_id)
            
            log_success(f"成功获取产品详情: {product_id}")
            log_info(f"  产品名称: {product_detail.get('name', 'N/A')}")
            log_info(f"  品牌: {product_detail.get('brand', 'N/A')}")
            log_info(f"  价格: {product_detail.get('price', 'N/A')}")
            log_info(f"  basket: {product_detail.get('basket', 'N/A')}")
            
        except Exception as e:
            log_error(f"获取产品详情失败: {product_id}", error=e)

async def main():
    """主测试函数"""
    log_info("开始测试重构后的智能Basket系统（无置信度版本）")
    
    try:
        # 初始化数据库连接池
        await init_pool()
        
        # 运行所有测试
        await test_database_tables()
        await test_smart_url_parsing()
        await test_basket_sample_operations()
        await test_dynamic_detection()
        await test_full_product_detail()
        
        log_success("所有测试完成")
        
    except Exception as e:
        log_error("测试过程中发生错误", error=e)
    finally:
        # 关闭数据库连接池
        await close_pool()

if __name__ == "__main__":
    asyncio.run(main())
