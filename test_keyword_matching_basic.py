"""
基础测试 keywordMatching 函数
逐步测试，先单个再并发
"""
import asyncio
import sys
import os
import json
import time
import random

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.keyword_matching import keywordMatching
from product_similarity.db import init_pool, close_pool
from test_keywords import keyword_info
from test_product_ids import nm_ids

async def test_single_case():
    """测试单个案例"""
    print("🧪 测试单个案例")
    print("=" * 50)
    
    # 初始化数据库
    await init_pool()
    print("✅ 数据库连接成功")
    
    # 选择测试数据
    keyword = random.choice(keyword_info)
    target_product_id = int(random.choice(nm_ids))
    
    print(f"关键词: {keyword}")
    print(f"目标产品ID: {target_product_id}")
    
    try:
        start_time = time.time()
        result = await keywordMatching(keyword, target_product_id)
        duration = time.time() - start_time
        
        print(f"✅ 成功 ({duration:.2f}秒)")
        print(f"状态: {result.get('status')}")
        print(f"平均相似度: {result.get('avg_similarity')}")
        print(f"相似产品数量: {result.get('similar_count')}")
        
        # 检查图片描述字段
        target_product = result.get("target_product", {})
        if target_product:
            product_info = target_product.get("product_info", {})
            desc_text = product_info.get("images_description_text", "")
            
            print(f"\n🖼️ 目标产品图片描述检查:")
            print(f"有描述: {'是' if desc_text else '否'}")
            print(f"描述长度: {len(desc_text)} 字符")
            if desc_text:
                print(f"描述预览: {desc_text[:100]}...")
            
            # 检查是否还有 image_descriptions 字段
            if 'image_descriptions' in product_info:
                print("⚠️ 警告: 仍包含 image_descriptions 字段")
            else:
                print("✅ 确认: 已删除 image_descriptions 字段")
        
        # 检查相似产品
        similar_products = result.get("similar_products", [])
        desc_count = 0
        for product in similar_products:
            product_info = product.get("product_info", {})
            desc_text = product_info.get("images_description_text", "")
            if desc_text:
                desc_count += 1
        
        print(f"\n🔍 相似产品图片描述:")
        print(f"有描述的产品: {desc_count}/{len(similar_products)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 失败: {e}")
        return False
    
    finally:
        await close_pool()

async def test_concurrent_cases(concurrent_count=10, test_count=15):
    """测试并发案例"""
    print(f"\n🚀 测试并发案例 ({concurrent_count}个并发, {test_count}个测试)")
    print("=" * 50)
    
    # 初始化数据库
    await init_pool()
    print("✅ 数据库连接成功")
    
    success_count = 0
    error_count = 0
    image_desc_count = 0
    
    semaphore = asyncio.Semaphore(concurrent_count)
    
    async def single_test(test_index):
        nonlocal success_count, error_count, image_desc_count
        
        async with semaphore:
            keyword = random.choice(keyword_info)
            target_product_id = int(random.choice(nm_ids))
            
            try:
                start_time = time.time()
                result = await keywordMatching(keyword, target_product_id)
                duration = time.time() - start_time
                
                # 检查图片描述
                target_product = result.get("target_product", {})
                has_desc = False
                if target_product:
                    product_info = target_product.get("product_info", {})
                    desc_text = product_info.get("images_description_text", "")
                    if desc_text and desc_text.strip():
                        has_desc = True
                        image_desc_count += 1
                
                print(f"✅ 测试#{test_index}: {keyword[:30]}... -> {target_product_id} ({duration:.2f}秒) 图片描述:{'有' if has_desc else '无'}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ 测试#{test_index}: {keyword[:30]}... -> {target_product_id} 失败: {str(e)[:50]}...")
                error_count += 1
    
    # 创建并发任务
    start_time = time.time()
    tasks = [asyncio.create_task(single_test(i+1)) for i in range(test_count)]
    await asyncio.gather(*tasks, return_exceptions=True)
    total_duration = time.time() - start_time
    
    await close_pool()
    
    # 打印结果
    print(f"\n📊 并发测试结果:")
    print(f"总测试数量: {test_count}")
    print(f"成功数量: {success_count}")
    print(f"失败数量: {error_count}")
    print(f"成功率: {(success_count/test_count)*100:.1f}%")
    print(f"总耗时: {total_duration:.2f}秒")
    print(f"平均耗时: {total_duration/test_count:.2f}秒/个")
    print(f"图片描述成功: {image_desc_count}/{success_count}")
    
    if success_count > 0:
        desc_rate = (image_desc_count / success_count) * 100
        print(f"图片描述成功率: {desc_rate:.1f}%")
    
    return success_count == test_count and image_desc_count > 0

async def main():
    """主函数"""
    print("🔥 keywordMatching 函数基础测试")
    
    # 1. 先测试单个案例
    single_success = await test_single_case()
    
    if single_success:
        # 2. 再测试并发案例
        concurrent_success = await test_concurrent_cases(concurrent_count=20, test_count=20)
        
        print(f"\n" + "=" * 50)
        print("🎯 最终结果:")
        print("=" * 50)
        
        if concurrent_success:
            print("🎉 所有测试通过！")
            print("✅ 单个请求测试通过")
            print("✅ 20个并发请求测试通过")
            print("✅ images_description_text 字段正常工作")
            print("✅ 使用真实数据测试")
        else:
            print("⚠️ 并发测试存在问题")
    else:
        print("\n❌ 单个案例测试失败，跳过并发测试")

if __name__ == "__main__":
    asyncio.run(main())
