#!/usr/bin/env python3
"""
调试单个关键词匹配请求
"""
import asyncio
import aiohttp
import json
import sys
sys.path.append('.')

async def debug_single_request():
    """调试单个请求"""
    
    print("🔍 调试单个关键词匹配请求")
    print("=" * 50)
    
    # 测试数据
    keyword = "люстра на потолок"
    target_product_id = 253486273
    
    payload = {
        "keyword": keyword,
        "target_product_id": target_product_id,
        "limit": 10
    }
    
    print(f"关键词: {keyword}")
    print(f"目标产品ID: {target_product_id}")
    print(f"限制数量: 10")
    print()
    
    # 测试直接访问
    url = "http://localhost:8001/keyword-matching"
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"🌐 发送请求到: {url}")
            print(f"📦 请求数据: {json.dumps(payload, ensure_ascii=False)}")
            print()
            
            async with session.post(url, json=payload, timeout=aiohttp.ClientTimeout(total=120)) as response:
                print(f"📊 响应状态: {response.status}")
                print(f"📋 响应头: {dict(response.headers)}")
                print()
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ 响应成功!")
                    print(f"📄 完整响应数据:")
                    print(json.dumps(result, ensure_ascii=False, indent=2))
                    print()
                    
                    # 解析关键字段
                    print("🔍 关键字段分析:")
                    print(f"   keyword: {result.get('keyword', 'N/A')}")
                    print(f"   target_product_id: {result.get('target_product_id', 'N/A')}")
                    print(f"   total_products_found: {result.get('total_products_found', 'N/A')}")
                    print(f"   products_compared: {result.get('products_compared', 'N/A')}")
                    print(f"   average_similarity: {result.get('average_similarity', 'N/A')}")
                    print(f"   similar_products_count: {result.get('similar_products_count', 'N/A')}")
                    print(f"   competitor_products_count: {result.get('competitor_products_count', 'N/A')}")
                    print(f"   cached: {result.get('cached', 'N/A')}")
                    print(f"   processing_time: {result.get('processing_time', 'N/A')}")
                    
                    # 检查产品详情
                    if 'products' in result:
                        products = result['products']
                        print(f"\n📦 产品详情 (共{len(products)}个):")
                        for i, product in enumerate(products[:3]):  # 只显示前3个
                            print(f"   产品{i+1}:")
                            print(f"     product_id: {product.get('product_id', 'N/A')}")
                            print(f"     similarity_score: {product.get('similarity_score', 'N/A')}")
                            print(f"     is_similar: {product.get('is_similar', 'N/A')}")
                            print(f"     is_competitor: {product.get('is_competitor', 'N/A')}")
                        if len(products) > 3:
                            print(f"   ... 还有 {len(products) - 3} 个产品")
                    
                else:
                    error_text = await response.text()
                    print(f"❌ 请求失败!")
                    print(f"错误内容: {error_text}")
                    
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_single_request())
