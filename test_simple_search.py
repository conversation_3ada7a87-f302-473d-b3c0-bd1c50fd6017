#!/usr/bin/env python3
"""
简单测试 Wildberries 搜索功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.product_similarity.services.wildberries_search import search_wildberries, extract_product_ids_from_search


async def test_search():
    """测试搜索功能"""
    try:
        # 测试搜索 - 使用俄文关键词
        keyword = "настенный светильник для ванной"  # 浴室壁灯的俄文
        print(f"开始搜索关键词: '{keyword}'")
        
        result = await search_wildberries(keyword)
        
        if result:
            print("搜索成功！")
            print(f"结果类型: {type(result)}")
            
            # 检查数据结构
            if 'data' in result:
                data = result['data']
                if 'products' in data:
                    products = data['products']
                    print(f"找到产品数量: {len(products)}")
                    
                    # 提取产品ID
                    product_ids = extract_product_ids_from_search(result, limit=10)
                    print(f"提取的产品ID (前10个): {product_ids}")
                else:
                    print("未找到 products 字段")
                    print(f"data 字段内容: {list(data.keys()) if isinstance(data, dict) else data}")
            else:
                print("未找到 data 字段")
                print(f"结果字段: {list(result.keys()) if isinstance(result, dict) else result}")
        else:
            print("搜索失败")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_search())
