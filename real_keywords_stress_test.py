#!/usr/bin/env python3
"""
真实关键词高并发压力测试 - 使用test_keywords.py中的100个不同关键词
"""
import asyncio
import time
import sys
import os
import random
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.product_similarity.services.keyword_matching import keywordMatching
from src.product_similarity.db import init_pool, close_pool, get_pool
# 直接从test_keywords.py文件中读取真实关键词数据
def load_real_keywords():
    """从test_keywords.py文件中加载真实关键词数据"""
    try:
        # 直接执行文件内容
        with open('test_keywords.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 创建一个局部命名空间来执行代码
        namespace = {}
        exec(content, namespace)

        # 获取keyword_info变量
        keyword_info = namespace.get('keyword_info', [])
        print(f"✅ 成功加载 {len(keyword_info)} 个真实关键词")
        return keyword_info

    except Exception as e:
        print(f"❌ 加载关键词文件失败: {e}")
        # 返回一些备用关键词
        return [
            {"keyword": "люстра на потолок", "count": 74886},
            {"keyword": "люстра светодиодная на потолок", "count": 28586},
            {"keyword": "светильник потолочный", "count": 21175},
            {"keyword": "светодиодные светильники на потолок", "count": 6302},
            {"keyword": "люстра светодиодная", "count": 5811},
            {"keyword": "плафон потолочный", "count": 4428},
            {"keyword": "люстра потолочная для гостиной", "count": 3414},
            {"keyword": "лампа потолочная", "count": 2375},
            {"keyword": "светильник потолочный на кухню", "count": 2336},
            {"keyword": "люстра для кухни", "count": 2000}
        ]

# 加载真实关键词数据
keyword_info = load_real_keywords()

# 测试配置
TOTAL_TASKS = 100  # 总任务数
MAX_CONCURRENT = 20  # 最大并发数
TARGET_PRODUCT_ID = 253486273  # 目标产品ID


def get_unique_test_keywords(count: int) -> List[str]:
    """从test_keywords.py中获取100个不同的关键词"""
    print(f"📝 从 {len(keyword_info)} 个关键词中选择 {count} 个进行测试...")
    
    # 按频次排序，选择前count个高频关键词
    sorted_keywords = sorted(keyword_info, key=lambda x: x["count"], reverse=True)
    selected_keywords = [item["keyword"] for item in sorted_keywords[:count]]
    
    print(f"✅ 已选择 {len(selected_keywords)} 个高频关键词")
    print(f"📊 频次范围: {sorted_keywords[0]['count']} - {sorted_keywords[count-1]['count']}")
    
    # 显示前10个关键词作为示例
    print("🔝 前10个关键词:")
    for i, keyword in enumerate(selected_keywords[:10], 1):
        freq = next(item["count"] for item in keyword_info if item["keyword"] == keyword)
        print(f"  {i:2d}. '{keyword}' (频次: {freq:,})")
    
    if len(selected_keywords) > 10:
        print(f"  ... 还有 {len(selected_keywords) - 10} 个关键词")
    
    return selected_keywords


async def check_database_before_test():
    """测试前检查数据库状态"""
    print("\n📊 测试前数据库状态:")
    pool = await get_pool()
    async with pool.acquire() as conn:
        total_count = await conn.fetchval('SELECT COUNT(*) FROM pj_similar.product_analyze_similar_result')
        print(f"  总记录数: {total_count}")
        
        # 检查目标产品的记录
        target_count = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM pj_similar.product_analyze_similar_result 
            WHERE target_product_id = $1
        """, TARGET_PRODUCT_ID)
        print(f"  目标产品 {TARGET_PRODUCT_ID} 的记录数: {target_count}")
        
        return total_count, target_count


async def check_database_after_test(initial_total: int, initial_target: int):
    """测试后检查数据库状态"""
    print("\n📊 测试后数据库状态:")
    pool = await get_pool()
    async with pool.acquire() as conn:
        final_total = await conn.fetchval('SELECT COUNT(*) FROM pj_similar.product_analyze_similar_result')
        final_target = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM pj_similar.product_analyze_similar_result 
            WHERE target_product_id = $1
        """, TARGET_PRODUCT_ID)
        
        print(f"  总记录数: {final_total} (新增: {final_total - initial_total})")
        print(f"  目标产品 {TARGET_PRODUCT_ID} 的记录数: {final_target} (新增: {final_target - initial_target})")
        
        # 如果有新增记录，显示最新的几条
        if final_total > initial_total:
            print(f"\n  最新的 {min(10, final_total - initial_total)} 条记录:")
            new_records = await conn.fetch("""
                SELECT keyword, target_product_id, avg_similarity, similar_count, 
                       competitor_count, valid_scores, created_at
                FROM pj_similar.product_analyze_similar_result 
                WHERE target_product_id = $1
                ORDER BY created_at DESC 
                LIMIT $2
            """, TARGET_PRODUCT_ID, min(10, final_total - initial_total))
            
            for i, record in enumerate(new_records, 1):
                keyword = record['keyword']
                avg_sim = record['avg_similarity']
                similar_count = record['similar_count']
                competitor_count = record['competitor_count']
                created_at = record['created_at']
                print(f"    {i:2d}. '{keyword[:40]}{'...' if len(keyword) > 40 else ''}' | "
                      f"相似度: {avg_sim}分 | 相似: {similar_count} | 竞品: {competitor_count} | "
                      f"时间: {created_at.strftime('%H:%M:%S')}")


async def execute_keyword_matching_task(task_id: int, keyword: str, target_id: int) -> Dict[str, Any]:
    """执行单个关键词匹配任务"""
    start_time = time.time()
    
    try:
        print(f"  🚀 [任务{task_id:03d}] 开始执行: '{keyword[:40]}{'...' if len(keyword) > 40 else ''}'")
        
        # 调用关键词匹配函数
        result = await keywordMatching(keyword, target_id)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result['status'] == 'success':
            data = result['data']
            from_cache = data.get('from_cache', False)
            cache_indicator = "缓存" if from_cache else "新建"
            print(f"  ✅ [任务{task_id:03d}] 成功完成 | 耗时: {duration:.2f}s | 相似度: {data['avg_similarity']}分 | {cache_indicator}")
            
            return {
                'task_id': task_id,
                'keyword': keyword,
                'status': 'success',
                'duration': duration,
                'avg_similarity': data['avg_similarity'],
                'similar_count': data['similar_count'],
                'competitor_count': data['competitor_count'],
                'valid_scores': data['valid_scores'],
                'from_cache': from_cache,
                'search_products_count': data.get('search_products_count', 0),
                'compared_products_count': data.get('compared_products_count', 0)
            }
        else:
            print(f"  ❌ [任务{task_id:03d}] 执行失败 | 耗时: {duration:.2f}s | 错误: {result.get('message', '未知错误')}")
            return {
                'task_id': task_id,
                'keyword': keyword,
                'status': 'error',
                'duration': duration,
                'error': result.get('message', '未知错误')
            }
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"  💥 [任务{task_id:03d}] 异常失败 | 耗时: {duration:.2f}s | 异常: {str(e)}")
        return {
            'task_id': task_id,
            'keyword': keyword,
            'status': 'exception',
            'duration': duration,
            'error': str(e)
        }


async def run_concurrent_batch(tasks: List[tuple], semaphore: asyncio.Semaphore) -> List[Dict[str, Any]]:
    """运行一批并发任务"""
    async def run_single_task(task_info):
        async with semaphore:
            task_id, keyword, target_id = task_info
            return await execute_keyword_matching_task(task_id, keyword, target_id)
    
    # 并发执行所有任务
    results = await asyncio.gather(*[run_single_task(task) for task in tasks], return_exceptions=True)
    
    # 处理异常结果
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            task_id, keyword, target_id = tasks[i]
            processed_results.append({
                'task_id': task_id,
                'keyword': keyword,
                'status': 'exception',
                'duration': 0,
                'error': str(result)
            })
        else:
            processed_results.append(result)
    
    return processed_results


async def run_real_keywords_stress_test():
    """运行真实关键词高并发压力测试"""
    print("🚀 真实关键词高并发压力测试开始")
    print("=" * 80)
    print(f"📊 总任务数: {TOTAL_TASKS}")
    print(f"🔄 最大并发数: {MAX_CONCURRENT}")
    print(f"🎯 目标产品ID: {TARGET_PRODUCT_ID}")
    print(f"📚 关键词来源: test_keywords.py ({len(keyword_info)} 个关键词)")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 初始化数据库连接池
        await init_pool()
        print("✅ 数据库连接池初始化完成")
        
        # 检查测试前的数据库状态
        initial_total, initial_target = await check_database_before_test()
        
        # 获取100个不同的测试关键词
        test_keywords = get_unique_test_keywords(TOTAL_TASKS)
        
        # 创建任务列表 - 每个任务使用不同的关键词
        tasks = [(i+1, test_keywords[i], TARGET_PRODUCT_ID) for i in range(TOTAL_TASKS)]
        
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(MAX_CONCURRENT)
        
        # 记录开始时间
        total_start_time = time.time()
        
        print(f"\n🔥 开始执行 {TOTAL_TASKS} 个并发任务 (每个任务使用不同关键词)...")
        print("-" * 80)
        
        # 执行所有任务
        all_results = await run_concurrent_batch(tasks, semaphore)
        
        # 记录结束时间
        total_end_time = time.time()
        total_duration = total_end_time - total_start_time
        
        print("-" * 80)
        print("🎯 所有任务执行完成！")
        
        # 统计结果
        success_results = [r for r in all_results if r['status'] == 'success']
        error_results = [r for r in all_results if r['status'] in ['error', 'exception']]
        cached_results = [r for r in success_results if r.get('from_cache', False)]
        new_results = [r for r in success_results if not r.get('from_cache', False)]
        
        success_count = len(success_results)
        error_count = len(error_results)
        cache_count = len(cached_results)
        new_count = len(new_results)
        
        # 计算性能指标
        if success_results:
            avg_duration = sum(r['duration'] for r in success_results) / len(success_results)
            avg_similarity = sum(r['avg_similarity'] for r in success_results) / len(success_results)
            total_similar_count = sum(r['similar_count'] for r in success_results)
            total_competitor_count = sum(r['competitor_count'] for r in success_results)
            
            # 计算吞吐量
            throughput = TOTAL_TASKS / total_duration
            
            # 找出最快和最慢的任务
            fastest_task = min(success_results, key=lambda x: x['duration'])
            slowest_task = max(success_results, key=lambda x: x['duration'])
        else:
            avg_duration = 0
            avg_similarity = 0
            total_similar_count = 0
            total_competitor_count = 0
            throughput = 0
            fastest_task = None
            slowest_task = None
        
        # 检查测试后的数据库状态
        await check_database_after_test(initial_total, initial_target)
        
        # 输出详细测试报告
        print("\n" + "=" * 80)
        print("📈 真实关键词高并发压力测试报告")
        print("=" * 80)
        
        # 基本统计
        print(f"🕐 总执行时间: {total_duration:.2f}秒")
        print(f"📊 任务总数: {TOTAL_TASKS}")
        print(f"✅ 成功任务: {success_count} ({success_count/TOTAL_TASKS*100:.1f}%)")
        print(f"❌ 失败任务: {error_count} ({error_count/TOTAL_TASKS*100:.1f}%)")
        print(f"🎯 缓存命中: {cache_count} ({cache_count/TOTAL_TASKS*100:.1f}%)")
        print(f"🆕 新建记录: {new_count} ({new_count/TOTAL_TASKS*100:.1f}%)")
        
        # 性能指标
        print(f"\n⚡ 性能指标:")
        print(f"  - 吞吐量: {throughput:.2f} 任务/秒")
        print(f"  - 平均响应时间: {avg_duration:.2f}秒")
        print(f"  - 最快任务: {fastest_task['duration']:.2f}秒" if fastest_task else "  - 最快任务: N/A")
        print(f"  - 最慢任务: {slowest_task['duration']:.2f}秒" if slowest_task else "  - 最慢任务: N/A")
        
        # 业务指标
        if success_results:
            print(f"\n📊 业务指标:")
            print(f"  - 平均相似度: {avg_similarity:.1f}分")
            print(f"  - 总相似产品数(>65分): {total_similar_count}")
            print(f"  - 总竞品数(>80分): {total_competitor_count}")
        
        # 错误分析
        if error_results:
            print(f"\n❌ 错误分析:")
            error_types = {}
            for result in error_results:
                error_type = result.get('error', 'Unknown')
                error_types[error_type] = error_types.get(error_type, 0) + 1
            
            for error_type, count in error_types.items():
                print(f"  - {error_type}: {count} 次")
        
        # 并发性能分析
        print(f"\n🔄 并发性能:")
        print(f"  - 配置并发数: {MAX_CONCURRENT}")
        print(f"  - 理论最小时间: {max(r['duration'] for r in all_results) if all_results else 0:.2f}秒")
        print(f"  - 实际执行时间: {total_duration:.2f}秒")
        print(f"  - 并发效率: {(max(r['duration'] for r in all_results) / total_duration * 100) if all_results and total_duration > 0 else 0:.1f}%")
        
        print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 压力测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("🔒 数据库连接池已关闭")


if __name__ == "__main__":
    asyncio.run(run_real_keywords_stress_test())
