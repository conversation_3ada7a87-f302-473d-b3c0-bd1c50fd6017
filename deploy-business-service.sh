#!/bin/bash

# 产品相似度业务服务部署脚本
# 用于将业务服务接入到现有的微服务集群

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_prerequisites() {
    log_info "检查前置条件..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 检查主服务状态
check_infrastructure() {
    log_info "检查主服务（基础设施）状态..."
    
    # 检查Consul是否可访问
    if curl -s http://localhost:8500/v1/status/leader > /dev/null; then
        log_success "Consul 服务正常"
    else
        log_error "Consul 服务不可访问，请确保主服务正在运行"
        log_info "启动主服务命令: docker-compose -f docker-compose.infrastructure.yml up -d"
        exit 1
    fi
    
    # 检查网关是否可访问
    if curl -s http://localhost/gateway/status > /dev/null; then
        log_success "网关服务正常"
    else
        log_warning "网关服务不可访问，但不影响服务注册"
    fi
}

# 检查网络
check_network() {
    log_info "检查微服务网络..."
    
    if docker network ls | grep -q "microservices"; then
        log_success "microservices 网络存在"
    else
        log_info "创建 microservices 网络..."
        docker network create microservices
        log_success "microservices 网络创建完成"
    fi
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    if [ ! -f .env ]; then
        if [ -f .env.business.example ]; then
            cp .env.business.example .env
            log_success "已复制环境变量模板到 .env"
            log_warning "请编辑 .env 文件，配置数据库连接、AI端点等信息"
            
            # 提示用户编辑配置
            read -p "是否现在编辑 .env 文件？(y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                ${EDITOR:-nano} .env
            fi
        else
            log_error ".env.business.example 文件不存在"
            exit 1
        fi
    else
        log_success ".env 文件已存在"
    fi
}

# 构建服务镜像
build_service() {
    log_info "构建产品相似度服务镜像..."
    
    if [ -f Dockerfile ]; then
        docker-compose -f docker-compose.business.yml build
        log_success "服务镜像构建完成"
    else
        log_error "Dockerfile 不存在"
        exit 1
    fi
}

# 启动业务服务
start_service() {
    log_info "启动产品相似度业务服务..."
    
    docker-compose -f docker-compose.business.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose -f docker-compose.business.yml ps | grep -q "Up"; then
        log_success "业务服务启动成功"
    else
        log_error "业务服务启动失败"
        log_info "查看日志: docker-compose -f docker-compose.business.yml logs"
        exit 1
    fi
}

# 验证服务注册
verify_registration() {
    log_info "验证服务注册..."
    
    # 等待服务注册
    sleep 5
    
    # 检查Consul中的服务注册
    if curl -s http://localhost:8500/v1/catalog/service/product-similarity | grep -q "product-similarity"; then
        log_success "服务已成功注册到 Consul"
    else
        log_warning "服务可能未注册到 Consul，请检查日志"
    fi
    
    # 检查健康状态
    if curl -s http://localhost:8000/health | grep -q "healthy"; then
        log_success "服务健康检查通过"
    else
        log_warning "服务健康检查失败，请检查服务状态"
    fi
}

# 测试网关路由
test_gateway() {
    log_info "测试网关路由..."
    
    # 测试通过网关访问服务
    if curl -s http://localhost/api/product-similarity/health | grep -q "healthy"; then
        log_success "网关路由测试通过"
    else
        log_warning "网关路由测试失败，可能需要等待consul-template更新配置"
        log_info "可以直接访问服务: http://localhost:8000"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "=== 服务访问信息 ==="
    echo "直接访问: http://localhost:8000"
    echo "网关访问: http://localhost/api/product-similarity"
    echo "健康检查: http://localhost:8000/health"
    echo "API文档: http://localhost:8000/docs"
    echo "Consul UI: http://localhost:8500"
    echo
    echo "=== 常用命令 ==="
    echo "查看服务状态: docker-compose -f docker-compose.business.yml ps"
    echo "查看服务日志: docker-compose -f docker-compose.business.yml logs -f"
    echo "停止服务: docker-compose -f docker-compose.business.yml down"
    echo "重启服务: docker-compose -f docker-compose.business.yml restart"
    echo
    echo "=== 测试命令 ==="
    echo "健康检查: curl http://localhost:8000/health"
    echo "服务信息: curl http://localhost:8000/info"
    echo "通过网关: curl http://localhost/api/product-similarity/health"
}

# 主函数
main() {
    echo "=== 产品相似度业务服务部署脚本 ==="
    echo
    
    # 检查参数
    case "${1:-deploy}" in
        "deploy")
            check_prerequisites
            check_infrastructure
            check_network
            setup_environment
            build_service
            start_service
            verify_registration
            test_gateway
            show_deployment_info
            ;;
        "stop")
            log_info "停止业务服务..."
            docker-compose -f docker-compose.business.yml down
            log_success "业务服务已停止"
            ;;
        "restart")
            log_info "重启业务服务..."
            docker-compose -f docker-compose.business.yml restart
            log_success "业务服务已重启"
            ;;
        "logs")
            docker-compose -f docker-compose.business.yml logs -f
            ;;
        "status")
            docker-compose -f docker-compose.business.yml ps
            ;;
        *)
            echo "用法: $0 [deploy|stop|restart|logs|status]"
            echo "  deploy  - 部署业务服务（默认）"
            echo "  stop    - 停止业务服务"
            echo "  restart - 重启业务服务"
            echo "  logs    - 查看服务日志"
            echo "  status  - 查看服务状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
