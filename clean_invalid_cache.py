#!/usr/bin/env python3
"""
清理数据库中相似度为0的无效缓存数据
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

async def clean_invalid_cache():
    """清理无效的缓存数据"""
    
    print("🧹 清理数据库中的无效缓存数据")
    print("=" * 60)
    
    # 数据库连接配置
    db_config = {
        'host': os.getenv('DB_HOST', '************'),
        'port': int(os.getenv('DB_PORT', 5432)),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', 'Aa123456'),
        'database': os.getenv('DB_NAME', 'postgres')
    }
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(**db_config)
        print("✅ 数据库连接成功")
        
        # 查询无效数据
        invalid_records = await conn.fetch("""
            SELECT keyword, target_product_id, avg_similarity, valid_scores, created_at
            FROM pj_similar.product_analyze_similar_result
            WHERE avg_similarity = 0 OR valid_scores = 0
            ORDER BY created_at DESC
        """)
        
        print(f"📊 找到 {len(invalid_records)} 条无效记录:")
        for record in invalid_records:
            print(f"   - {record['keyword']} -> {record['target_product_id']} (相似度: {record['avg_similarity']}, 有效评分: {record['valid_scores']})")
        
        if invalid_records:
            # 删除无效数据
            deleted_count = await conn.execute("""
                DELETE FROM pj_similar.product_analyze_similar_result
                WHERE avg_similarity = 0 OR valid_scores = 0
            """)
            
            print(f"🗑️  已删除 {deleted_count.split()[-1]} 条无效记录")
        else:
            print("✅ 没有找到无效记录")
        
        # 查询剩余有效数据
        valid_records = await conn.fetch("""
            SELECT keyword, target_product_id, avg_similarity, valid_scores, created_at
            FROM pj_similar.product_analyze_similar_result
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        print(f"\n📊 剩余有效记录 (最近10条):")
        for record in valid_records:
            print(f"   ✓ {record['keyword']} -> {record['target_product_id']} (相似度: {record['avg_similarity']}, 有效评分: {record['valid_scores']})")
        
        await conn.close()
        print("\n✅ 数据库清理完成")
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")

async def main():
    """主函数"""
    await clean_invalid_cache()

if __name__ == "__main__":
    asyncio.run(main())
