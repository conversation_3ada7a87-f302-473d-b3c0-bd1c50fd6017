#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超时和重试配置
"""

import requests
import json
import time

def test_timeout_retry_config():
    """测试超时和重试配置"""
    print("⏰ 测试超时和重试配置")
    print("=" * 60)
    
    # 测试1: 健康检查
    print("1. 健康检查")
    try:
        response = requests.get("http://localhost:8001/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 健康检查通过 - 状态: {data.get('status')}")
        else:
            print(f"   ❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
    
    # 测试2: 检查配置信息
    print("\n2. 检查服务配置")
    try:
        response = requests.get("http://localhost:8001/info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 服务信息获取成功")
            print(f"   📊 服务名称: {data.get('service_name', 'N/A')}")
            print(f"   📊 版本: {data.get('version', 'N/A')}")
        else:
            print(f"   ⚠️ 服务信息获取失败: {response.status_code}")
    except Exception as e:
        print(f"   ⚠️ 服务信息获取异常: {e}")
    
    # 测试3: 简单的产品对比测试（验证超时设置）
    print("\n3. 产品对比测试（验证超时和重试）")
    test_data = {
        "keyword": "лампа настольная",
        "target_product_id": 253486273
    }
    
    try:
        print("   🚀 开始产品对比测试...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8001/keyword-matching",
            json=test_data,
            timeout=400  # 设置客户端超时为400秒，大于服务端的360秒
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"   ⏱️ 总处理时间: {processing_time:.2f}秒")
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 产品对比测试成功")
            print(f"   📊 相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            print(f"   📊 相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
            print(f"   📊 缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
            
            # 分析处理时间
            if processing_time < 1:
                print("   💡 处理时间很短，可能命中缓存")
            elif processing_time < 60:
                print("   💡 处理时间正常，AI响应较快")
            elif processing_time < 180:
                print("   💡 处理时间较长，AI可能在深度分析")
            else:
                print("   💡 处理时间很长，可能遇到重试或网络延迟")
                
        else:
            print(f"   ❌ 产品对比测试失败")
            print(f"   📄 错误信息: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print(f"   ⏰ 客户端请求超时 (>400秒)")
        print("   💡 这表明服务端可能正在使用新的6分钟超时设置")
    except Exception as e:
        print(f"   ❌ 产品对比测试异常: {e}")
    
    # 测试4: 检查Docker日志中的重试信息
    print("\n4. 建议检查Docker日志")
    print("   💡 运行以下命令查看重试日志:")
    print("   docker logs product-similarity-server --tail 20")
    print("   🔍 查找包含'重试'、'retry'、'timeout'的日志条目")

def test_configuration_values():
    """测试配置值"""
    print("\n" + "=" * 60)
    print("📋 当前配置值验证")
    print("=" * 60)
    
    # 读取环境变量文件
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            env_content = f.read()
            
        # 查找AI_TIMEOUT配置
        for line in env_content.split('\n'):
            if line.startswith('AI_TIMEOUT='):
                timeout_value = line.split('=')[1]
                print(f"✅ AI_TIMEOUT配置: {timeout_value}秒 ({int(timeout_value)/60:.1f}分钟)")
                break
        else:
            print("⚠️ 未找到AI_TIMEOUT配置")
            
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
    
    print("\n📊 预期配置:")
    print("   • AI_TIMEOUT: 360秒 (6分钟)")
    print("   • MAX_RETRIES: 3次")
    print("   • 端点轮换: 支持多个AI端点")
    print("   • 重试状态码: 429, 500, 502, 503, 504")

if __name__ == "__main__":
    test_configuration_values()
    test_timeout_retry_config()
