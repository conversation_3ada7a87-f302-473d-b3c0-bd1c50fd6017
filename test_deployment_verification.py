#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署验证测试脚本
验证新部署的keywordMatching服务是否正常工作
"""

import asyncio
import httpx
import json
import time
from datetime import datetime

# 测试配置
API_BASE_URL = "http://localhost:8001"
GATEWAY_URL = "http://localhost/api/product-similarity"
TEST_KEYWORD = "люстра"
TARGET_PRODUCT_ID = 253486273

async def test_direct_api():
    """测试直接API访问"""
    print("🔍 测试直接API访问...")
    
    try:
        async with httpx.AsyncClient(timeout=120) as client:
            response = await client.post(
                f"{API_BASE_URL}/keyword-matching",
                json={
                    "keyword": TEST_KEYWORD,
                    "target_product_id": TARGET_PRODUCT_ID
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 直接API访问成功")
                print(f"   关键词: {data.get('data', {}).get('keyword', 'N/A')}")
                print(f"   相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
                print(f"   相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
                print(f"   缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
                return True
            else:
                print(f"❌ 直接API访问失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 直接API访问异常: {str(e)}")
        return False

async def test_gateway_api():
    """测试网关API访问"""
    print("\n🌐 测试网关API访问...")
    
    try:
        async with httpx.AsyncClient(timeout=120) as client:
            response = await client.post(
                f"{GATEWAY_URL}/keyword-matching",
                json={
                    "keyword": TEST_KEYWORD,
                    "target_product_id": TARGET_PRODUCT_ID
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 网关API访问成功")
                print(f"   关键词: {data.get('data', {}).get('keyword', 'N/A')}")
                print(f"   相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
                print(f"   相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
                print(f"   缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
                return True
            else:
                print(f"❌ 网关API访问失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 网关API访问异常: {str(e)}")
        return False

async def test_health_check():
    """测试健康检查"""
    print("\n💊 测试健康检查...")
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.get(f"{API_BASE_URL}/health")
            
            if response.status_code == 200:
                print("✅ 健康检查通过")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False

async def test_image_description_integration():
    """测试图片描述集成"""
    print("\n🖼️ 测试图片描述集成...")
    
    # 使用一个新的关键词来确保调用图片描述API
    test_keyword = "светильник потолочный"
    
    try:
        start_time = time.time()
        
        async with httpx.AsyncClient(timeout=180) as client:
            response = await client.post(
                f"{API_BASE_URL}/keyword-matching",
                json={
                    "keyword": test_keyword,
                    "target_product_id": TARGET_PRODUCT_ID
                }
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 图片描述集成测试成功")
                print(f"   处理时间: {processing_time:.2f}秒")
                print(f"   关键词: {data.get('data', {}).get('keyword', 'N/A')}")
                print(f"   相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
                
                # 长处理时间表明调用了图片描述API
                if processing_time > 30:
                    print("   ✅ 长处理时间表明图片描述API被调用")
                else:
                    print("   ⚠️ 处理时间较短，可能使用了缓存")
                    
                return True
            else:
                print(f"❌ 图片描述集成测试失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 图片描述集成测试异常: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始部署验证测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    results = []
    
    # 执行各项测试
    results.append(await test_health_check())
    results.append(await test_direct_api())
    results.append(await test_gateway_api())
    results.append(await test_image_description_integration())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    test_names = [
        "健康检查",
        "直接API访问",
        "网关API访问", 
        "图片描述集成"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！部署验证成功！")
    else:
        print("⚠️ 部分测试失败，请检查服务状态")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
