#!/usr/bin/env python3
"""
检查数据库中basket_samples表的数据
验证short_id的存储是否正确
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.db import init_pool, close_pool, get_pool
from product_similarity.logging import log_success, log_error, log_info

async def check_basket_samples():
    """检查basket_samples表中的数据"""
    log_info("检查basket_samples表中的数据...")
    
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            # 查看一些样本数据
            samples = await conn.fetch("""
                SELECT basket, short_id, created_at
                FROM pj_similar.basket_samples
                ORDER BY created_at DESC
                LIMIT 20
            """)
            
            log_info("最近的20个样本:")
            log_info("basket | short_id | created_at")
            log_info("-" * 40)
            
            for sample in samples:
                log_info(f"{sample['basket']:>6} | {sample['short_id']:>8} | {sample['created_at']}")
            
            # 检查short_id的范围分布
            ranges = await conn.fetch("""
                SELECT 
                    MIN(short_id) as min_short_id,
                    MAX(short_id) as max_short_id,
                    COUNT(*) as total_count,
                    COUNT(DISTINCT basket) as unique_baskets
                FROM pj_similar.basket_samples
            """)
            
            if ranges:
                range_info = ranges[0]
                log_info(f"\nshort_id范围统计:")
                log_info(f"  最小short_id: {range_info['min_short_id']}")
                log_info(f"  最大short_id: {range_info['max_short_id']}")
                log_info(f"  总样本数: {range_info['total_count']}")
                log_info(f"  不同basket数: {range_info['unique_baskets']}")
            
            # 检查每个basket的short_id范围
            basket_ranges = await conn.fetch("""
                SELECT 
                    basket,
                    MIN(short_id) as min_short_id,
                    MAX(short_id) as max_short_id,
                    COUNT(*) as sample_count
                FROM pj_similar.basket_samples
                GROUP BY basket
                ORDER BY basket
            """)
            
            log_info(f"\n每个basket的short_id范围:")
            log_info("basket | min_short_id | max_short_id | samples")
            log_info("-" * 50)
            
            for basket_range in basket_ranges:
                log_info(f"{basket_range['basket']:>6} | {basket_range['min_short_id']:>12} | {basket_range['max_short_id']:>12} | {basket_range['sample_count']:>7}")
            
            # 验证一些具体的计算
            log_info(f"\n验证具体产品ID的short_id计算:")
            test_products = [449105617, 313067529, 376736954]
            
            for product_id in test_products:
                calculated_short_id = product_id // 100000
                
                # 查看数据库中是否有这个short_id的记录
                db_samples = await conn.fetch("""
                    SELECT basket, short_id
                    FROM pj_similar.basket_samples
                    WHERE short_id = $1
                    LIMIT 5
                """, calculated_short_id)
                
                log_info(f"产品ID {product_id} → short_id {calculated_short_id}")
                if db_samples:
                    log_info(f"  数据库中short_id={calculated_short_id}的样本:")
                    for sample in db_samples:
                        log_info(f"    basket={sample['basket']}, short_id={sample['short_id']}")
                else:
                    log_info(f"  数据库中没有short_id={calculated_short_id}的样本")
                
    except Exception as e:
        log_error("检查数据库样本失败", error=e)

async def main():
    """主函数"""
    log_info("开始检查数据库中的basket样本数据")
    
    try:
        # 初始化数据库连接池
        await init_pool()
        
        # 检查样本数据
        await check_basket_samples()
        
        log_success("检查完成")
        
    except Exception as e:
        log_error("检查过程中发生错误", error=e)
    finally:
        # 关闭数据库连接池
        await close_pool()

if __name__ == "__main__":
    asyncio.run(main())
