"""
Pydantic数据模型定义
定义API请求和响应的数据结构
"""
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime

# ==================== 基础模型 ====================

class BaseResponse(BaseModel):
    """基础响应模型"""
    status: str = Field(default="success", description="响应状态")
    message: str = Field(default="", description="响应消息")

class ErrorResponse(BaseResponse):
    """错误响应模型"""
    status: str = Field(default="error", description="响应状态")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")

# ==================== 产品相关模型 ====================

class ProductInfoResponse(BaseResponse):
    """产品信息响应模型"""
    data: Dict[str, Any] = Field(..., description="产品信息")
    product_id: int = Field(..., description="产品ID")
    cached: bool = Field(default=False, description="是否来自缓存")

class ProductBatchRequest(BaseModel):
    """批量获取产品请求模型"""
    product_ids: List[int] = Field(..., min_items=1, max_items=100, description="产品ID列表")
    force_refresh: bool = Field(default=False, description="是否强制刷新缓存")

    @validator('product_ids')
    def validate_product_ids(cls, v):
        """验证产品ID"""
        for product_id in v:
            if not isinstance(product_id, int) or product_id <= 0:
                raise ValueError(f"无效的产品ID: {product_id}")
        return v

class ProductBatchResponse(BaseResponse):
    """批量产品信息响应模型"""
    data: Dict[int, Dict[str, Any]] = Field(..., description="产品ID到产品信息的映射")
    total_requested: int = Field(..., description="请求的产品数量")
    total_found: int = Field(..., description="找到的产品数量")

# ==================== 相似度比较相关模型 ====================

class CompareRequest(BaseModel):
    """产品比较请求模型"""
    product_id1: int = Field(..., gt=0, description="第一个产品ID")
    product_id2: int = Field(..., gt=0, description="第二个产品ID")
    mode: str = Field(default="text", pattern="^(text|multimodal|both)$", description="比较模式")
    convert: bool = Field(default=False, description="是否转换为markdown格式")

    @validator('product_id2')
    def validate_different_products(cls, v, values):
        """确保两个产品ID不同"""
        if 'product_id1' in values and v == values['product_id1']:
            raise ValueError("两个产品ID不能相同")
        return v

class SimilarityResult(BaseModel):
    """相似度结果模型"""
    similar_scores: int = Field(..., ge=1, le=100, description="相似度分数(1-100)")
    reson: str = Field(..., description="分析说明")
    comparison_mode: str = Field(..., description="比较模式")
    model_used: str = Field(..., description="使用的模型")
    cached: bool = Field(default=False, description="是否来自缓存")

class CompareResponse(BaseResponse):
    """产品比较响应模型"""
    data: SimilarityResult = Field(..., description="相似度结果")
    product_id1: int = Field(..., description="第一个产品ID")
    product_id2: int = Field(..., description="第二个产品ID")

class BatchCompareRequest(BaseModel):
    """批量比较请求模型"""
    product_pairs: List[List[int]] = Field(..., min_items=1, max_items=50, description="产品ID对列表")
    mode: str = Field(default="text", pattern="^(text|multimodal|both)$", description="比较模式")
    convert: bool = Field(default=False, description="是否转换为markdown格式")
    max_concurrent: int = Field(default=5, ge=1, le=10, description="最大并发数")

    @validator('product_pairs')
    def validate_product_pairs(cls, v):
        """验证产品ID对"""
        for pair in v:
            if len(pair) != 2:
                raise ValueError("每个产品对必须包含2个产品ID")
            if not all(isinstance(pid, int) and pid > 0 for pid in pair):
                raise ValueError("产品ID必须是正整数")
            if pair[0] == pair[1]:
                raise ValueError("产品对中的两个ID不能相同")
        return v

class BatchCompareResult(BaseModel):
    """批量比较单个结果模型"""
    product_id1: int = Field(..., description="第一个产品ID")
    product_id2: int = Field(..., description="第二个产品ID")
    success: bool = Field(..., description="比较是否成功")
    result: Optional[SimilarityResult] = Field(None, description="相似度结果")
    error: Optional[str] = Field(None, description="错误信息")

class BatchCompareResponse(BaseResponse):
    """批量比较响应模型"""
    data: List[BatchCompareResult] = Field(..., description="批量比较结果")
    total_pairs: int = Field(..., description="总比较对数")
    success_count: int = Field(..., description="成功比较数")
    failed_count: int = Field(..., description="失败比较数")

# ==================== 相似度列表相关模型 ====================

class SimilarityListItem(BaseModel):
    """相似度列表项模型"""
    product_id: int = Field(..., description="产品ID")
    similarity_score: int = Field(..., ge=1, le=100, description="相似度分数")
    comparison_mode: str = Field(..., description="比较模式")
    model_used: str = Field(..., description="使用的模型")
    reason: Optional[str] = Field(None, description="分析说明")
    created_at: Optional[str] = Field(None, description="创建时间")

class SimilarityListResponse(BaseResponse):
    """相似度列表响应模型"""
    data: List[SimilarityListItem] = Field(..., description="相似度列表")
    product_id: int = Field(..., description="查询的产品ID")
    total_count: int = Field(..., description="结果总数")

class TopSimilarityResponse(BaseResponse):
    """高相似度产品对响应模型"""
    data: List[Dict[str, Any]] = Field(..., description="高相似度产品对列表")
    total_count: int = Field(..., description="结果总数")

# ==================== 关键词匹配相关模型 ====================

class KeywordMatchingRequest(BaseModel):
    """关键词匹配请求模型"""
    keyword: str = Field(..., min_length=1, max_length=100, description="搜索关键词")
    target_product_id: int = Field(..., gt=0, description="目标产品ID")

class KeywordMatchingResult(BaseModel):
    """关键词匹配结果模型"""
    keyword: str = Field(..., description="关键词")
    target_product_id: int = Field(..., description="目标产品ID")
    avg_similarity: int = Field(..., ge=0, le=100, description="平均相似度分数")
    similar_count: int = Field(..., ge=0, description="相似数量(>65分)")
    competitor_count: int = Field(..., ge=0, description="竞品数量(>80分)")
    valid_scores: int = Field(..., ge=0, description="有效样本数量")
    created_at: datetime = Field(..., description="创建时间")
    from_cache: bool = Field(default=False, description="是否来自缓存")
    search_products_count: Optional[int] = Field(None, description="搜索到的产品数量")
    compared_products_count: Optional[int] = Field(None, description="实际比较的产品数量")

class KeywordMatchingResponse(BaseResponse):
    """关键词匹配响应模型"""
    data: KeywordMatchingResult = Field(..., description="关键词匹配结果")

# ==================== 健康检查和服务信息模型 ====================

class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="服务版本")
    checks: Dict[str, str] = Field(..., description="各组件检查结果")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="检查时间")

class ServiceInfoResponse(BaseModel):
    """服务信息响应模型"""
    service_name: str = Field(..., description="服务名称")
    service_id: str = Field(..., description="服务ID")
    address: str = Field(..., description="服务地址")
    port: int = Field(..., description="服务端口")
    tags: List[str] = Field(..., description="服务标签")
    version: str = Field(..., description="服务版本")
    environment: str = Field(..., description="运行环境")

# ==================== 统计信息模型 ====================

class DatabaseStats(BaseModel):
    """数据库统计信息模型"""
    total_products: int = Field(default=0, description="产品总数")
    products_today: int = Field(default=0, description="今日新增产品")
    products_week: int = Field(default=0, description="本周新增产品")
    products_month: int = Field(default=0, description="本月新增产品")
    total_comparisons: int = Field(default=0, description="比较总数")
    comparisons_today: int = Field(default=0, description="今日比较数")
    comparisons_week: int = Field(default=0, description="本周比较数")
    comparisons_month: int = Field(default=0, description="本月比较数")
    avg_similarity_score: Optional[float] = Field(None, description="平均相似度分数")

class StatsResponse(BaseResponse):
    """统计信息响应模型"""
    data: DatabaseStats = Field(..., description="统计数据")

# ==================== 缓存管理模型 ====================

class CacheRefreshRequest(BaseModel):
    """缓存刷新请求模型"""
    product_id: Optional[int] = Field(None, gt=0, description="产品ID（可选）")
    product_id1: Optional[int] = Field(None, gt=0, description="第一个产品ID（相似度缓存）")
    product_id2: Optional[int] = Field(None, gt=0, description="第二个产品ID（相似度缓存）")
    cache_type: str = Field(default="product", pattern="^(product|similarity|all)$", description="缓存类型")

    @validator('product_id2')
    def validate_similarity_cache_params(cls, v, values):
        """验证相似度缓存参数"""
        cache_type = values.get('cache_type')
        product_id1 = values.get('product_id1')
        
        if cache_type == 'similarity':
            if not product_id1 or not v:
                raise ValueError("相似度缓存刷新需要提供两个产品ID")
            if product_id1 == v:
                raise ValueError("两个产品ID不能相同")
        
        return v

class CacheRefreshResponse(BaseResponse):
    """缓存刷新响应模型"""
    cache_type: str = Field(..., description="缓存类型")
    refreshed_items: int = Field(default=0, description="刷新的项目数")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
