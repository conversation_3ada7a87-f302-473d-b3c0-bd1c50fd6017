#!/usr/bin/env python3
"""
简化版压力测试
先测试小规模，确保功能正常
"""

import asyncio
import aiohttp
import json
import time
import random
from test_keywords import keyword_info

async def simple_pressure_test():
    """简化版压力测试"""
    
    print("🎯 简化版压力测试")
    print("=" * 60)
    
    # 测试配置
    test_keywords_count = 20  # 先测试20个关键词
    max_concurrent = 5        # 5个并发
    target_product_id = 253486273
    
    # 随机选择关键词
    available_keywords = [item['keyword'] for item in keyword_info]
    test_keywords = random.sample(available_keywords, test_keywords_count)
    
    print(f"📊 测试参数:")
    print(f"   关键词数量: {test_keywords_count}")
    print(f"   最大并发数: {max_concurrent}")
    print(f"   目标产品ID: {target_product_id}")
    print(f"   测试关键词: {test_keywords[:5]}... (显示前5个)")
    print("=" * 60)
    
    results = []
    start_time = time.time()
    
    async def test_single_keyword(session, keyword, test_id):
        """测试单个关键词"""
        request_data = {
            "keyword": keyword,
            "target_product_id": target_product_id
        }
        
        request_start = time.time()
        
        try:
            async with session.post(
                "http://localhost:8001/keyword-matching",
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=180)
            ) as response:
                request_end = time.time()
                processing_time = request_end - request_start
                
                if response.status == 200:
                    result = await response.json()
                    data = result.get('data', {})
                    
                    print(f"✅ [{test_id:2d}] {keyword[:30]:<30} | 相似度: {data.get('avg_similarity', 0):2d} | 有效评分: {data.get('valid_scores', 0):2d} | 耗时: {processing_time:5.1f}s | 缓存: {'是' if data.get('from_cache') else '否'}")
                    
                    return {
                        'test_id': test_id,
                        'keyword': keyword,
                        'success': True,
                        'processing_time': processing_time,
                        'avg_similarity': data.get('avg_similarity', 0),
                        'valid_scores': data.get('valid_scores', 0),
                        'from_cache': data.get('from_cache', False),
                        'data_valid': data.get('avg_similarity', 0) > 0 and data.get('valid_scores', 0) > 0
                    }
                else:
                    print(f"❌ [{test_id:2d}] {keyword[:30]:<30} | 错误: HTTP {response.status}")
                    return {
                        'test_id': test_id,
                        'keyword': keyword,
                        'success': False,
                        'processing_time': processing_time,
                        'error': f"HTTP {response.status}"
                    }
                    
        except Exception as e:
            request_end = time.time()
            processing_time = request_end - request_start
            print(f"❌ [{test_id:2d}] {keyword[:30]:<30} | 异常: {str(e)[:50]}")
            return {
                'test_id': test_id,
                'keyword': keyword,
                'success': False,
                'processing_time': processing_time,
                'error': str(e)
            }
    
    # 创建会话
    async with aiohttp.ClientSession() as session:
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def controlled_test(keyword, test_id):
            async with semaphore:
                return await test_single_keyword(session, keyword, test_id)
        
        # 创建所有任务
        tasks = [
            controlled_test(keyword, i + 1)
            for i, keyword in enumerate(test_keywords)
        ]
        
        print("🚀 开始执行测试...")
        print(f"{'序号':<4} {'关键词':<30} | {'相似度':<6} | {'有效评分':<8} | {'耗时':<7} | {'缓存'}")
        print("-" * 80)
        
        # 执行所有任务
        results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 分析结果
    print("\n" + "=" * 60)
    print("📊 测试结果分析")
    print("=" * 60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    valid_data_tests = sum(1 for r in results if r.get('data_valid', False))
    cached_results = sum(1 for r in results if r.get('from_cache', False))
    
    processing_times = [r['processing_time'] for r in results if r['success']]
    avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
    
    similarities = [r.get('avg_similarity', 0) for r in results if r.get('data_valid', False)]
    avg_similarity = sum(similarities) / len(similarities) if similarities else 0
    
    print(f"📈 基础统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   成功请求: {successful_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"   有效数据: {valid_data_tests} ({valid_data_tests/total_tests*100:.1f}%)")
    print(f"   缓存命中: {cached_results} ({cached_results/total_tests*100:.1f}%)")
    
    print(f"\n⏱️  性能统计:")
    print(f"   总耗时: {total_time:.1f} 秒")
    print(f"   平均处理时间: {avg_processing_time:.1f} 秒")
    print(f"   吞吐量: {total_tests/total_time:.2f} 请求/秒")
    
    print(f"\n📊 数据质量:")
    print(f"   平均相似度: {avg_similarity:.1f}")
    
    # 判断测试结果
    if successful_tests == total_tests and valid_data_tests >= total_tests * 0.8:
        print(f"\n🎉 压力测试通过！")
        print(f"✅ 100% 成功率")
        print(f"✅ {valid_data_tests/total_tests*100:.1f}% 数据有效性")
        print(f"✅ 服务稳定性良好")
        
        # 如果小规模测试成功，建议进行大规模测试
        print(f"\n🚀 建议进行大规模测试:")
        print(f"   python batch_pressure_test.py")
        
    else:
        print(f"\n⚠️  压力测试需要关注")
        if successful_tests < total_tests:
            print(f"   - 成功率: {successful_tests/total_tests*100:.1f}%")
        if valid_data_tests < total_tests * 0.8:
            print(f"   - 数据有效性: {valid_data_tests/total_tests*100:.1f}%")
    
    # 显示失败的测试
    failed_tests = [r for r in results if not r['success']]
    if failed_tests:
        print(f"\n❌ 失败的测试:")
        for test in failed_tests[:5]:  # 只显示前5个
            print(f"   - {test['keyword']}: {test.get('error', 'Unknown error')}")

async def main():
    """主函数"""
    print("🎯 keywordMatching 简化版压力测试")
    print("📝 先测试小规模，确保功能正常")
    
    await simple_pressure_test()

if __name__ == "__main__":
    asyncio.run(main())
