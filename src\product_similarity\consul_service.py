"""
Consul服务注册和发现模块
"""
import asyncio
import socket
import uuid
from typing import Dict, Any, List, Optional
import aiohttp

from .config import settings
from .logging import log_success, log_error, log_warning, log_info

class ConsulService:
    """Consul服务管理类"""
    
    def __init__(self):
        self.consul_host = settings.CONSUL_HOST
        self.consul_port = settings.CONSUL_PORT
        self.service_name = settings.SERVICE_NAME
        self.service_port = settings.SERVICE_PORT
        self.service_tags = settings.get_service_tags_list()
        self.environment = settings.ENVIRONMENT
        
        # 生成唯一的服务ID
        self.service_id = f"{self.service_name}-{uuid.uuid4().hex[:8]}"
        
        # 获取本机IP地址
        self.service_address = self._get_local_ip()
        
        # Consul API基础URL
        self.consul_url = f"http://{self.consul_host}:{self.consul_port}"
        
        log_info(f"Consul服务配置: {self.service_name}@{self.service_address}:{self.service_port}")
    
    def _get_local_ip(self) -> str:
        """获取本机IP地址"""
        try:
            # 在Docker环境中，优先使用HOSTNAME环境变量
            import os
            hostname = os.getenv("HOSTNAME")
            if hostname:
                log_info(f"使用Docker容器hostname: {hostname}")
                return hostname

            # 创建一个UDP socket连接到外部地址来获取本机IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
            return local_ip
        except Exception:
            # 如果获取失败，使用localhost
            log_warning("无法获取本机IP地址，使用localhost")
            return "127.0.0.1"
    
    async def register(self) -> bool:
        """注册服务到Consul"""
        service_definition = {
            "ID": self.service_id,
            "Name": self.service_name,
            "Tags": self.service_tags,
            "Address": self.service_address,
            "Port": self.service_port,
            "Meta": {
                "environment": self.environment,
                "version": "1.0.0"
            },
            "Check": {
                "HTTP": f"http://{self.service_address}:{self.service_port}/health",
                "Interval": "15s",
                "Timeout": "10s",
                "DeregisterCriticalServiceAfter": "30s"
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/agent/service/register"
                async with session.put(url, json=service_definition) as response:
                    if response.status == 200:
                        log_success(f"服务注册成功: {self.service_id}")
                        return True
                    else:
                        error_text = await response.text()
                        log_error(f"服务注册失败: {response.status}, {error_text}")
                        return False
        except Exception as e:
            log_error("服务注册异常", error=e)
            return False
    
    async def deregister(self) -> bool:
        """从Consul注销服务"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/agent/service/deregister/{self.service_id}"
                async with session.put(url) as response:
                    if response.status == 200:
                        log_success(f"服务注销成功: {self.service_id}")
                        return True
                    else:
                        error_text = await response.text()
                        log_error(f"服务注销失败: {response.status}, {error_text}")
                        return False
        except Exception as e:
            log_error("服务注销异常", error=e)
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """检查Consul连接状态"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/agent/self"
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "healthy",
                            "consul_node": data.get("Config", {}).get("NodeName", "unknown"),
                            "consul_datacenter": data.get("Config", {}).get("Datacenter", "unknown")
                        }
                    else:
                        return {
                            "status": "unhealthy",
                            "error": f"HTTP {response.status}"
                        }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def discover_service(self, service_name: str) -> List[Dict[str, Any]]:
        """发现指定名称的服务实例"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/health/service/{service_name}?passing=true"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        services = []
                        for item in data:
                            service = item.get("Service", {})
                            services.append({
                                "id": service.get("ID"),
                                "name": service.get("Service"),
                                "address": service.get("Address"),
                                "port": service.get("Port"),
                                "tags": service.get("Tags", []),
                                "meta": service.get("Meta", {})
                            })
                        return services
                    else:
                        log_error(f"服务发现失败: {service_name}, HTTP {response.status}")
                        return []
        except Exception as e:
            log_error(f"服务发现异常: {service_name}", error=e)
            return []
    
    async def get_all_services(self) -> Dict[str, List[str]]:
        """获取所有注册的服务"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/agent/services"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        services = {}
                        for service_id, service_info in data.items():
                            service_name = service_info.get("Service")
                            if service_name not in services:
                                services[service_name] = []
                            services[service_name].append({
                                "id": service_id,
                                "address": service_info.get("Address"),
                                "port": service_info.get("Port"),
                                "tags": service_info.get("Tags", [])
                            })
                        return services
                    else:
                        log_error(f"获取服务列表失败: HTTP {response.status}")
                        return {}
        except Exception as e:
            log_error("获取服务列表异常", error=e)
            return {}
    
    async def update_service_meta(self, meta: Dict[str, str]) -> bool:
        """更新服务元数据"""
        try:
            # 先获取当前服务定义
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/v1/agent/service/{self.service_id}"
                async with session.get(url) as response:
                    if response.status != 200:
                        log_error("无法获取当前服务定义")
                        return False
                    
                    current_service = await response.json()
                    
                    # 更新元数据
                    current_service["Meta"].update(meta)
                    
                    # 重新注册服务
                    register_url = f"{self.consul_url}/v1/agent/service/register"
                    async with session.put(register_url, json=current_service) as reg_response:
                        if reg_response.status == 200:
                            log_success("服务元数据更新成功")
                            return True
                        else:
                            log_error(f"服务元数据更新失败: HTTP {reg_response.status}")
                            return False
        except Exception as e:
            log_error("更新服务元数据异常", error=e)
            return False
    
    async def wait_for_consul(self, max_retries: int = 30, retry_interval: float = 2.0) -> bool:
        """等待Consul可用"""
        for attempt in range(max_retries):
            health = await self.health_check()
            if health["status"] == "healthy":
                log_success("Consul连接成功")
                return True
            
            if attempt < max_retries - 1:
                log_warning(f"等待Consul可用... ({attempt + 1}/{max_retries})")
                await asyncio.sleep(retry_interval)
        
        log_error("Consul连接超时")
        return False

# 创建全局Consul服务实例
consul_service = ConsulService()
