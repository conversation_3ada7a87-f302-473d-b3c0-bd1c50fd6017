#!/usr/bin/env python3
"""
测试业务服务部署
"""
import asyncio
import httpx
import json
import time

async def test_business_service():
    """测试业务服务部署"""
    
    print("🚀 测试产品相似度业务服务部署")
    print("=" * 60)
    
    # 业务服务URL (端口8001)
    business_url = "http://localhost:8001"
    
    # 网关URL
    gateway_url = "http://localhost/api/product-similarity"
    
    async with httpx.AsyncClient(timeout=60) as client:
        
        # 1. 测试直接访问业务服务
        print("\n📊 1. 测试直接访问业务服务")
        print("-" * 40)
        
        try:
            # 健康检查
            health_response = await client.get(f"{business_url}/health")
            print(f"健康检查状态码: {health_response.status_code}")
            if health_response.status_code == 200:
                health_data = health_response.json()
                print("✅ 业务服务健康检查通过")
                print(f"服务状态: {health_data.get('status')}")
                print(f"服务名称: {health_data.get('service')}")
            else:
                print(f"❌ 业务服务健康检查失败: {health_response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到业务服务: {e}")
            return False
        
        # 2. 测试服务信息
        print("\n📋 2. 测试服务信息")
        print("-" * 40)
        try:
            info_response = await client.get(f"{business_url}/info")
            print(f"服务信息状态码: {info_response.status_code}")
            if info_response.status_code == 200:
                info_data = info_response.json()
                print("✅ 服务信息获取成功")
                print(f"服务名称: {info_data.get('service_name')}")
                print(f"服务ID: {info_data.get('service_id')}")
                print(f"地址: {info_data.get('address')}:{info_data.get('port')}")
                print(f"标签: {info_data.get('tags')}")
            else:
                print(f"❌ 服务信息获取失败: {info_response.status_code}")
        except Exception as e:
            print(f"❌ 服务信息获取异常: {e}")
        
        # 3. 测试关键词匹配API
        print("\n🎯 3. 测试关键词匹配API")
        print("-" * 40)
        
        test_data = {
            "keyword": "люстра на потолок",
            "target_product_id": 253486273
        }
        
        print(f"测试关键词: {test_data['keyword']}")
        print(f"目标产品ID: {test_data['target_product_id']}")
        
        try:
            start_time = time.time()
            response = await client.post(
                f"{business_url}/keyword-matching",
                json=test_data
            )
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"请求状态码: {response.status_code}")
            print(f"请求耗时: {duration:.2f}秒")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 关键词匹配API调用成功！")
                
                if result.get('status') == 'success':
                    data = result.get('data', {})
                    print(f"\n📈 业务结果:")
                    print(f"  关键词: {data.get('keyword')}")
                    print(f"  目标产品ID: {data.get('target_product_id')}")
                    print(f"  平均相似度: {data.get('avg_similarity')}分")
                    print(f"  相似产品数(>65分): {data.get('similar_count')}")
                    print(f"  竞品数(>80分): {data.get('competitor_count')}")
                    print(f"  有效样本数: {data.get('valid_scores')}")
                    print(f"  是否来自缓存: {data.get('from_cache')}")
                    return True
                else:
                    print(f"❌ API返回错误: {result.get('message')}")
                    return False
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ API调用异常: {e}")
            return False

async def test_consul_registration():
    """测试Consul服务注册"""
    
    print("\n" + "=" * 60)
    print("🔍 检查Consul服务注册")
    print("=" * 60)
    
    async with httpx.AsyncClient(timeout=10) as client:
        try:
            # 检查已注册的服务
            response = await client.get("http://localhost:8500/v1/catalog/services")
            if response.status_code == 200:
                services = response.json()
                print(f"📋 Consul中已注册服务 ({len(services)}个):")
                for service_name, tags in services.items():
                    print(f"  - {service_name}: {tags}")
                
                # 检查product-similarity服务详情
                if "product-similarity" in services:
                    print("\n✅ product-similarity服务已注册到Consul")
                    
                    # 获取服务实例详情
                    instances_response = await client.get(
                        "http://localhost:8500/v1/health/service/product-similarity?passing=true"
                    )
                    if instances_response.status_code == 200:
                        instances = instances_response.json()
                        print(f"📊 健康的服务实例数: {len(instances)}")
                        for instance in instances:
                            service = instance.get("Service", {})
                            print(f"  实例ID: {service.get('ID')}")
                            print(f"  地址: {service.get('Address')}:{service.get('Port')}")
                            print(f"  标签: {service.get('Tags')}")
                            print(f"  元数据: {service.get('Meta')}")
                else:
                    print("❌ product-similarity服务未在Consul中注册")
                    return False
            else:
                print(f"❌ 无法获取Consul服务列表: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Consul连接异常: {e}")
            return False
    
    return True

async def test_gateway_access():
    """测试通过网关访问"""
    
    print("\n" + "=" * 60)
    print("🌐 测试通过网关访问")
    print("=" * 60)
    
    gateway_url = "http://localhost/api/product-similarity"
    
    async with httpx.AsyncClient(timeout=60) as client:
        
        # 1. 测试网关健康检查
        print("\n📊 1. 测试网关健康检查")
        print("-" * 40)
        try:
            response = await client.get(f"{gateway_url}/health")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                print("✅ 网关访问健康检查成功")
            else:
                print(f"❌ 网关访问健康检查失败: {response.text}")
        except Exception as e:
            print(f"❌ 网关访问异常: {e}")
        
        # 2. 测试网关关键词匹配API
        print("\n🎯 2. 测试网关关键词匹配API")
        print("-" * 40)
        
        test_data = {
            "keyword": "люстра на потолок",
            "target_product_id": 253486273
        }
        
        try:
            start_time = time.time()
            response = await client.post(
                f"{gateway_url}/keyword-matching",
                json=test_data
            )
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"状态码: {response.status_code}")
            print(f"耗时: {duration:.2f}秒")
            
            if response.status_code == 200:
                print("✅ 网关关键词匹配API调用成功！")
                return True
            else:
                print(f"❌ 网关API调用失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 网关API调用异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("🧪 业务服务部署测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 测试结果
    results = []
    
    # 1. 测试业务服务
    business_success = await test_business_service()
    results.append(("业务服务直接访问", business_success))
    
    # 2. 测试Consul注册
    consul_success = await test_consul_registration()
    results.append(("Consul服务注册", consul_success))
    
    # 3. 测试网关访问
    gateway_success = await test_gateway_access()
    results.append(("网关访问", gateway_success))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    
    print(f"\n总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！业务服务部署成功！")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，请检查配置")

if __name__ == "__main__":
    asyncio.run(main())
