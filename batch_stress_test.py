"""
批量压力测试重构后的 get_product_detail 函数
使用真实产品ID进行大量测试，打印每个结果
"""
import asyncio
import sys
import os
import json
import time
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail
from test_product_ids import nm_ids

class BatchStressTest:
    def __init__(self, test_count=20, concurrent_limit=5):
        self.test_count = test_count
        self.concurrent_limit = concurrent_limit
        self.results = []
        self.success_count = 0
        self.error_count = 0
        self.start_time = None
        self.semaphore = asyncio.Semaphore(concurrent_limit)
    
    async def test_single_product(self, product_id: int, test_index: int) -> dict:
        """测试单个产品"""
        async with self.semaphore:
            print(f"\n{'='*80}")
            print(f"🧪 测试 #{test_index} - 产品ID: {product_id}")
            print(f"{'='*80}")
            
            start_time = time.time()
            result = {
                "test_index": test_index,
                "product_id": product_id,
                "start_time": datetime.now().strftime("%H:%M:%S"),
                "status": "unknown",
                "duration": 0,
                "error": None,
                "data": None
            }
            
            try:
                # 获取产品详情
                product_data = await get_product_detail(product_id, force_refresh=True)
                duration = time.time() - start_time
                
                result.update({
                    "status": "success",
                    "duration": duration,
                    "data": product_data
                })
                
                # 打印成功结果
                print(f"✅ 测试成功 - 耗时: {duration:.2f}秒")
                print(f"📦 产品信息:")
                print(f"  产品名称: {product_data.get('imt_name', 'N/A')}")
                print(f"  产品ID: {product_data.get('nm_id', 'N/A')}")
                print(f"  产品类别: {product_data.get('subj_name', 'N/A')}")
                
                # 检查图片信息
                img_urls = product_data.get('product_img_urls', [])
                desc_text = product_data.get('images_description_text', '')
                print(f"🖼️ 图片信息:")
                print(f"  图片数量: {len(img_urls)}")
                print(f"  描述长度: {len(desc_text)} 字符")
                
                if img_urls:
                    print(f"  主图URL: {img_urls[0]}")
                
                if desc_text:
                    print(f"  描述预览: {desc_text[:150]}...")
                
                # 打印完整JSON数据
                print(f"\n📋 完整产品数据:")
                print(f"{'='*60}")
                print(json.dumps(product_data, ensure_ascii=False, indent=2))
                print(f"{'='*60}")
                
                self.success_count += 1
                
            except Exception as e:
                duration = time.time() - start_time
                result.update({
                    "status": "error",
                    "duration": duration,
                    "error": str(e)
                })
                
                # 打印错误结果
                print(f"❌ 测试失败 - 耗时: {duration:.2f}秒")
                print(f"错误信息: {e}")
                
                self.error_count += 1
            
            self.results.append(result)
            return result
    
    async def run_batch_test(self):
        """运行批量测试"""
        print(f"🚀 开始批量压力测试")
        print(f"测试数量: {self.test_count}")
        print(f"并发限制: {self.concurrent_limit}")
        print(f"可用产品ID数量: {len(nm_ids)}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        self.start_time = time.time()
        
        # 选择测试的产品ID
        test_product_ids = []
        for i in range(self.test_count):
            product_id = int(nm_ids[i % len(nm_ids)])
            test_product_ids.append(product_id)
        
        # 创建并发任务
        tasks = []
        for i, product_id in enumerate(test_product_ids, 1):
            task = asyncio.create_task(
                self.test_single_product(product_id, i)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # 打印总结
        await self.print_summary()
    
    async def print_summary(self):
        """打印测试总结"""
        total_duration = time.time() - self.start_time
        
        print(f"\n{'='*80}")
        print(f"📊 批量压力测试总结")
        print(f"{'='*80}")
        print(f"总测试数量: {self.test_count}")
        print(f"成功数量: {self.success_count}")
        print(f"失败数量: {self.error_count}")
        print(f"成功率: {(self.success_count/self.test_count)*100:.1f}%")
        print(f"总耗时: {total_duration:.2f}秒")
        print(f"平均耗时: {total_duration/self.test_count:.2f}秒/个")
        
        # 统计成功测试的性能
        successful_results = [r for r in self.results if r["status"] == "success"]
        if successful_results:
            durations = [r["duration"] for r in successful_results]
            avg_duration = sum(durations) / len(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            
            print(f"\n⚡ 性能统计 (仅成功测试):")
            print(f"平均响应时间: {avg_duration:.2f}秒")
            print(f"最快响应时间: {min_duration:.2f}秒")
            print(f"最慢响应时间: {max_duration:.2f}秒")
        
        # 显示失败的测试
        failed_results = [r for r in self.results if r["status"] == "error"]
        if failed_results:
            print(f"\n❌ 失败的测试:")
            for result in failed_results:
                print(f"  测试#{result['test_index']} - 产品ID:{result['product_id']} - 错误:{result['error']}")
        
        # 显示成功测试的产品信息摘要
        if successful_results:
            print(f"\n✅ 成功测试的产品摘要:")
            for result in successful_results[:10]:  # 只显示前10个
                data = result["data"]
                name = data.get('imt_name', 'N/A')[:50]
                desc_len = len(data.get('images_description_text', ''))
                print(f"  #{result['test_index']} - {name}... (描述:{desc_len}字符, {result['duration']:.2f}秒)")
            
            if len(successful_results) > 10:
                print(f"  ... 还有 {len(successful_results) - 10} 个成功测试")
        
        print(f"{'='*80}")

async def main():
    """主函数"""
    # 可以调整这些参数
    TEST_COUNT = 15  # 测试数量
    CONCURRENT_LIMIT = 3  # 并发限制
    
    print("🔥 批量压力测试 - 重构后的 get_product_detail 函数")
    print(f"将测试 {TEST_COUNT} 个产品，并发限制 {CONCURRENT_LIMIT}")
    
    # 创建测试实例
    tester = BatchStressTest(
        test_count=TEST_COUNT,
        concurrent_limit=CONCURRENT_LIMIT
    )
    
    # 运行测试
    await tester.run_batch_test()

if __name__ == "__main__":
    asyncio.run(main())
