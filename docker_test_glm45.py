#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在Docker容器内测试glm-4.5模型
"""

import os
import json
import requests

def test_glm45_in_docker():
    print("🐳 在Docker容器内测试glm-4.5模型")
    print("=" * 50)
    
    # 获取环境变量
    endpoints_str = os.getenv('TEXT_ENDPOINTS')
    if not endpoints_str:
        print("❌ 未找到TEXT_ENDPOINTS环境变量")
        return
    
    print(f"📡 TEXT_ENDPOINTS: {endpoints_str}")
    
    try:
        endpoints = json.loads(endpoints_str)
        print(f"📊 解析到 {len(endpoints)} 个端点")
        
        # 找到glm-4.5端点
        glm45_endpoint = None
        for ep in endpoints:
            print(f"   - 模型: {ep.get('model')}, API Key: {ep.get('api_key', '')[:20]}...")
            if ep.get('model') == 'glm-4.5':
                glm45_endpoint = ep
        
        if not glm45_endpoint:
            print("❌ 未找到glm-4.5端点")
            return
        
        print(f"\n🎯 找到glm-4.5端点:")
        print(f"   URL: {glm45_endpoint['url']}")
        print(f"   Model: {glm45_endpoint['model']}")
        print(f"   API Key: {glm45_endpoint['api_key'][:20]}...")
        
        # 测试请求
        url = f"{glm45_endpoint['url']}/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {glm45_endpoint['api_key']}"
        }
        
        # 测试1: user角色 (应该成功)
        payload_user = {
            "model": glm45_endpoint['model'],
            "messages": [{"role": "user", "content": "你好，请用中文简单回答：你是什么AI模型？"}],
            "temperature": 0.1,
            "max_tokens": 100
        }

        # 测试2: system角色 (可能失败)
        payload_system = {
            "model": glm45_endpoint['model'],
            "messages": [{"role": "system", "content": "你好，请用中文简单回答：你是什么AI模型？"}],
            "temperature": 0.1,
            "max_tokens": 100
        }
        
        print(f"\n🧪 测试1: user角色消息...")
        print(f"URL: {url}")

        response = requests.post(url, headers=headers, json=payload_user, timeout=30)

        print(f"📊 HTTP状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            ai_response = result['choices'][0]['message']['content']
            print("✅ user角色测试成功!")
            print(f"🤖 AI回复: {ai_response}")
        else:
            print("❌ user角色测试失败!")
            print(f"错误内容: {response.text}")

        print(f"\n🧪 测试2: system角色消息...")

        response2 = requests.post(url, headers=headers, json=payload_system, timeout=30)

        print(f"📊 HTTP状态码: {response2.status_code}")

        if response2.status_code == 200:
            result2 = response2.json()
            ai_response2 = result2['choices'][0]['message']['content']
            print("✅ system角色测试成功!")
            print(f"🤖 AI回复: {ai_response2}")
        else:
            print("❌ system角色测试失败!")
            print(f"错误内容: {response2.text}")
            print("🎯 这可能就是400错误的原因！")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_glm45_in_docker()
