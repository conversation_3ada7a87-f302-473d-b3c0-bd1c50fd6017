#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署和验证脚本
自动化部署keywordMatching业务服务并验证功能
"""

import subprocess
import time
import requests
import json
from datetime import datetime

def run_command(command, cwd=None, timeout=60):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=cwd
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令超时"
    except Exception as e:
        return False, "", str(e)

def deploy_service():
    """部署服务"""
    print("🚀 开始部署keywordMatching业务服务")
    print("=" * 60)
    
    # 1. 停止现有服务
    print("1. 停止现有服务...")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.business.yml down", timeout=30)
    if success:
        print("   ✅ 现有服务已停止")
    else:
        print(f"   ⚠️ 停止服务警告: {stderr}")
    
    # 2. 构建新镜像
    print("\n2. 构建新镜像...")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.business.yml build --no-cache", timeout=300)
    if success:
        print("   ✅ 镜像构建成功")
    else:
        print(f"   ❌ 镜像构建失败: {stderr}")
        return False
    
    # 3. 启动服务
    print("\n3. 启动服务...")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.business.yml up -d", timeout=60)
    if success:
        print("   ✅ 服务启动成功")
    else:
        print(f"   ❌ 服务启动失败: {stderr}")
        return False
    
    # 4. 等待服务就绪
    print("\n4. 等待服务就绪...")
    for i in range(12):  # 等待最多60秒
        time.sleep(5)
        try:
            response = requests.get("http://localhost:8001/health", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ 服务就绪 (等待{(i+1)*5}秒)")
                return True
        except:
            pass
        print(f"   ⏳ 等待服务就绪... ({(i+1)*5}秒)")
    
    print("   ❌ 服务启动超时")
    return False

def verify_deployment():
    """验证部署"""
    print("\n🔍 验证部署功能")
    print("=" * 60)
    
    tests = []
    
    # 1. 健康检查
    print("1. 健康检查...")
    try:
        response = requests.get("http://localhost:8001/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 健康检查通过 - 状态: {data.get('status', 'N/A')}")
            tests.append(True)
        else:
            print(f"   ❌ 健康检查失败: {response.status_code}")
            tests.append(False)
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
        tests.append(False)
    
    # 2. keywordMatching功能
    print("\n2. keywordMatching功能...")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8001/keyword-matching",
            json={"keyword": "люстра", "target_product_id": 253486273},
            timeout=60
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            similarity = data.get('data', {}).get('avg_similarity', 0)
            print(f"   ✅ keywordMatching正常 - 相似度: {similarity}, 时间: {end_time-start_time:.2f}秒")
            tests.append(True)
        else:
            print(f"   ❌ keywordMatching失败: {response.status_code}")
            tests.append(False)
    except Exception as e:
        print(f"   ❌ keywordMatching异常: {e}")
        tests.append(False)
    
    # 3. Consul注册
    print("\n3. Consul服务注册...")
    try:
        response = requests.get("http://localhost:8500/v1/catalog/service/product-similarity", timeout=10)
        if response.status_code == 200:
            services = response.json()
            if services:
                print(f"   ✅ Consul注册正常 - 实例数: {len(services)}")
                tests.append(True)
            else:
                print("   ❌ Consul中无服务实例")
                tests.append(False)
        else:
            print(f"   ❌ Consul查询失败: {response.status_code}")
            tests.append(False)
    except Exception as e:
        print(f"   ❌ Consul检查异常: {e}")
        tests.append(False)
    
    # 4. 容器状态
    print("\n4. 容器状态...")
    success, stdout, stderr = run_command("docker ps --filter name=product-similarity-server --format '{{.Names}}\t{{.Status}}'")
    if success and "healthy" in stdout:
        print("   ✅ 容器状态健康")
        tests.append(True)
    elif success and "Up" in stdout:
        print("   ✅ 容器运行正常")
        tests.append(True)
    else:
        print(f"   ❌ 容器状态异常: {stdout}")
        tests.append(False)
    
    return tests

def main():
    """主函数"""
    print("🎯 keywordMatching业务服务部署和验证")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 部署服务
    deploy_success = deploy_service()
    
    if not deploy_success:
        print("\n❌ 部署失败，停止验证")
        return False
    
    # 验证部署
    test_results = verify_deployment()
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 部署和验证结果汇总:")
    
    test_names = ["健康检查", "keywordMatching功能", "Consul服务注册", "容器状态"]
    passed = sum(test_results)
    total = len(test_results)
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("\n🎉 部署和验证全部成功！")
        print("\n📝 服务访问信息:")
        print("   直接访问: http://localhost:8001/keyword-matching")
        print("   健康检查: http://localhost:8001/health")
        print("   Consul UI: http://localhost:8501")
        return True
    elif passed >= total * 0.75:
        print("\n⚠️ 部署基本成功，部分功能需要检查")
        return True
    else:
        print("\n❌ 部署存在严重问题，请检查日志")
        print("\n🔧 故障排查命令:")
        print("   docker-compose -f docker-compose.business.yml logs product-similarity")
        print("   docker ps")
        print("   python verify_current_deployment.py")
        return False

if __name__ == "__main__":
    main()
