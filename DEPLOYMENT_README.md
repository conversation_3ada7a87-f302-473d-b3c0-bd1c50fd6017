# keywordMatching业务服务部署指南

## 概述

本项目实现了keywordMatching业务服务，提供关键词匹配、产品相似度分析和图片描述集成功能。

## 功能特性

- ✅ **关键词匹配**: 基于Wildberries平台的产品搜索和相似度分析
- ✅ **图片描述集成**: 自动调用AI图片描述服务增强产品信息
- ✅ **数据库缓存**: PostgreSQL + Redis双重缓存机制
- ✅ **服务发现**: Consul自动服务注册和发现
- ✅ **健康监控**: 完善的健康检查和监控机制
- ✅ **网关集成**: 通过Nginx网关提供统一API访问

## 快速部署

### 1. 自动化部署（推荐）

```bash
# 一键部署和验证
python deploy_and_verify.py
```

### 2. 手动部署

```bash
# 停止现有服务
docker-compose -f docker-compose.business.yml down

# 构建并启动服务
docker-compose -f docker-compose.business.yml up --build -d

# 查看服务状态
docker-compose -f docker-compose.business.yml ps
```

## 验证部署

### 快速验证

```bash
# 完整功能验证
python verify_current_deployment.py

# 快速功能测试
python quick_test.py

# 网关访问测试
python test_gateway_access.py
```

### 手动验证

```bash
# 健康检查
curl http://localhost:8001/health

# 功能测试
curl -X POST http://localhost:8001/keyword-matching \
  -H "Content-Type: application/json" \
  -d '{"keyword": "люстра", "target_product_id": 253486273}'

# 网关访问
curl -X POST http://localhost/api/product-similarity/keyword-matching \
  -H "Content-Type: application/json" \
  -d '{"keyword": "люстра", "target_product_id": 253486273}'
```

## 配置说明

### 环境变量配置 (.env)

主要配置项：

```env
# 服务配置
SERVICE_NAME=product-similarity
SERVICE_PORT=8001
PORT=8001

# 数据库配置
PG_HOST=************
PG_PORT=5432
PG_USER=lens
PG_PASSWORD=Ls.3956573
PG_DB=lens

# Redis配置
REDIS_HOST=************
REDIS_PORT=6379
REDIS_PASSWORD=ls3956573
REDIS_DB=5

# Consul配置
CONSUL_HOST=consul
CONSUL_PORT=8500
```

### Docker Compose配置

关键配置更新：

1. **健康检查修复**: 使用Python urllib替代curl
2. **图片描述集成**: 支持AI图片描述API调用
3. **环境变量集成**: 完全使用.env文件配置
4. **网络配置**: 连接到microservices网络

## 服务访问

| 服务 | 直接访问 | 网关访问 |
|------|----------|----------|
| 健康检查 | http://localhost:8001/health | http://localhost/api/product-similarity/health |
| 关键词匹配 | http://localhost:8001/keyword-matching | http://localhost/api/product-similarity/keyword-matching |
| 服务信息 | http://localhost:8001/info | http://localhost/api/product-similarity/info |

## 监控和日志

### 查看日志

```bash
# 查看服务日志
docker-compose -f docker-compose.business.yml logs -f product-similarity

# 查看容器状态
docker ps --filter name=product-similarity-server

# 查看Consul注册状态
curl http://localhost:8500/v1/catalog/service/product-similarity
```

### 性能监控

- **处理时间**: 正常1-60秒（取决于缓存状态和图片描述调用）
- **相似度范围**: 1-100分
- **缓存命中率**: 通常>80%
- **成功率**: 目标>95%

## 故障排查

### 常见问题

1. **容器启动失败**
   ```bash
   docker logs product-similarity-server
   docker-compose -f docker-compose.business.yml down
   docker-compose -f docker-compose.business.yml up --build -d
   ```

2. **健康检查失败**
   - 检查数据库连接
   - 检查Redis连接
   - 检查Consul连接

3. **网关访问失败**
   ```bash
   docker logs nginx-gateway
   curl http://localhost:8001/health  # 直接访问测试
   ```

4. **图片描述API失败**
   ```bash
   docker logs say-img-description-server
   curl "http://localhost/api/say-img-description/describe?url=test"
   ```

### 重新部署

```bash
# 完全重新部署
docker-compose -f docker-compose.business.yml down
docker system prune -f
python deploy_and_verify.py
```

## 更新历史

### 2025-07-30
- ✅ 修复健康检查配置（Python urllib）
- ✅ 集成图片描述API功能
- ✅ 修复相似度计算字段名问题
- ✅ 添加数据验证防止0值存储
- ✅ 完善错误处理和日志记录
- ✅ 验证网关路由和服务注册
- ✅ 创建自动化部署和验证脚本

## 技术栈

- **后端**: Python 3.12, FastAPI
- **数据库**: PostgreSQL 16
- **缓存**: Redis 7
- **服务发现**: Consul 1.16
- **网关**: Nginx 1.25
- **容器化**: Docker, Docker Compose
- **AI集成**: 图片描述服务, 产品相似度分析

## 联系支持

如有问题，请检查：
1. 运行 `python verify_current_deployment.py` 进行诊断
2. 查看容器日志
3. 检查网络连接和端口占用
