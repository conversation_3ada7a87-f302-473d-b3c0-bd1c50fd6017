"""
详细测试重构后的 get_product_detail 函数
使用真实产品ID进行测试
"""
import asyncio
import sys
import os
import json
import time

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail
from product_similarity.crud import get_product_info
from test_product_ids import nm_ids

async def test_single_product_detailed(product_id: int):
    """详细测试单个产品"""
    print(f"\n{'='*80}")
    print(f"🧪 详细测试产品ID: {product_id}")
    print(f"{'='*80}")
    
    # 1. 首次请求（应该从远程获取）
    print("\n📡 第一次请求（从远程获取）...")
    start_time = time.time()
    
    try:
        result1 = await get_product_detail(product_id, force_refresh=True)
        first_request_time = time.time() - start_time
        
        print(f"✅ 第一次请求成功，耗时: {first_request_time:.2f}秒")
        
        # 检查产品基本信息
        print(f"\n📦 产品基本信息:")
        print(f"  产品名称: {result1.get('name', 'N/A')}")
        print(f"  产品品牌: {result1.get('brand', 'N/A')}")
        print(f"  产品价格: {result1.get('priceU', 'N/A')}")
        print(f"  产品ID: {result1.get('id', 'N/A')}")
        
        # 检查图片信息
        print(f"\n🖼️ 图片信息:")
        img_urls = result1.get('product_img_urls', [])
        print(f"  图片数量: {len(img_urls)}")
        if img_urls:
            print(f"  主图URL: {img_urls[0]}")
        
        # 检查图片描述
        print(f"\n📝 图片描述信息:")
        combined_desc = result1.get('images_description_text', '')
        print(f"  描述长度: {len(combined_desc)}")
        print(f"  描述预览: {combined_desc[:200]}...")
        
    except Exception as e:
        print(f"❌ 第一次请求失败: {e}")
        return False
    
    # 2. 检查数据库中是否已存储
    print(f"\n💾 检查数据库存储...")
    try:
        cached_data = await get_product_info(product_id)
        if cached_data:
            print(f"✅ 数据已存储到数据库")
            print(f"  缓存数据包含字段: {list(cached_data.keys())}")
        else:
            print(f"❌ 数据未存储到数据库")
            return False
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False
    
    # 3. 第二次请求（应该从缓存获取）
    print(f"\n🔄 第二次请求（从缓存获取）...")
    start_time = time.time()
    
    try:
        result2 = await get_product_detail(product_id, force_refresh=False)
        second_request_time = time.time() - start_time
        
        print(f"✅ 第二次请求成功，耗时: {second_request_time:.2f}秒")
        print(f"⚡ 缓存加速比: {first_request_time/second_request_time:.1f}x")
        
        # 验证两次结果是否一致
        if result1 == result2:
            print(f"✅ 两次请求结果完全一致")
        else:
            print(f"⚠️ 两次请求结果有差异")
            
    except Exception as e:
        print(f"❌ 第二次请求失败: {e}")
        return False
    
    # 4. 打印完整的合并数据（不做任何处理）
    print(f"\n📋 完整合并数据（原始JSON）:")
    print(f"{'='*80}")
    print(json.dumps(result1, ensure_ascii=False, indent=2))
    print(f"{'='*80}")
    
    return True

async def main():
    """主测试函数"""
    print("🚀 开始详细测试重构后的 get_product_detail 函数")
    print("使用真实产品ID进行测试")
    
    # 从产品ID列表中选择前3个进行测试
    test_ids = [int(nm_id) for nm_id in nm_ids[:3]]
    
    print(f"\n将测试以下产品ID: {test_ids}")
    
    success_count = 0
    total_count = len(test_ids)
    
    for product_id in test_ids:
        success = await test_single_product_detailed(product_id)
        if success:
            success_count += 1
        
        # 在测试之间添加短暂延迟
        await asyncio.sleep(1)
    
    print(f"\n{'='*80}")
    print(f"🏁 测试完成!")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有测试通过！重构后的函数工作正常！")
        print("\n✅ 验证结果:")
        print("  1. 图片描述功能正常")
        print("  2. 产品信息获取正常")
        print("  3. 数据库存储和缓存功能正常")
        print("  4. 数据合并功能正常")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    print(f"{'='*80}")

if __name__ == "__main__":
    asyncio.run(main())
