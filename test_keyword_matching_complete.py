#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试关键词匹配功能并完整打印产品信息
"""

import asyncio
import json
from src.product_similarity.services.keyword_matching import keywordMatching
from src.product_similarity.db import init_pool, close_pool
from test_product_ids import nm_ids

async def test_keyword_matching_complete():
    """测试关键词匹配功能并完整打印产品信息"""
    try:
        # 初始化数据库连接池
        await init_pool()
        print("数据库连接池初始化完成")
        
        # 使用真实的产品ID进行测试
        test_keyword = "органайзер для стола"  # 桌面整理器的俄文
        test_product_id = int(nm_ids[0])  # 使用第一个真实产品ID
        
        print(f"开始测试关键词匹配: '{test_keyword}' -> {test_product_id}")
        print("=" * 80)
        
        # 执行关键词匹配分析
        result = await keywordMatching(test_keyword, test_product_id)
        
        print("=== 关键词匹配结果 ===")
        print(f"状态: {result.get('status')}")
        
        if result.get('status') == 'success':
            data = result.get('data', {})
            print(f"关键词: {data.get('keyword')}")
            print(f"目标产品ID: {data.get('target_product_id')}")
            print(f"平均相似度: {data.get('avg_similarity')}")
            print(f"相似产品数量: {data.get('similar_count')}")
            print(f"竞品数量: {data.get('competitor_count')}")
            print(f"有效对比数量: {data.get('valid_scores')}")
            print(f"搜索产品数量: {data.get('search_products_count')}")
            print(f"对比产品数量: {data.get('compared_products_count')}")
            print(f"是否来自缓存: {data.get('from_cache')}")
            print(f"创建时间: {data.get('created_at')}")
            
            # 获取详细的产品对比信息
            detailed_results = data.get('detailed_results', [])
            print(f"\n=== 详细对比结果 (共{len(detailed_results)}个产品) ===")
            
            for i, detail in enumerate(detailed_results[:5], 1):  # 只显示前5个
                print(f"\n--- 产品 {i} ---")
                print(f"产品ID: {detail.get('product_id')}")
                print(f"相似度分数: {detail.get('similarity_score')}")
                print(f"是否相似: {detail.get('is_similar')}")
                print(f"是否竞品: {detail.get('is_competitor')}")
                
                # 获取完整的产品信息
                product_info = detail.get('product_info', {})
                if product_info:
                    print(f"产品名称: {product_info.get('imt_name', 'N/A')}")
                    print(f"产品分类: {product_info.get('subj_name', 'N/A')}")
                    print(f"产品描述: {product_info.get('description', 'N/A')[:100]}...")
                    
                    # 显示图片描述信息
                    image_descriptions = product_info.get('image_descriptions', [])
                    if image_descriptions:
                        print(f"图片描述数量: {len(image_descriptions)}")
                        for j, img_desc in enumerate(image_descriptions, 1):
                            desc_text = img_desc.get('description', 'N/A')
                            print(f"  图片{j}描述: {desc_text[:80]}...")
                    
                    # 显示产品属性
                    options = product_info.get('options', {})
                    if options:
                        print("主要属性:")
                        for key, value in list(options.items())[:3]:  # 只显示前3个属性
                            print(f"  {key}: {value}")
            
            if len(detailed_results) > 5:
                print(f"\n... 还有 {len(detailed_results) - 5} 个产品未显示")
        
        else:
            print(f"错误信息: {result.get('message')}")
        
        print("\n" + "=" * 80)
        print("完整关键词匹配结果:")
        print("=" * 80)
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("\n" + "=" * 80)
        print("数据库连接池已关闭")

async def test_keyword_matching_with_different_keywords():
    """测试不同关键词的匹配效果"""
    try:
        await init_pool()
        print("=" * 80)
        print("测试不同关键词的匹配效果")
        print("=" * 80)
        
        # 测试多个关键词
        test_keywords = [
            "органайзер для стола",
            "ящик для хранения", 
            "настольный органайзер"
        ]
        test_product_id = int(nm_ids[0])
        
        for i, keyword in enumerate(test_keywords, 1):
            print(f"\n🔍 测试关键词 {i}/{len(test_keywords)}: '{keyword}'")
            print("-" * 60)
            
            try:
                result = await keywordMatching(keyword, test_product_id)
                
                if result.get('status') == 'success':
                    data = result.get('data', {})
                    print(f"✅ 成功")
                    print(f"   平均相似度: {data.get('avg_similarity')}")
                    print(f"   相似产品数量: {data.get('similar_count')}")
                    print(f"   竞品数量: {data.get('competitor_count')}")
                    print(f"   有效对比数量: {data.get('valid_scores')}")
                    print(f"   是否来自缓存: {data.get('from_cache')}")
                    
                    # 显示前3个产品的简要信息
                    detailed_results = data.get('detailed_results', [])
                    print(f"   前3个产品:")
                    for j, detail in enumerate(detailed_results[:3], 1):
                        product_info = detail.get('product_info', {})
                        name = product_info.get('imt_name', 'N/A')
                        score = detail.get('similarity_score')
                        print(f"     {j}. {name[:40]}... (相似度: {score})")
                else:
                    print(f"❌ 失败: {result.get('message')}")
                    
            except Exception as e:
                print(f"❌ 错误: {str(e)}")
        
    except Exception as e:
        print(f"批量测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await close_pool()

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整关键词匹配测试（包含完整产品信息）")
    print("2. 多关键词对比测试")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_keyword_matching_complete())
    elif choice == "2":
        asyncio.run(test_keyword_matching_with_different_keywords())
    else:
        print("无效选择，默认运行完整测试")
        asyncio.run(test_keyword_matching_complete())
