"""
数据库模型定义
集中放置所有 DDL / DML 片段，方便 crud 调用
"""

# 创建schema
SCHEMA_SQL = """
CREATE SCHEMA IF NOT EXISTS pj_similar;
"""

# 产品信息表
PRODUCT_INFO_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS pj_similar.product_info (
    nm_id        BIGINT PRIMARY KEY,
    product_info JSONB             NOT NULL,
    created_at   TIMESTAMPTZ       DEFAULT NOW(),
    updated_at   TIMESTAMPTZ       DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_product_info_created_at 
ON pj_similar.product_info(created_at);

CREATE INDEX IF NOT EXISTS idx_product_info_updated_at 
ON pj_similar.product_info(updated_at);

-- 创建GIN索引用于JSONB查询
CREATE INDEX IF NOT EXISTS idx_product_info_jsonb 
ON pj_similar.product_info USING GIN(product_info);
"""

# 相似度结果表
SIMILARITY_RESULT_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS pj_similar.similarity_result (
    id                    BIGSERIAL PRIMARY KEY,
    product_id1           BIGINT NOT NULL,
    product_id2           BIGINT NOT NULL,
    similarity_score      INTEGER,
    comparison_mode       TEXT,
    model_used            TEXT,
    reason                TEXT,
    created_at            TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_similarity_product_id1 
ON pj_similar.similarity_result(product_id1);

CREATE INDEX IF NOT EXISTS idx_similarity_product_id2 
ON pj_similar.similarity_result(product_id2);

CREATE INDEX IF NOT EXISTS idx_similarity_score 
ON pj_similar.similarity_result(similarity_score);

CREATE INDEX IF NOT EXISTS idx_similarity_created_at 
ON pj_similar.similarity_result(created_at);

CREATE INDEX IF NOT EXISTS idx_similarity_mode
ON pj_similar.similarity_result(comparison_mode);

-- 创建唯一索引确保产品对的唯一性（不考虑顺序）
-- 先删除可能存在的重复数据
DELETE FROM pj_similar.similarity_result a USING pj_similar.similarity_result b
WHERE a.id > b.id
AND LEAST(a.product_id1, a.product_id2) = LEAST(b.product_id1, b.product_id2)
AND GREATEST(a.product_id1, a.product_id2) = GREATEST(b.product_id1, b.product_id2);

-- 然后创建唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_similarity_unique_pair
ON pj_similar.similarity_result(LEAST(product_id1, product_id2), GREATEST(product_id1, product_id2));
"""

# 缓存表（可选，用于Redis之外的持久化缓存）
CACHE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS pj_similar.cache (
    cache_key     TEXT PRIMARY KEY,
    cache_value   JSONB NOT NULL,
    expires_at    TIMESTAMPTZ,
    created_at    TIMESTAMPTZ DEFAULT NOW(),
    updated_at    TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_cache_expires_at 
ON pj_similar.cache(expires_at);

-- 创建清理过期缓存的函数
CREATE OR REPLACE FUNCTION pj_similar.cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM pj_similar.cache 
    WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
"""

# 关键词分析结果表
KEYWORD_ANALYSIS_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS pj_similar.product_analyze_similar_result (
    id                    SERIAL PRIMARY KEY,
    keyword               TEXT NOT NULL,
    target_product_id     BIGINT NOT NULL,
    avg_similarity        INTEGER NOT NULL DEFAULT 0,
    similar_count         INTEGER NOT NULL DEFAULT 0,
    competitor_count      INTEGER NOT NULL DEFAULT 0,
    valid_scores          INTEGER NOT NULL DEFAULT 0,
    created_at            TIMESTAMP DEFAULT NOW(),

    -- 确保关键词和目标产品ID的组合是唯一的
    UNIQUE (keyword, target_product_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_keyword_analysis_keyword
ON pj_similar.product_analyze_similar_result(keyword);

CREATE INDEX IF NOT EXISTS idx_keyword_analysis_target_product_id
ON pj_similar.product_analyze_similar_result(target_product_id);

CREATE INDEX IF NOT EXISTS idx_keyword_analysis_created_at
ON pj_similar.product_analyze_similar_result(created_at);

CREATE INDEX IF NOT EXISTS idx_keyword_analysis_avg_similarity
ON pj_similar.product_analyze_similar_result(avg_similarity);
"""

# 统计视图
STATISTICS_VIEW_SQL = """
-- 产品统计视图
CREATE OR REPLACE VIEW pj_similar.product_stats AS
SELECT 
    COUNT(*) as total_products,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 day') as products_today,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as products_week,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as products_month
FROM pj_similar.product_info;

-- 相似度统计视图
CREATE OR REPLACE VIEW pj_similar.similarity_stats AS
SELECT 
    COUNT(*) as total_comparisons,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 day') as comparisons_today,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as comparisons_week,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as comparisons_month,
    AVG(similarity_score) as avg_similarity_score,
    MIN(similarity_score) as min_similarity_score,
    MAX(similarity_score) as max_similarity_score,
    COUNT(DISTINCT comparison_mode) as unique_modes,
    COUNT(DISTINCT model_used) as unique_models
FROM pj_similar.similarity_result;

-- 热门产品视图（被比较次数最多的产品）
CREATE OR REPLACE VIEW pj_similar.popular_products AS
SELECT
    product_id,
    SUM(comparison_count) as comparison_count,
    AVG(avg_similarity_score) as avg_similarity_score,
    MAX(last_compared_at) as last_compared_at
FROM (
    SELECT
        product_id1 as product_id,
        COUNT(*) as comparison_count,
        AVG(similarity_score) as avg_similarity_score,
        MAX(created_at) as last_compared_at
    FROM pj_similar.similarity_result
    GROUP BY product_id1

    UNION ALL

    SELECT
        product_id2 as product_id,
        COUNT(*) as comparison_count,
        AVG(similarity_score) as avg_similarity_score,
        MAX(created_at) as last_compared_at
    FROM pj_similar.similarity_result
    GROUP BY product_id2
) combined
GROUP BY product_id
ORDER BY comparison_count DESC;
"""

# 智能Basket系统表
BASKET_SAMPLES_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS pj_similar.basket_samples (
    id BIGSERIAL PRIMARY KEY,
    basket VARCHAR(2) NOT NULL,
    short_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(basket, short_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_basket_samples_basket
ON pj_similar.basket_samples(basket);

CREATE INDEX IF NOT EXISTS idx_basket_samples_short_id
ON pj_similar.basket_samples(short_id);

CREATE INDEX IF NOT EXISTS idx_basket_samples_created_at
ON pj_similar.basket_samples(created_at);
"""

BASKET_MAPPING_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS pj_similar.basket_mapping (
    id SERIAL PRIMARY KEY,
    basket VARCHAR(2) NOT NULL UNIQUE,
    min_short_id INTEGER NOT NULL,
    max_short_id INTEGER NOT NULL,
    sample_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_basket_mapping_basket
ON pj_similar.basket_mapping(basket);

CREATE INDEX IF NOT EXISTS idx_basket_mapping_range
ON pj_similar.basket_mapping(min_short_id, max_short_id);

CREATE INDEX IF NOT EXISTS idx_basket_mapping_sample_count
ON pj_similar.basket_mapping(sample_count);
"""

# 智能Basket系统存储过程
BASKET_FUNCTIONS_SQL = """
-- 重新计算basket范围映射的存储过程
CREATE OR REPLACE FUNCTION pj_similar.recalculate_basket_ranges()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    basket_record RECORD;
BEGIN
    -- 清空现有映射
    DELETE FROM pj_similar.basket_mapping;

    -- 为每个basket重新计算范围（所有样本都是成功的）
    FOR basket_record IN
        SELECT
            basket,
            MIN(short_id) as min_short_id,
            MAX(short_id) as max_short_id,
            COUNT(*) as sample_count
        FROM pj_similar.basket_samples
        GROUP BY basket
    LOOP
        INSERT INTO pj_similar.basket_mapping (
            basket, min_short_id, max_short_id, sample_count
        ) VALUES (
            basket_record.basket,
            basket_record.min_short_id,
            basket_record.max_short_id,
            basket_record.sample_count
        );

        updated_count := updated_count + 1;
    END LOOP;

    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- 清理过期样本的存储过程
CREATE OR REPLACE FUNCTION pj_similar.cleanup_old_samples()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除90天前的样本（保留更多历史数据）
    DELETE FROM pj_similar.basket_samples
    WHERE created_at < NOW() - INTERVAL '90 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
"""

# 所有表的创建SQL列表
ALL_TABLES_SQL = [
    SCHEMA_SQL,
    PRODUCT_INFO_TABLE_SQL,
    SIMILARITY_RESULT_TABLE_SQL,
    CACHE_TABLE_SQL,
    BASKET_SAMPLES_TABLE_SQL,
    BASKET_MAPPING_TABLE_SQL,
    BASKET_FUNCTIONS_SQL,
    STATISTICS_VIEW_SQL
]

# 数据库初始化SQL
INIT_DATABASE_SQL = "\n".join(ALL_TABLES_SQL)
