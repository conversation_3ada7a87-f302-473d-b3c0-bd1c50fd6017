#!/usr/bin/env python3
"""
测试修复后的图片描述功能
验证 Docker 网络中的图片描述API调用
"""

import asyncio
import aiohttp
import json
import time

async def test_keyword_matching_with_image_description():
    """测试关键词匹配中的图片描述功能"""
    
    print("🔍 测试修复后的图片描述功能")
    print("=" * 60)
    
    # 使用一个新的关键词，确保不会命中缓存
    test_keyword = f"светодиодная лампа {int(time.time())}"
    target_product_id = 253486273
    
    request_data = {
        "keyword": test_keyword,
        "target_product_id": target_product_id
    }
    
    print(f"📝 测试关键词: {test_keyword}")
    print(f"🎯 目标产品ID: {target_product_id}")
    print("🚀 开始请求...")
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8001/keyword-matching", 
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=180)  # 增加超时时间，因为图片描述需要更长时间
            ) as response:
                end_time = time.time()
                processing_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ 请求成功")
                    print(f"⏱️  处理时间: {processing_time:.2f} 秒")
                    
                    data = result.get('data', {})
                    print(f"📊 结果摘要:")
                    print(f"   关键词: {data.get('keyword')}")
                    print(f"   平均相似度: {data.get('avg_similarity')}")
                    print(f"   相似产品数: {data.get('similar_count')}")
                    print(f"   竞争产品数: {data.get('competitor_count')}")
                    print(f"   有效评分数: {data.get('valid_scores')}")
                    print(f"   缓存状态: {'命中缓存' if data.get('from_cache') else '新请求'}")
                    
                    # 如果处理时间较长且不是缓存，说明可能调用了图片描述接口
                    if not data.get('from_cache') and processing_time > 20:
                        print("✅ 处理时间较长，很可能已调用图片描述接口")
                    elif data.get('from_cache'):
                        print("ℹ️  命中缓存，未调用图片描述接口")
                    else:
                        print("⚠️  处理时间较短，可能图片描述调用失败")
                    
                    return True
                else:
                    print(f"❌ 请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

async def check_logs():
    """提示查看日志"""
    print("\n📋 请查看Docker日志以确认图片描述API调用:")
    print("   docker logs product-similarity-server --tail 50")
    print("   docker logs say-img-description-server --tail 20")

async def test_direct_gateway_access():
    """测试通过网关直接访问图片描述API"""
    
    print("\n🌐 测试通过网关直接访问图片描述API")
    print("=" * 60)
    
    # 测试图片URL
    test_image_url = "https://basket-12.wbbasket.ru/vol1790/part179025/179025522/images/big/1.webp"
    
    print(f"📸 测试图片URL: {test_image_url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            params = {"url": test_image_url}
            async with session.get(
                "http://localhost/api/say-img-description/describe", 
                params=params,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                print(f"   状态码: {response.status}")
                
                if response.status == 200:
                    try:
                        result = await response.json()
                        description = result.get('description', 'N/A')
                        print(f"   ✅ 成功获取描述: {description[:100]}...")
                        return True
                    except:
                        text = await response.text()
                        print(f"   ✅ 成功获取文本: {text[:100]}...")
                        return True
                else:
                    error_text = await response.text()
                    print(f"   ❌ 错误响应: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

async def main():
    """主函数"""
    print("🎯 测试修复后的图片描述功能")
    print("📝 验证 Docker 网络中的图片描述API调用")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    await asyncio.sleep(5)
    
    # 测试通过网关直接访问图片描述API
    gateway_success = await test_direct_gateway_access()
    
    # 测试关键词匹配中的图片描述功能
    integration_success = await test_keyword_matching_with_image_description()
    
    # 提示查看日志
    await check_logs()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"网关图片描述API: {'✅ 正常' if gateway_success else '❌ 异常'}")
    print(f"关键词匹配集成: {'✅ 正常' if integration_success else '❌ 异常'}")
    
    if gateway_success and integration_success:
        print("\n🎉 所有测试通过！")
        print("✅ 图片描述API网关访问正常")
        print("✅ 关键词匹配集成成功")
        print("✅ Docker网络配置正确")
    else:
        print("\n❌ 部分测试失败")
        if not gateway_success:
            print("   - 网关图片描述API有问题")
        if not integration_success:
            print("   - 关键词匹配集成有问题")

if __name__ == "__main__":
    asyncio.run(main())
