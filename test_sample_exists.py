#!/usr/bin/env python3
"""
检查样本是否已存在
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail, _parse_product_url
from product_similarity.crud import get_basket_stats, add_basket_sample
from product_similarity.logging import log_info, log_error, log_success, log_warning
from product_similarity.database import get_connection

async def check_sample_exists():
    """检查特定样本是否已存在"""
    log_info("=== 检查样本是否已存在 ===")
    
    # 测试产品
    test_product_id = 159973580
    short_id = test_product_id // 100000  # 1599
    
    # 计算basket
    traditional_data = _parse_product_url(test_product_id)
    basket = traditional_data["basket"]
    
    log_info(f"产品ID: {test_product_id}")
    log_info(f"Short ID: {short_id}")
    log_info(f"Basket: {basket}")
    
    # 直接查询数据库检查样本是否存在
    async with get_connection() as conn:
        result = await conn.fetchrow(
            """
            SELECT * FROM pj_similar.basket_samples 
            WHERE basket = $1 AND short_id = $2
            """,
            basket, short_id
        )
        
        if result:
            log_info(f"✅ 样本已存在: {dict(result)}")
        else:
            log_info(f"❌ 样本不存在")
            
        # 查看所有相关样本
        all_samples = await conn.fetch(
            """
            SELECT * FROM pj_similar.basket_samples 
            WHERE basket = $1 OR short_id = $2
            ORDER BY created_at DESC
            """,
            basket, short_id
        )
        
        log_info(f"相关样本数量: {len(all_samples)}")
        for sample in all_samples:
            log_info(f"  - {dict(sample)}")

async def test_unique_sample():
    """测试一个肯定不存在的样本"""
    log_info("\n=== 测试唯一样本 ===")
    
    # 使用一个肯定不存在的组合
    unique_basket = "88"
    unique_short_id = 8888
    
    # 获取添加前统计
    before_stats = await get_basket_stats()
    before_samples = before_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"添加前样本数: {before_samples}")
    
    # 添加唯一样本
    log_info(f"添加唯一样本: basket={unique_basket}, short_id={unique_short_id}")
    success = await add_basket_sample(unique_basket, unique_short_id)
    log_info(f"添加结果: {success}")
    
    # 获取添加后统计
    after_stats = await get_basket_stats()
    after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"添加后样本数: {after_samples}")
    
    if after_samples > before_samples:
        log_success(f"✅ 唯一样本添加成功: {before_samples} → {after_samples}")
    else:
        log_warning(f"⚠️ 唯一样本添加失败: {before_samples} → {after_samples}")

async def test_product_with_unique_id():
    """测试一个使用唯一ID的产品"""
    log_info("\n=== 测试唯一ID产品 ===")
    
    # 构造一个肯定不存在的产品ID
    unique_product_id = 888800001  # short_id = 8888, 应该对应basket 42左右
    
    log_info(f"测试唯一产品ID: {unique_product_id}")
    
    # 计算其basket
    traditional_data = _parse_product_url(unique_product_id)
    basket = traditional_data["basket"]
    short_id = unique_product_id // 100000
    
    log_info(f"计算结果: short_id={short_id}, basket={basket}")
    
    # 检查样本是否已存在
    async with get_connection() as conn:
        existing = await conn.fetchrow(
            """
            SELECT * FROM pj_similar.basket_samples 
            WHERE basket = $1 AND short_id = $2
            """,
            basket, short_id
        )
        
        if existing:
            log_info(f"样本已存在: {dict(existing)}")
        else:
            log_info("样本不存在，可以用于测试")
            
            # 获取处理前统计
            before_stats = await get_basket_stats()
            before_samples = before_stats.get('sample_stats', {}).get('total_samples', 0)
            log_info(f"处理前样本数: {before_samples}")
            
            try:
                # 尝试获取产品信息（可能会失败，但应该记录样本）
                product_info = await get_product_detail(unique_product_id)
                log_info(f"产品获取结果: {'成功' if product_info else '失败'}")
                
                # 获取处理后统计
                after_stats = await get_basket_stats()
                after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
                log_info(f"处理后样本数: {after_samples}")
                
                if after_samples > before_samples:
                    log_success(f"✅ 样本已记录: {before_samples} → {after_samples}")
                else:
                    log_warning(f"⚠️ 样本未记录: {before_samples} → {after_samples}")
                    
            except Exception as e:
                log_error(f"获取产品失败: {unique_product_id}", error=e)
                
                # 即使失败也检查样本是否被记录
                after_stats = await get_basket_stats()
                after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
                log_info(f"失败后样本数: {after_samples}")

async def main():
    """主测试函数"""
    log_info("开始检查样本存在性")
    
    await check_sample_exists()
    await test_unique_sample()
    await test_product_with_unique_id()
    
    log_success("样本存在性检查完成")

if __name__ == "__main__":
    asyncio.run(main())
