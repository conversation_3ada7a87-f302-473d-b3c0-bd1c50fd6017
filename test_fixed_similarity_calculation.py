#!/usr/bin/env python3
"""
测试修复后的相似度计算功能
验证相似度不再为0，数据有效性验证正常
"""

import asyncio
import aiohttp
import json
import time

async def test_fixed_similarity():
    """测试修复后的相似度计算"""
    
    print("🔧 测试修复后的相似度计算功能")
    print("=" * 60)
    
    # 使用一个新的关键词，确保不会命中缓存
    test_keyword = f"настольная лампа офисная новая {int(time.time())}"
    target_product_id = 253486273
    
    request_data = {
        "keyword": test_keyword,
        "target_product_id": target_product_id
    }
    
    print(f"📝 测试关键词: {test_keyword}")
    print(f"🎯 目标产品ID: {target_product_id}")
    print("🚀 开始请求...")
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8001/keyword-matching", 
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=180)
            ) as response:
                end_time = time.time()
                processing_time = end_time - start_time
                
                print(f"⏱️  处理时间: {processing_time:.2f} 秒")
                print(f"📊 响应状态: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ 请求成功")
                    
                    data = result.get('data', {})
                    print(f"📊 结果详情:")
                    print(f"   关键词: {data.get('keyword')}")
                    print(f"   平均相似度: {data.get('avg_similarity')}")
                    print(f"   相似产品数: {data.get('similar_count')}")
                    print(f"   竞争产品数: {data.get('competitor_count')}")
                    print(f"   有效评分数: {data.get('valid_scores')}")
                    print(f"   缓存状态: {'命中缓存' if data.get('from_cache') else '新请求'}")
                    print(f"   搜索产品数: {data.get('search_products_count', 'N/A')}")
                    
                    # 验证数据有效性
                    avg_similarity = data.get('avg_similarity', 0)
                    valid_scores = data.get('valid_scores', 0)
                    
                    if avg_similarity > 0 and valid_scores > 0:
                        print("✅ 数据验证通过:")
                        print(f"   ✓ 平均相似度 > 0: {avg_similarity}")
                        print(f"   ✓ 有效评分数 > 0: {valid_scores}")
                        return True, data
                    else:
                        print("❌ 数据验证失败:")
                        print(f"   ✗ 平均相似度: {avg_similarity} (应该 > 0)")
                        print(f"   ✗ 有效评分数: {valid_scores} (应该 > 0)")
                        return False, data
                        
                else:
                    print(f"❌ 请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
                    return False, None
                    
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False, None

async def test_cached_result():
    """测试缓存结果的数据有效性"""
    
    print("\n🗄️  测试缓存结果")
    print("=" * 60)
    
    # 使用一个已知存在的关键词（从之前的测试）
    test_keyword = "настольная лампа офисная 1753808786"  # 从日志中看到的
    target_product_id = 253486273
    
    request_data = {
        "keyword": test_keyword,
        "target_product_id": target_product_id
    }
    
    print(f"📝 测试关键词: {test_keyword}")
    print(f"🎯 目标产品ID: {target_product_id}")
    print("🚀 开始请求...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8001/keyword-matching", 
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ 请求成功")
                    
                    data = result.get('data', {})
                    print(f"📊 缓存结果详情:")
                    print(f"   关键词: {data.get('keyword')}")
                    print(f"   平均相似度: {data.get('avg_similarity')}")
                    print(f"   相似产品数: {data.get('similar_count')}")
                    print(f"   竞争产品数: {data.get('competitor_count')}")
                    print(f"   有效评分数: {data.get('valid_scores')}")
                    print(f"   缓存状态: {'命中缓存' if data.get('from_cache') else '新请求'}")
                    
                    # 验证缓存数据有效性
                    avg_similarity = data.get('avg_similarity', 0)
                    valid_scores = data.get('valid_scores', 0)
                    
                    if avg_similarity > 0 and valid_scores > 0:
                        print("✅ 缓存数据验证通过")
                        return True, data
                    else:
                        print("❌ 缓存数据验证失败")
                        return False, data
                        
                else:
                    print(f"❌ 请求失败: {response.status}")
                    return False, None
                    
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False, None

async def main():
    """主函数"""
    print("🔧 测试修复后的相似度计算功能")
    print("📝 验证数据有效性和0值过滤")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    await asyncio.sleep(5)
    
    # 测试新请求
    new_success, new_data = await test_fixed_similarity()
    
    # 测试缓存结果
    cache_success, cache_data = await test_cached_result()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    if new_success:
        print("✅ 新请求数据有效")
        print(f"   平均相似度: {new_data.get('avg_similarity')}")
        print(f"   有效评分数: {new_data.get('valid_scores')}")
    else:
        print("❌ 新请求数据无效")
    
    if cache_success:
        print("✅ 缓存数据有效")
        print(f"   平均相似度: {cache_data.get('avg_similarity')}")
        print(f"   有效评分数: {cache_data.get('valid_scores')}")
    else:
        print("❌ 缓存数据无效")
    
    if new_success and cache_success:
        print("\n🎉 所有测试通过！")
        print("✅ 相似度计算修复成功")
        print("✅ 数据有效性验证正常")
        print("✅ 0值过滤机制工作正常")
    else:
        print("\n❌ 部分测试失败")
        print("请检查日志以了解详细错误信息")

if __name__ == "__main__":
    asyncio.run(main())
