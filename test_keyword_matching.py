#!/usr/bin/env python3
"""
测试关键词匹配功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.product_similarity.services.keyword_matching import keywordMatching
from src.product_similarity.db import init_pool, close_pool
from test_product_ids import nm_ids


async def test_keyword_matching():
    """测试关键词匹配功能"""
    try:
        # 初始化数据库连接池
        await init_pool()
        print("数据库连接池初始化完成")
        
        # 使用真实的产品ID进行测试 - 使用俄文关键词
        test_keyword = "настенный светильник для ванной"  # 浴室壁灯的俄文
        test_product_id = int(nm_ids[0])  # 使用第一个真实产品ID
        
        print(f"开始测试关键词匹配: '{test_keyword}' -> {test_product_id}")
        
        # 执行关键词匹配分析
        result = await keywordMatching(test_keyword, test_product_id)
        
        print("\n=== 测试结果 ===")
        print(f"状态: {result.get('status')}")
        
        if result.get('status') == 'success':
            data = result.get('data', {})
            print(f"关键词: {data.get('keyword')}")
            print(f"目标产品ID: {data.get('target_product_id')}")
            print(f"平均相似度: {data.get('avg_similarity')}")
            print(f"相似数量(>65分): {data.get('similar_count')}")
            print(f"竞品数量(>80分): {data.get('competitor_count')}")
            print(f"有效样本数量: {data.get('valid_scores')}")
            print(f"热度数量: {data.get('hot_count')}")
            print(f"是否来自缓存: {data.get('from_cache')}")
            print(f"搜索产品数量: {data.get('search_products_count')}")
            print(f"比较产品数量: {data.get('compared_products_count')}")
            print(f"创建时间: {data.get('created_at')}")
        else:
            print(f"错误信息: {result.get('message')}")
        
        print("\n=== 测试第二次调用（应该从缓存获取） ===")
        result2 = await keywordMatching(test_keyword, test_product_id)
        
        if result2.get('status') == 'success':
            data2 = result2.get('data', {})
            print(f"是否来自缓存: {data2.get('from_cache')}")
            print(f"平均相似度: {data2.get('avg_similarity')}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("数据库连接池已关闭")


if __name__ == "__main__":
    asyncio.run(test_keyword_matching())
