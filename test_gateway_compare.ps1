# 测试网关产品比较功能
$headers = @{
    'Content-Type' = 'application/json'
}

$body = @{
    product_id1 = 449105617
    product_id2 = 313067529
    mode = "text"
    convert = $false
} | ConvertTo-Json

Write-Host "发送请求到网关..."
Write-Host "URL: http://localhost/api/product-similarity/compare"
Write-Host "Body: $body"

try {
    $response = Invoke-WebRequest -Uri "http://localhost/api/product-similarity/compare" -Method POST -Body $body -Headers $headers -UseBasicParsing -TimeoutSec 30
    
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "响应内容:"
    Write-Host $response.Content
    
    # 解析JSON响应
    $jsonResponse = $response.Content | ConvertFrom-Json
    Write-Host "`n解析后的响应:"
    Write-Host "状态: $($jsonResponse.status)"
    Write-Host "相似度分数: $($jsonResponse.data.similar_scores)"
    Write-Host "比较原因: $($jsonResponse.data.reson)"
    Write-Host "使用模型: $($jsonResponse.data.model_used)"
    Write-Host "是否缓存: $($jsonResponse.data.cached)"
}
catch {
    Write-Host "请求失败: $($_.Exception.Message)"
    Write-Host "错误详情: $($_.Exception)"
}
