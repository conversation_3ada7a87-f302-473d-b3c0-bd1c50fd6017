# 关键词匹配分析Prompt生成器

## 概述

这个工具基于 `test_tasks.py` 和 `keywordMatching` 函数中的 `compare_products_by_ids` 函数，为前50个任务构建完整的prompt输入内容。它整合了系统提示词（system prompt）和根提示词（root prompt），为关键词匹配分析提供完整的上下文。

## 文件结构

```
├── prompt_for_keyword_matching.py  # 主要的prompt生成器
├── tasks_summary.json              # 任务摘要信息
├── generated_prompts/              # 生成的prompt文件目录
│   ├── task_01_органайзер_для_прово.txt
│   ├── task_02_держатель_для_провод.txt
│   └── ... (共50个文件)
└── README_prompt_generator.md      # 本说明文件
```

## 核心功能

### 1. 系统提示词 (System Prompt)
来自 `similarity.py` 中的 `SYSTEM_PROMPT_TEMPLATE`，包含：
- 产品相似度评分规则（1-100分）
- 评分标准（类目、标题、价格、图片描述）
- 思考过程和扣减规则
- 输出格式要求

### 2. 根提示词 (Root Prompt)
基于 `keywordMatching` 函数的工作流程，包含：
- 系统概述和工作步骤
- 核心功能模块说明
- 技术特点和使用场景
- 输出格式示例

### 3. 任务数据
处理前50个任务，主要特点：
- 目标产品ID: 303244457（线缆整理相关产品）
- 关键词类型: 俄语关键词，涉及线缆整理、桌面收纳等

## 使用方法

### 基本用法

```bash
# 显示第一个任务的prompt
python prompt_for_keyword_matching.py

# 显示指定任务的prompt（1-50）
python prompt_for_keyword_matching.py 10

# 保存所有prompt到文件
python prompt_for_keyword_matching.py save

# 导出任务摘要
python prompt_for_keyword_matching.py summary
```

### 编程接口

```python
from prompt_for_keyword_matching import generate_prompt_for_task, generate_all_prompts

# 生成单个任务的prompt
task_prompt = generate_prompt_for_task(0)  # 第一个任务
print(task_prompt["combined_prompt"])

# 生成所有任务的prompt
all_prompts = generate_all_prompts()
```

## 输出格式

每个生成的prompt包含以下部分：

1. **任务信息**
   - 任务编号 (1-50)
   - 关键词
   - 目标产品ID

2. **系统概述**
   - 关键词匹配分析系统的工作流程
   - 核心功能模块说明

3. **产品比较系统提示词**
   - 详细的评分规则和标准
   - 思考过程和扣减规则
   - 输出格式要求

## 关键词匹配分析工作流程

1. **关键词搜索**: 在Wildberries平台搜索相关产品
2. **产品筛选**: 提取前50个产品ID
3. **相似度比较**: 与目标产品进行相似度比较
4. **统计分析**: 计算平均相似度、相似产品数量等
5. **结果保存**: 保存分析结果到数据库

## 评分标准

产品相似度评分（1-100分）包含以下维度：
- **1级类目相同**: 5-10分
- **3级类目相似**: 15-25分
- **标题相似性**: 15-25分
- **价格相似性**: 10-15分
- **图片描述相似性**: 20-35分

## 输出指标

关键词匹配分析的主要输出指标：
- `avg_similarity`: 平均相似度
- `similar_count`: 相似产品数量（>65分）
- `competitor_count`: 竞品数量（>80分）
- `valid_scores`: 有效评分数量

## 示例任务

**任务1**: 
- 关键词: "органайзер для проводов"
- 目标产品ID: 303244457
- 类别: 线缆整理器

**任务10**:
- 关键词: "органайзер на стол"
- 目标产品ID: 303244457
- 类别: 桌面整理器

## 技术特点

- **异步处理**: 使用asyncio进行高效并发
- **缓存机制**: 避免重复计算
- **错误处理**: 完善的异常处理和日志
- **数据持久化**: 结果自动保存到数据库
- **并发控制**: 信号量限制并发请求数量

## 使用场景

1. **电商平台关键词优化**: 分析关键词与产品匹配度
2. **竞品分析**: 识别同类竞争产品
3. **产品定位**: 了解产品市场位置
4. **搜索引擎优化**: 优化搜索结果表现

## 注意事项

1. 所有关键词均为俄语，主要针对Wildberries平台
2. 目标产品ID固定为303244457（线缆整理相关产品）
3. 系统使用真实产品ID进行测试，不使用假数据
4. 生成的prompt文件使用UTF-8编码保存

## 文件说明

- `prompt_for_keyword_matching.py`: 主程序文件
- `tasks_summary.json`: 包含所有任务的摘要信息
- `generated_prompts/`: 包含50个独立的prompt文件
- 每个prompt文件命名格式: `task_XX_关键词前缀.txt`

## 依赖要求

- Python 3.7+
- 标准库模块: json, os, sys

无需额外安装第三方依赖包。
