#!/usr/bin/env python3
"""
最终高并发测试 - 验证图片描述功能集成
使用 test_keywords.py 中的关键词进行测试
"""

import asyncio
import aiohttp
import json
import time
from test_keywords import keyword_info

class ConcurrentKeywordTester:
    def __init__(self, target_product_id=253486273, max_concurrent=10):
        self.target_product_id = target_product_id
        self.max_concurrent = max_concurrent
        self.api_url = "http://localhost:8001/keyword-matching"
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # 统计信息
        self.total_tasks = 0
        self.completed_tasks = 0
        self.success_count = 0
        self.error_count = 0
        self.cache_hits = 0
        self.new_requests = 0
        
        # 性能统计
        self.total_processing_time = 0
        self.min_time = float('inf')
        self.max_time = 0
        
    async def test_single_keyword(self, keyword_data):
        """测试单个关键词"""
        async with self.semaphore:
            keyword = keyword_data["keyword"]
            start_time = time.time()
            
            try:
                request_data = {
                    "keyword": keyword,
                    "target_product_id": self.target_product_id
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(self.api_url, json=request_data) as response:
                        end_time = time.time()
                        processing_time = end_time - start_time
                        
                        if response.status == 200:
                            result = await response.json()
                            data = result.get('data', {})
                            
                            # 更新统计
                            self.success_count += 1
                            if data.get('from_cache'):
                                self.cache_hits += 1
                            else:
                                self.new_requests += 1
                            
                            # 更新性能统计
                            self.total_processing_time += processing_time
                            self.min_time = min(self.min_time, processing_time)
                            self.max_time = max(self.max_time, processing_time)
                            
                            return {
                                'keyword': keyword,
                                'success': True,
                                'processing_time': processing_time,
                                'avg_similarity': data.get('avg_similarity', 0),
                                'similar_count': data.get('similar_count', 0),
                                'competitor_count': data.get('competitor_count', 0),
                                'from_cache': data.get('from_cache', False),
                                'search_products_count': data.get('search_products_count', 0),
                                'compared_products_count': data.get('compared_products_count', 0)
                            }
                        else:
                            self.error_count += 1
                            return {
                                'keyword': keyword,
                                'success': False,
                                'error': f"HTTP {response.status}",
                                'processing_time': processing_time
                            }
                            
            except Exception as e:
                end_time = time.time()
                processing_time = end_time - start_time
                self.error_count += 1
                return {
                    'keyword': keyword,
                    'success': False,
                    'error': str(e),
                    'processing_time': processing_time
                }
            finally:
                self.completed_tasks += 1
    
    async def run_continuous_test(self, total_keywords=500):
        """运行持续并发测试"""
        print(f"🚀 开始高并发测试")
        print(f"📊 测试参数:")
        print(f"   - 总关键词数: {total_keywords}")
        print(f"   - 并发数: {self.max_concurrent}")
        print(f"   - 目标产品ID: {self.target_product_id}")
        print("=" * 60)
        
        # 选择关键词
        selected_keywords = keyword_info[:total_keywords]
        self.total_tasks = len(selected_keywords)
        
        start_time = time.time()
        
        # 创建任务队列
        tasks = []
        for keyword_data in selected_keywords:
            task = asyncio.create_task(self.test_single_keyword(keyword_data))
            tasks.append(task)
        
        # 监控进度
        progress_task = asyncio.create_task(self.monitor_progress())
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 停止进度监控
        progress_task.cancel()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 分析结果
        await self.analyze_results(results, total_time)
    
    async def monitor_progress(self):
        """监控测试进度"""
        try:
            while self.completed_tasks < self.total_tasks:
                await asyncio.sleep(2)
                progress = (self.completed_tasks / self.total_tasks) * 100
                print(f"📈 进度: {self.completed_tasks}/{self.total_tasks} ({progress:.1f}%) | "
                      f"成功: {self.success_count} | 失败: {self.error_count} | "
                      f"缓存: {self.cache_hits} | 新请求: {self.new_requests}")
        except asyncio.CancelledError:
            pass
    
    async def analyze_results(self, results, total_time):
        """分析测试结果"""
        print("\n" + "=" * 60)
        print("📊 测试结果分析")
        print("=" * 60)
        
        # 基本统计
        success_rate = (self.success_count / self.total_tasks) * 100
        cache_rate = (self.cache_hits / self.total_tasks) * 100 if self.total_tasks > 0 else 0
        
        print(f"⏱️  总耗时: {total_time:.2f} 秒")
        print(f"📈 成功率: {success_rate:.1f}% ({self.success_count}/{self.total_tasks})")
        print(f"❌ 失败数: {self.error_count}")
        print(f"💾 缓存命中率: {cache_rate:.1f}% ({self.cache_hits}/{self.total_tasks})")
        print(f"🆕 新请求数: {self.new_requests}")
        
        # 性能统计
        if self.success_count > 0:
            avg_time = self.total_processing_time / self.success_count
            print(f"\n⚡ 性能统计:")
            print(f"   - 平均处理时间: {avg_time:.2f} 秒")
            print(f"   - 最快处理时间: {self.min_time:.2f} 秒")
            print(f"   - 最慢处理时间: {self.max_time:.2f} 秒")
            print(f"   - 平均QPS: {self.success_count / total_time:.2f}")
        
        # 相似度分析
        successful_results = [r for r in results if isinstance(r, dict) and r.get('success')]
        if successful_results:
            similarities = [r.get('avg_similarity', 0) for r in successful_results]
            similar_counts = [r.get('similar_count', 0) for r in successful_results]
            
            print(f"\n🎯 相似度分析:")
            print(f"   - 平均相似度: {sum(similarities) / len(similarities):.1f}")
            print(f"   - 最高相似度: {max(similarities)}")
            print(f"   - 最低相似度: {min(similarities)}")
            print(f"   - 平均相似产品数: {sum(similar_counts) / len(similar_counts):.1f}")
        
        # 图片描述功能验证
        long_processing_requests = [r for r in successful_results 
                                  if not r.get('from_cache') and r.get('processing_time', 0) > 5]
        
        print(f"\n🖼️  图片描述功能验证:")
        print(f"   - 长处理时间请求数: {len(long_processing_requests)}")
        print(f"   - 新请求中长处理时间比例: {len(long_processing_requests) / max(self.new_requests, 1) * 100:.1f}%")
        
        if len(long_processing_requests) > 0:
            print("   ✅ 检测到长处理时间请求，图片描述功能可能已集成")
        else:
            print("   ⚠️  未检测到明显的图片描述处理时间")

async def main():
    """主函数"""
    print("🎯 最终高并发测试 - 验证图片描述功能集成")
    print("📝 使用 test_keywords.py 中的真实关键词")
    print("=" * 60)
    
    # 创建测试器
    tester = ConcurrentKeywordTester(
        target_product_id=253486273,
        max_concurrent=10  # 10个并发任务
    )
    
    # 运行测试
    await tester.run_continuous_test()  # 使用默认的500个关键词
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
