#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终部署测试
"""

import requests
import json
import time

def test_final_deployment():
    """最终部署测试"""
    print("🎯 最终部署测试")
    print("=" * 50)
    
    # 测试数据
    test_data = {
        "keyword": "лампа",
        "target_product_id": 253486273
    }
    
    # 测试1: 健康检查
    print("1. 健康检查")
    try:
        response = requests.get("http://localhost:8001/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 健康检查通过 - 状态: {data.get('status')}")
        else:
            print(f"   ❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
    
    # 测试2: 直接访问keywordMatching
    print("\n2. 直接访问keywordMatching")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8001/keyword-matching",
            json=test_data,
            timeout=60
        )
        end_time = time.time()
        
        print(f"   状态码: {response.status_code}")
        print(f"   处理时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 直接访问成功")
            print(f"   📊 相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            print(f"   📊 相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
            print(f"   📊 缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
        else:
            print(f"   ❌ 直接访问失败")
            print(f"   📄 错误信息: {response.text[:200]}...")
    except Exception as e:
        print(f"   ❌ 直接访问异常: {e}")
    
    # 测试3: 网关访问
    print("\n3. 网关访问测试")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost/api/product-similarity/keyword-matching",
            json=test_data,
            timeout=60
        )
        end_time = time.time()
        
        print(f"   状态码: {response.status_code}")
        print(f"   处理时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 网关访问成功")
            print(f"   📊 相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            print(f"   📊 相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
            print(f"   📊 缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
        else:
            print(f"   ❌ 网关访问失败")
            print(f"   📄 错误信息: {response.text[:200]}...")
    except Exception as e:
        print(f"   ❌ 网关访问异常: {e}")
    
    # 测试4: Consul服务注册
    print("\n4. Consul服务注册检查")
    try:
        response = requests.get("http://localhost:8500/v1/catalog/service/product-similarity", timeout=10)
        if response.status_code == 200:
            services = response.json()
            if services:
                print(f"   ✅ Consul注册正常 - 实例数: {len(services)}")
                service = services[0]
                print(f"   📍 服务地址: {service.get('ServiceAddress')}:{service.get('ServicePort')}")
            else:
                print("   ❌ Consul中无服务实例")
        else:
            print(f"   ❌ Consul查询失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Consul检查异常: {e}")

if __name__ == "__main__":
    test_final_deployment()
