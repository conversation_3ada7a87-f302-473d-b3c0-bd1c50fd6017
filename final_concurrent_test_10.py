#!/usr/bin/env python3
"""
最终高并发测试 - 10个并发任务
使用 test_keywords.py 中的关键词与产品 ID 253486273 进行匹配
当任务结束时，其他任务补上，保持 10 个任务进行
"""

import asyncio
import aiohttp
import json
import time
from typing import List, Dict, Any
import random

# 导入关键词数据
try:
    from test_keywords import keyword_info
    print(f"✅ 成功加载 {len(keyword_info)} 个关键词")
except ImportError:
    print("❌ 无法导入 test_keywords.py")
    exit(1)

class ConcurrentTester:
    def __init__(self, max_concurrent: int = 10, target_product_id: int = 253486273):
        self.max_concurrent = max_concurrent
        self.target_product_id = target_product_id
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # 统计数据
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.cache_hits = 0
        self.new_requests = 0
        self.total_processing_time = 0
        self.results = []
        
        # 关键词队列
        self.keyword_queue = asyncio.Queue()
        self.completed_keywords = set()
        
    async def setup_keyword_queue(self, total_keywords: int = 500):
        """设置关键词队列"""
        # 随机选择关键词，避免重复
        selected_keywords = random.sample(keyword_info, min(total_keywords, len(keyword_info)))
        
        for kw_info in selected_keywords:
            await self.keyword_queue.put(kw_info["keyword"])
        
        print(f"📝 已准备 {self.keyword_queue.qsize()} 个关键词进行测试")
    
    async def test_single_keyword(self, session: aiohttp.ClientSession, keyword: str) -> Dict[str, Any]:
        """测试单个关键词"""
        async with self.semaphore:
            start_time = time.time()
            
            request_data = {
                "keyword": keyword,
                "target_product_id": self.target_product_id
            }
            
            try:
                async with session.post(
                    "http://localhost:8001/keyword-matching",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    self.total_requests += 1
                    self.total_processing_time += processing_time
                    
                    if response.status == 200:
                        result = await response.json()
                        data = result.get('data', {})
                        
                        self.successful_requests += 1
                        
                        if data.get('from_cache', False):
                            self.cache_hits += 1
                        else:
                            self.new_requests += 1
                        
                        # 记录结果
                        result_info = {
                            'keyword': keyword,
                            'processing_time': processing_time,
                            'avg_similarity': data.get('avg_similarity', 0),
                            'similar_count': data.get('similar_count', 0),
                            'competitor_count': data.get('competitor_count', 0),
                            'valid_scores': data.get('valid_scores', 0),
                            'from_cache': data.get('from_cache', False),
                            'status': 'success'
                        }
                        
                        self.results.append(result_info)
                        self.completed_keywords.add(keyword)
                        
                        return result_info
                    else:
                        self.failed_requests += 1
                        error_text = await response.text()
                        print(f"❌ 关键词 '{keyword}' 请求失败: {response.status} - {error_text}")
                        return {
                            'keyword': keyword,
                            'processing_time': processing_time,
                            'status': 'failed',
                            'error': f"HTTP {response.status}"
                        }
                        
            except asyncio.TimeoutError:
                self.failed_requests += 1
                print(f"⏰ 关键词 '{keyword}' 请求超时")
                return {
                    'keyword': keyword,
                    'processing_time': time.time() - start_time,
                    'status': 'timeout'
                }
            except Exception as e:
                self.failed_requests += 1
                print(f"❌ 关键词 '{keyword}' 请求异常: {e}")
                return {
                    'keyword': keyword,
                    'processing_time': time.time() - start_time,
                    'status': 'error',
                    'error': str(e)
                }
    
    async def worker(self, session: aiohttp.ClientSession, worker_id: int):
        """工作协程 - 持续处理关键词队列"""
        print(f"🚀 工作线程 {worker_id} 启动")
        
        while True:
            try:
                # 从队列获取关键词，如果队列为空则退出
                keyword = await asyncio.wait_for(self.keyword_queue.get(), timeout=1.0)
                
                print(f"🔄 工作线程 {worker_id} 处理关键词: {keyword}")
                
                # 处理关键词
                result = await self.test_single_keyword(session, keyword)
                
                # 标记任务完成
                self.keyword_queue.task_done()
                
                # 显示进度
                progress = len(self.completed_keywords)
                total = self.total_requests
                success_rate = (self.successful_requests / total * 100) if total > 0 else 0
                cache_rate = (self.cache_hits / total * 100) if total > 0 else 0
                
                print(f"📊 进度: {progress} | 成功率: {success_rate:.1f}% | 缓存率: {cache_rate:.1f}% | 工作线程: {worker_id}")
                
            except asyncio.TimeoutError:
                # 队列为空，退出工作线程
                print(f"✅ 工作线程 {worker_id} 完成任务")
                break
            except Exception as e:
                print(f"❌ 工作线程 {worker_id} 异常: {e}")
                break
    
    async def run_concurrent_test(self, total_keywords: int = 500):
        """运行并发测试"""
        print("🎯 开始高并发测试")
        print(f"📋 配置: {self.max_concurrent} 个并发任务, {total_keywords} 个关键词")
        print("=" * 80)
        
        # 设置关键词队列
        await self.setup_keyword_queue(total_keywords)
        
        start_time = time.time()
        
        # 创建 HTTP 会话
        async with aiohttp.ClientSession() as session:
            # 创建工作协程
            workers = [
                self.worker(session, i+1) 
                for i in range(self.max_concurrent)
            ]
            
            # 等待所有工作协程完成
            await asyncio.gather(*workers)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 显示最终结果
        self.print_final_results(total_time)
    
    def print_final_results(self, total_time: float):
        """打印最终结果"""
        print("\n" + "=" * 80)
        print("📊 高并发测试结果")
        print("=" * 80)
        
        print(f"⏱️  总耗时: {total_time:.2f} 秒")
        print(f"📝 总请求数: {self.total_requests}")
        print(f"✅ 成功请求: {self.successful_requests}")
        print(f"❌ 失败请求: {self.failed_requests}")
        print(f"📈 成功率: {(self.successful_requests / self.total_requests * 100):.1f}%")
        
        print(f"\n🔄 缓存统计:")
        print(f"💾 缓存命中: {self.cache_hits}")
        print(f"🆕 新请求: {self.new_requests}")
        print(f"📊 缓存命中率: {(self.cache_hits / self.total_requests * 100):.1f}%")
        
        if self.successful_requests > 0:
            avg_processing_time = self.total_processing_time / self.successful_requests
            print(f"\n⚡ 性能统计:")
            print(f"平均处理时间: {avg_processing_time:.2f} 秒")
            print(f"请求吞吐量: {self.successful_requests / total_time:.2f} 请求/秒")
        
        # 相似度统计
        if self.results:
            similarities = [r['avg_similarity'] for r in self.results if r.get('avg_similarity', 0) > 0]
            if similarities:
                print(f"\n🎯 相似度统计:")
                print(f"平均相似度: {sum(similarities) / len(similarities):.1f}")
                print(f"最高相似度: {max(similarities)}")
                print(f"最低相似度: {min(similarities)}")
        
        print(f"\n🏆 完成关键词数: {len(self.completed_keywords)}")

async def main():
    """主函数"""
    print("🚀 最终高并发测试 - 10个并发任务")
    print("📝 使用 test_keywords.py 关键词与产品 ID 253486273 匹配")
    print("🔄 当任务结束时，其他任务补上，保持 10 个任务进行")
    print("=" * 80)
    
    # 创建测试器
    tester = ConcurrentTester(max_concurrent=10, target_product_id=253486273)
    
    # 运行测试 (测试 100 个关键词)
    await tester.run_concurrent_test(total_keywords=100)

if __name__ == "__main__":
    asyncio.run(main())
