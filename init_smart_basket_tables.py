#!/usr/bin/env python3
"""
初始化智能Basket系统数据库表
创建basket_samples和basket_mapping表及相关存储过程
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.db import init_pool, close_pool, get_pool
from product_similarity.logging import log_success, log_error, log_info

# 简化的表创建SQL（无置信度）
BASKET_SAMPLES_SQL = """
CREATE TABLE IF NOT EXISTS pj_similar.basket_samples (
    id BIGSERIAL PRIMARY KEY,
    basket VARCHAR(2) NOT NULL,
    short_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(basket, short_id)
);

CREATE INDEX IF NOT EXISTS idx_basket_samples_basket ON pj_similar.basket_samples(basket);
CREATE INDEX IF NOT EXISTS idx_basket_samples_short_id ON pj_similar.basket_samples(short_id);
CREATE INDEX IF NOT EXISTS idx_basket_samples_created_at ON pj_similar.basket_samples(created_at);
"""

BASKET_MAPPING_SQL = """
CREATE TABLE IF NOT EXISTS pj_similar.basket_mapping (
    id SERIAL PRIMARY KEY,
    basket VARCHAR(2) NOT NULL UNIQUE,
    min_short_id INTEGER NOT NULL,
    max_short_id INTEGER NOT NULL,
    sample_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_basket_mapping_basket ON pj_similar.basket_mapping(basket);
CREATE INDEX IF NOT EXISTS idx_basket_mapping_range ON pj_similar.basket_mapping(min_short_id, max_short_id);
CREATE INDEX IF NOT EXISTS idx_basket_mapping_sample_count ON pj_similar.basket_mapping(sample_count);
"""

BASKET_FUNCTIONS_SQL = """
-- 重新计算basket范围映射的存储过程（简化版，无置信度）
CREATE OR REPLACE FUNCTION pj_similar.recalculate_basket_ranges()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    basket_record RECORD;
BEGIN
    -- 清空现有映射
    DELETE FROM pj_similar.basket_mapping;
    
    -- 为每个basket重新计算范围（所有样本都是成功的）
    FOR basket_record IN 
        SELECT 
            basket,
            MIN(short_id) as min_short_id,
            MAX(short_id) as max_short_id,
            COUNT(*) as sample_count
        FROM pj_similar.basket_samples 
        GROUP BY basket
    LOOP
        INSERT INTO pj_similar.basket_mapping (
            basket, min_short_id, max_short_id, sample_count
        ) VALUES (
            basket_record.basket,
            basket_record.min_short_id,
            basket_record.max_short_id,
            basket_record.sample_count
        );
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- 清理过期样本的存储过程（简化版，只清理超过90天的样本）
CREATE OR REPLACE FUNCTION pj_similar.cleanup_old_samples()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除90天前的样本（保留更多历史数据）
    DELETE FROM pj_similar.basket_samples 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
"""

async def create_schema():
    """创建schema"""
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            await conn.execute("CREATE SCHEMA IF NOT EXISTS pj_similar")
            log_success("Schema创建成功")
    except Exception as e:
        log_error("Schema创建失败", error=e)
        raise

async def create_basket_tables():
    """创建basket相关表"""
    log_info("开始创建智能Basket系统表...")
    
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            # 创建basket_samples表
            await conn.execute(BASKET_SAMPLES_SQL)
            log_success("basket_samples表创建成功")
            
            # 创建basket_mapping表
            await conn.execute(BASKET_MAPPING_SQL)
            log_success("basket_mapping表创建成功")
            
            # 创建存储过程
            await conn.execute(BASKET_FUNCTIONS_SQL)
            log_success("存储过程创建成功")
            
    except Exception as e:
        log_error("创建表失败", error=e)
        raise

async def verify_tables():
    """验证表是否创建成功"""
    log_info("验证表结构...")
    
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            # 检查表是否存在
            tables = await conn.fetch("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'pj_similar' 
                AND table_name IN ('basket_samples', 'basket_mapping')
                ORDER BY table_name
            """)
            
            table_names = [row['table_name'] for row in tables]
            log_info(f"已创建的表: {table_names}")
            
            if 'basket_samples' in table_names and 'basket_mapping' in table_names:
                log_success("所有表创建成功")
                
                # 测试存储过程
                result = await conn.fetchval("SELECT pj_similar.recalculate_basket_ranges()")
                log_info(f"测试重新计算范围: {result}")
                
                result = await conn.fetchval("SELECT pj_similar.cleanup_old_samples()")
                log_info(f"测试清理过期样本: {result}")
                
                return True
            else:
                log_error("表创建不完整")
                return False
                
    except Exception as e:
        log_error("验证表失败", error=e)
        return False

async def main():
    """主函数"""
    log_info("开始初始化智能Basket系统数据库表")
    
    try:
        # 初始化数据库连接池
        await init_pool()
        
        # 创建schema
        await create_schema()
        
        # 创建表
        await create_basket_tables()
        
        # 验证表
        success = await verify_tables()
        
        if success:
            log_success("智能Basket系统数据库初始化完成")
        else:
            log_error("智能Basket系统数据库初始化失败")
            
    except Exception as e:
        log_error("初始化过程中发生错误", error=e)
    finally:
        # 关闭数据库连接池
        await close_pool()

if __name__ == "__main__":
    asyncio.run(main())
