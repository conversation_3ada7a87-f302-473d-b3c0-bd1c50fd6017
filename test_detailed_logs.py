#!/usr/bin/env python3
"""
测试详细日志记录
验证每个任务给到大模型的两个产品的product_info内容是否在日志中显示
"""

import asyncio
import aiohttp
import json
import time

async def test_detailed_logging():
    """测试详细日志记录"""
    
    print("🔍 测试详细日志记录功能")
    print("=" * 60)
    
    # 测试关键词和目标产品
    test_keyword = "настольная лампа"
    target_product_id = 253486273
    
    request_data = {
        "keyword": test_keyword,
        "target_product_id": target_product_id
    }
    
    print(f"📝 测试关键词: {test_keyword}")
    print(f"🎯 目标产品ID: {target_product_id}")
    print("🚀 开始请求...")
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8001/keyword-matching", 
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=120)
            ) as response:
                end_time = time.time()
                processing_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ 请求成功")
                    print(f"⏱️  处理时间: {processing_time:.2f} 秒")
                    
                    data = result.get('data', {})
                    print(f"📊 结果摘要:")
                    print(f"   关键词: {data.get('keyword')}")
                    print(f"   平均相似度: {data.get('avg_similarity')}")
                    print(f"   相似产品数: {data.get('similar_count')}")
                    print(f"   竞争产品数: {data.get('competitor_count')}")
                    print(f"   有效评分数: {data.get('valid_scores')}")
                    print(f"   缓存状态: {'命中缓存' if data.get('from_cache') else '新请求'}")
                    
                    print("\n📋 请求完成，请查看 Docker 日志以查看详细的产品信息:")
                    print("   docker logs product-similarity-server --tail 100")
                    
                    return True
                else:
                    print(f"❌ 请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

async def main():
    """主函数"""
    print("🎯 详细日志记录测试")
    print("📝 验证每个任务给到大模型的两个产品的product_info内容")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    await asyncio.sleep(5)
    
    # 测试详细日志记录
    success = await test_detailed_logging()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！")
        print("📋 请查看 Docker 日志以查看详细的产品信息:")
        print("   docker logs product-similarity-server --tail 100")
        print("\n🔍 日志中应该包含:")
        print("   🎯 [目标产品] 目标产品信息")
        print("   🔍 [产品比较] 比较产品信息")
        print("   ⚖️  [相似度比较] 比较结果")
        print("   📝 完整的 product_info JSON 内容")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    asyncio.run(main())
