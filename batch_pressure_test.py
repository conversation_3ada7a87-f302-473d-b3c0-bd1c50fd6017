#!/usr/bin/env python3
"""
批量压力测试
使用test_keywords.py中的真实关键词进行大规模并发测试
"""

import asyncio
import aiohttp
import json
import time
import random
from typing import List, Dict, Any
from test_keywords import keyword_info

class BatchPressureTest:
    def __init__(self, base_url: str = "http://localhost:8001", target_product_id: int = 253486273):
        self.base_url = base_url
        self.target_product_id = target_product_id
        self.session = None
        self.results = []
        self.start_time = None
        self.end_time = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def single_keyword_test(self, keyword: str, test_id: int) -> Dict[str, Any]:
        """单个关键词测试"""
        request_data = {
            "keyword": keyword,
            "target_product_id": self.target_product_id
        }
        
        start_time = time.time()
        
        try:
            async with self.session.post(
                f"{self.base_url}/keyword-matching",
                json=request_data
            ) as response:
                end_time = time.time()
                processing_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    data = result.get('data', {})
                    
                    return {
                        'test_id': test_id,
                        'keyword': keyword,
                        'success': True,
                        'status_code': response.status,
                        'processing_time': processing_time,
                        'avg_similarity': data.get('avg_similarity', 0),
                        'similar_count': data.get('similar_count', 0),
                        'competitor_count': data.get('competitor_count', 0),
                        'valid_scores': data.get('valid_scores', 0),
                        'from_cache': data.get('from_cache', False),
                        'search_products_count': data.get('search_products_count', 0),
                        'data_valid': data.get('avg_similarity', 0) > 0 and data.get('valid_scores', 0) > 0,
                        'error': None
                    }
                else:
                    error_text = await response.text()
                    return {
                        'test_id': test_id,
                        'keyword': keyword,
                        'success': False,
                        'status_code': response.status,
                        'processing_time': processing_time,
                        'error': f"HTTP {response.status}: {error_text}",
                        'data_valid': False
                    }
                    
        except Exception as e:
            end_time = time.time()
            processing_time = end_time - start_time
            
            return {
                'test_id': test_id,
                'keyword': keyword,
                'success': False,
                'status_code': 0,
                'processing_time': processing_time,
                'error': str(e),
                'data_valid': False
            }
    
    async def batch_test(self, keywords: List[str], max_concurrent: int = 20) -> List[Dict[str, Any]]:
        """批量测试"""
        print(f"🚀 开始批量压力测试")
        print(f"📊 测试参数:")
        print(f"   关键词数量: {len(keywords)}")
        print(f"   最大并发数: {max_concurrent}")
        print(f"   目标产品ID: {self.target_product_id}")
        print("=" * 60)
        
        self.start_time = time.time()
        
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def controlled_test(keyword: str, test_id: int):
            async with semaphore:
                return await self.single_keyword_test(keyword, test_id)
        
        # 创建所有任务
        tasks = [
            controlled_test(keyword, i + 1) 
            for i, keyword in enumerate(keywords)
        ]
        
        # 执行所有任务并显示进度
        completed = 0
        results = []
        
        for coro in asyncio.as_completed(tasks):
            result = await coro
            results.append(result)
            completed += 1
            
            # 显示进度
            if completed % 10 == 0 or completed == len(tasks):
                elapsed = time.time() - self.start_time
                progress = completed / len(tasks) * 100
                print(f"📈 进度: {completed}/{len(tasks)} ({progress:.1f}%) - 耗时: {elapsed:.1f}秒")
        
        self.end_time = time.time()
        self.results = results
        
        return results
    
    def analyze_results(self) -> Dict[str, Any]:
        """分析测试结果"""
        if not self.results:
            return {}
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r['success'])
        valid_data_tests = sum(1 for r in self.results if r.get('data_valid', False))
        cached_results = sum(1 for r in self.results if r.get('from_cache', False))
        new_requests = total_tests - cached_results
        
        # 计算时间统计
        total_time = self.end_time - self.start_time if self.start_time and self.end_time else 0
        processing_times = [r['processing_time'] for r in self.results if r['success']]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # 计算相似度统计
        similarities = [r.get('avg_similarity', 0) for r in self.results if r.get('data_valid', False)]
        avg_similarity = sum(similarities) / len(similarities) if similarities else 0
        
        # 计算有效评分统计
        valid_scores = [r.get('valid_scores', 0) for r in self.results if r.get('data_valid', False)]
        avg_valid_scores = sum(valid_scores) / len(valid_scores) if valid_scores else 0
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'valid_data_tests': valid_data_tests,
            'success_rate': successful_tests / total_tests * 100 if total_tests > 0 else 0,
            'data_validity_rate': valid_data_tests / total_tests * 100 if total_tests > 0 else 0,
            'cache_hit_rate': cached_results / total_tests * 100 if total_tests > 0 else 0,
            'new_requests': new_requests,
            'total_time': total_time,
            'avg_processing_time': avg_processing_time,
            'avg_similarity': avg_similarity,
            'avg_valid_scores': avg_valid_scores,
            'throughput': total_tests / total_time if total_time > 0 else 0
        }
    
    def print_analysis(self, analysis: Dict[str, Any]):
        """打印分析结果"""
        print("\n" + "=" * 60)
        print("📊 批量压力测试结果分析")
        print("=" * 60)
        
        print(f"📈 基础统计:")
        print(f"   总测试数: {analysis['total_tests']}")
        print(f"   成功请求: {analysis['successful_tests']} ({analysis['success_rate']:.1f}%)")
        print(f"   有效数据: {analysis['valid_data_tests']} ({analysis['data_validity_rate']:.1f}%)")
        print(f"   新请求数: {analysis['new_requests']}")
        print(f"   缓存命中: {analysis['total_tests'] - analysis['new_requests']} ({analysis['cache_hit_rate']:.1f}%)")
        
        print(f"\n⏱️  性能统计:")
        print(f"   总耗时: {analysis['total_time']:.1f} 秒")
        print(f"   平均处理时间: {analysis['avg_processing_time']:.1f} 秒")
        print(f"   吞吐量: {analysis['throughput']:.2f} 请求/秒")
        
        print(f"\n📊 数据质量:")
        print(f"   平均相似度: {analysis['avg_similarity']:.1f}")
        print(f"   平均有效评分数: {analysis['avg_valid_scores']:.1f}")
        
        # 判断测试结果
        if analysis['success_rate'] >= 95 and analysis['data_validity_rate'] >= 95:
            print(f"\n🎉 压力测试通过！")
            print(f"✅ 高成功率: {analysis['success_rate']:.1f}%")
            print(f"✅ 高数据有效性: {analysis['data_validity_rate']:.1f}%")
            print(f"✅ 服务稳定性良好")
        else:
            print(f"\n⚠️  压力测试需要关注")
            if analysis['success_rate'] < 95:
                print(f"   - 成功率偏低: {analysis['success_rate']:.1f}%")
            if analysis['data_validity_rate'] < 95:
                print(f"   - 数据有效性偏低: {analysis['data_validity_rate']:.1f}%")

async def main():
    """主函数"""
    print("🎯 keywordMatching 批量压力测试")
    print("📝 使用真实关键词进行大规模并发测试")
    print("=" * 60)
    
    # 测试配置
    test_configs = [
        {"keywords_count": 100, "concurrent": 20, "description": "中等规模测试"},
        {"keywords_count": 200, "concurrent": 30, "description": "大规模测试"},
        {"keywords_count": 500, "concurrent": 50, "description": "超大规模测试"}
    ]
    
    # 让用户选择测试规模
    print("请选择测试规模:")
    for i, config in enumerate(test_configs, 1):
        print(f"   {i}. {config['description']} - {config['keywords_count']}个关键词, {config['concurrent']}并发")
    
    try:
        choice = int(input("请输入选择 (1-3): ")) - 1
        if choice < 0 or choice >= len(test_configs):
            choice = 0
    except:
        choice = 0
    
    config = test_configs[choice]
    print(f"\n✅ 选择: {config['description']}")
    
    # 准备测试关键词
    available_keywords = [item['keyword'] for item in keyword_info]
    test_keywords = random.sample(available_keywords, min(config['keywords_count'], len(available_keywords)))
    
    print(f"📝 准备测试 {len(test_keywords)} 个关键词")
    
    # 执行测试
    async with BatchPressureTest() as tester:
        results = await tester.batch_test(test_keywords, config['concurrent'])
        analysis = tester.analyze_results()
        tester.print_analysis(analysis)
        
        # 保存详细结果
        timestamp = int(time.time())
        result_file = f"batch_test_results_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                'config': config,
                'analysis': analysis,
                'detailed_results': results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细结果已保存到: {result_file}")

if __name__ == "__main__":
    asyncio.run(main())
