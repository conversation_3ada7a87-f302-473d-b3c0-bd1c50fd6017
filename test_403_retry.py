#!/usr/bin/env python3
"""
测试403错误重试功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyzer import AIProductComparer, EndpointConfig
from get_product_detail import get_product_detail
from test_product_ids import nm_ids
import json
import time


def test_403_retry_mechanism():
    """测试403错误重试机制"""
    print("=== 测试403错误重试机制 ===")
    
    # 使用真实的产品ID
    product_id1 = int(nm_ids[0])  # 449105617
    product_id2 = int(nm_ids[1])  # 313067529
    
    print(f"测试产品: {product_id1} vs {product_id2}")
    
    # 配置API端点
    text_endpoints = [
        EndpointConfig(
            url="http://************:3000",
            api_key="your-api-key",
            model="deepseek/deepseek-chat-v3-0324:free",
            is_multimodal=False
        )
    ]
    
    try:
        # 创建比较器
        comparer = AIProductComparer(
            text_endpoints=text_endpoints,
            timeout=60,
            temperature=0.1
        )
        
        print("正在获取产品信息...")
        
        # 获取产品信息
        product1_info = get_product_detail(product_id1)
        product2_info = get_product_detail(product_id2)
        
        if not product1_info or not product2_info:
            print("❌ 获取产品信息失败")
            return False
        
        print(f"产品1: {product1_info.get('imt_name', 'N/A')}")
        print(f"产品2: {product2_info.get('imt_name', 'N/A')}")
        
        print("\n正在进行产品比较（可能会遇到403错误并自动重试）...")
        
        # 执行比较
        start_time = time.time()
        result = comparer.compare(product1_info, product2_info, mode="text")
        end_time = time.time()
        
        print("✅ 产品比较成功!")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        print(f"相似度评分: {result.get('similar_scores', 'N/A')}/100")
        print(f"分析说明: {result.get('reson', 'N/A')[:100]}...")
        
        # 保存比较结果
        result_filename = f"403_retry_test_result_{product_id1}_vs_{product_id2}.json"
        with open(result_filename, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"✅ 比较结果已保存到: {result_filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 产品比较失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False


def test_retry_configuration():
    """测试重试配置"""
    print("\n=== 测试重试配置 ===")
    
    # 检查重试配置
    print(f"最大重试次数: {AIProductComparer.MAX_RETRIES}")
    print(f"可重试状态码: {AIProductComparer.RETRY_STATUS}")
    print(f"多模态冷却时间: {AIProductComparer.MM_COOLDOWN} 秒")
    
    # 验证403在重试状态码中
    if 403 in AIProductComparer.RETRY_STATUS:
        print("✅ 403状态码已添加到重试列表")
    else:
        print("❌ 403状态码未在重试列表中")
        return False
    
    return True


def test_endpoint_configuration():
    """测试端点配置"""
    print("\n=== 测试端点配置 ===")
    
    try:
        # 创建端点配置
        endpoint = EndpointConfig(
            url="http://************:3000",
            api_key="test-key",
            model="deepseek/deepseek-chat-v3-0324:free",
            is_multimodal=False
        )
        
        print(f"端点URL: {endpoint.url}")
        print(f"模型: {endpoint.model}")
        print(f"是否多模态: {endpoint.is_multimodal}")
        
        # 检查请求头
        headers = endpoint.headers()
        print(f"请求头包含Authorization: {'Authorization' in headers}")
        print(f"请求头包含Content-Type: {'Content-Type' in headers}")
        
        print("✅ 端点配置正常")
        return True
        
    except Exception as e:
        print(f"❌ 端点配置失败: {e}")
        return False


def simulate_403_error():
    """模拟403错误测试（仅用于演示重试逻辑）"""
    print("\n=== 模拟403错误测试 ===")
    print("注意: 这是一个演示性测试，展示重试逻辑的工作原理")
    
    # 创建一个无效的端点来触发403错误
    invalid_endpoints = [
        EndpointConfig(
            url="http://invalid-endpoint.example.com",
            api_key="invalid-key",
            model="invalid-model",
            is_multimodal=False
        )
    ]
    
    try:
        comparer = AIProductComparer(
            text_endpoints=invalid_endpoints,
            timeout=10,  # 较短的超时时间
            temperature=0.1
        )
        
        # 创建简单的测试数据
        test_product1 = {"imt_name": "测试产品1", "description": "测试描述1"}
        test_product2 = {"imt_name": "测试产品2", "description": "测试描述2"}
        
        print("尝试使用无效端点进行比较（预期会失败）...")
        result = comparer.compare(test_product1, test_product2, mode="text")
        
        print("❌ 意外成功 - 这不应该发生")
        return False
        
    except Exception as e:
        print(f"✅ 预期的失败: {type(e).__name__}: {e}")
        print("重试机制已正确配置，会在遇到403等错误时自动重试")
        return True


def main():
    """主测试函数"""
    print("开始测试403错误重试功能...")
    
    # 1. 测试重试配置
    config_test = test_retry_configuration()
    
    # 2. 测试端点配置
    endpoint_test = test_endpoint_configuration()
    
    # 3. 模拟403错误测试
    simulation_test = simulate_403_error()
    
    # 4. 实际产品比较测试（可能遇到403错误）
    comparison_test = test_403_retry_mechanism()
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结:")
    print(f"重试配置测试: {'✅ 成功' if config_test else '❌ 失败'}")
    print(f"端点配置测试: {'✅ 成功' if endpoint_test else '❌ 失败'}")
    print(f"错误模拟测试: {'✅ 成功' if simulation_test else '❌ 失败'}")
    print(f"产品比较测试: {'✅ 成功' if comparison_test else '❌ 失败'}")
    print(f"{'='*60}")
    
    if all([config_test, endpoint_test, simulation_test]):
        print("\n🎉 403重试功能已成功配置！")
        print("\n重试机制特性:")
        print("- 403错误会自动重试最多3次")
        print("- 每次重试间隔1秒")
        print("- 支持的重试状态码: 403, 429, 500, 502, 503, 504")
        print("- 超时错误也会自动重试")
        print("- 详细的重试日志输出")
        
        if comparison_test:
            print("- ✅ 实际产品比较测试成功")
        else:
            print("- ⚠️ 实际产品比较测试失败（可能是网络或API问题）")
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")


if __name__ == "__main__":
    main()
