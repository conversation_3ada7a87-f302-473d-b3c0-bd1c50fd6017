#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片描述API
"""

import requests
import time

def test_image_description_api():
    """测试图片描述API"""
    print("🖼️ 测试图片描述API")
    print("=" * 50)
    
    test_url = "https://basket-01.wbbasket.ru/vol3804/part380496/380496894/images/big/1.webp"
    
    # 测试1: 直接访问图片描述服务
    print("1. 直接访问图片描述服务")
    try:
        start_time = time.time()
        response = requests.get(
            f"http://localhost:8000/describe?url={test_url}",
            timeout=30
        )
        end_time = time.time()
        
        print(f"   状态码: {response.status_code}")
        print(f"   处理时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            print("   ✅ 直接访问图片描述服务正常")
            print(f"   📄 响应长度: {len(response.text)} 字符")
        else:
            print(f"   ❌ 直接访问失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 直接访问异常: {e}")
    
    # 测试2: 通过网关访问图片描述服务
    print("\n2. 通过网关访问图片描述服务")
    try:
        start_time = time.time()
        response = requests.get(
            f"http://localhost/api/say-img-description/describe?url={test_url}",
            timeout=30
        )
        end_time = time.time()
        
        print(f"   状态码: {response.status_code}")
        print(f"   处理时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            print("   ✅ 网关访问图片描述服务正常")
            print(f"   📄 响应长度: {len(response.text)} 字符")
        else:
            print(f"   ❌ 网关访问失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 网关访问异常: {e}")
    
    # 测试3: 检查服务健康状态
    print("\n3. 检查图片描述服务健康状态")
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 图片描述服务健康检查正常")
            print(f"   📄 响应: {response.text}")
        else:
            print(f"   ❌ 健康检查失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")

if __name__ == "__main__":
    test_image_description_api()
