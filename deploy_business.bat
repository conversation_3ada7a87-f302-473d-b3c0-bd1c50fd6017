@echo off
REM 产品相似度业务服务 Docker 部署脚本 (Windows版本)
REM 使用 docker-compose.business.yml 进行部署
REM 作者: AI Assistant
REM 日期: 2025-01-30

setlocal enabledelayedexpansion

REM 颜色定义 (Windows CMD不支持颜色，使用echo代替)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

REM 检查Docker是否安装
:check_docker
echo %INFO% 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Docker 未安装，请先安装 Docker Desktop
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Docker Compose 未安装，请先安装 Docker Compose
    exit /b 1
)

echo %SUCCESS% Docker 环境检查通过

REM 检查外部网络是否存在
:check_network
echo %INFO% 检查外部网络 microservices...
docker network ls | findstr "microservices" >nul
if errorlevel 1 (
    echo %WARNING% 外部网络 microservices 不存在，正在创建...
    docker network create microservices
    if errorlevel 1 (
        echo %ERROR% 创建外部网络失败
        exit /b 1
    )
    echo %SUCCESS% 外部网络创建成功
) else (
    echo %SUCCESS% 外部网络检查通过
)

REM 检查环境变量文件
:check_env_file
echo %INFO% 检查环境变量文件...
if not exist ".env" (
    if exist ".env.business.example" (
        echo %WARNING% .env 文件不存在，正在从 .env.business.example 复制...
        copy ".env.business.example" ".env" >nul
        echo %INFO% 请编辑 .env 文件配置必要的环境变量
    ) else (
        echo %ERROR% .env.business.example 文件不存在，无法创建环境配置
        exit /b 1
    )
)
echo %SUCCESS% 环境变量文件检查通过

REM 构建Docker镜像
:build_image
echo %INFO% 开始构建 Docker 镜像...

REM 检查基础镜像是否存在
docker images | findstr "say_img_description-say-img-description" >nul
if errorlevel 1 (
    echo %WARNING% 基础镜像 say_img_description-say-img-description:latest 不存在
    echo %INFO% 将使用标准构建流程
)

docker-compose -f docker-compose.business.yml build
if errorlevel 1 (
    echo %ERROR% Docker 镜像构建失败
    exit /b 1
)

echo %SUCCESS% Docker 镜像构建完成

REM 启动服务
:start_services
echo %INFO% 启动业务服务...

REM 停止现有服务
docker-compose -f docker-compose.business.yml down >nul 2>&1

REM 启动服务
docker-compose -f docker-compose.business.yml up -d
if errorlevel 1 (
    echo %ERROR% 服务启动失败
    exit /b 1
)

echo %SUCCESS% 业务服务启动完成

REM 等待服务就绪
:wait_for_services
echo %INFO% 等待服务就绪...

REM 等待产品相似度服务
echo %INFO% 等待产品相似度服务...
set /a service_retry=0
:service_wait_loop
if !service_retry! geq 60 (
    echo %ERROR% 产品相似度服务启动超时
    exit /b 1
)
curl -s http://localhost:8001/health >nul 2>&1
if errorlevel 1 (
    timeout /t 3 /nobreak >nul
    set /a service_retry+=1
    goto service_wait_loop
)
echo %SUCCESS% 产品相似度服务就绪

REM 运行健康检查
:health_check
echo %INFO% 运行健康检查...

REM 检查产品相似度服务
curl -s http://localhost:8001/health >nul 2>&1
if errorlevel 1 (
    echo %ERROR% 产品相似度服务健康检查失败
    goto health_check_failed
)
echo %SUCCESS% 产品相似度服务健康检查通过

REM 检查服务注册到Consul
echo %INFO% 检查服务注册状态...
curl -s http://localhost:8500/v1/catalog/service/product-similarity >nul 2>&1
if errorlevel 1 (
    echo %WARNING% 无法连接到Consul或服务未注册
) else (
    echo %SUCCESS% 服务已成功注册到Consul
)

goto health_check_passed

:health_check_failed
echo %ERROR% 健康检查失败，请检查服务日志
docker-compose -f docker-compose.business.yml logs --tail=50
exit /b 1

:health_check_passed

REM 显示服务信息
:show_service_info
echo %INFO% 业务服务信息:
echo ==================================
echo 🔍 Consul UI:           http://localhost:8500
echo 📊 API 文档:            http://localhost:8001/docs  
echo ❤️  健康检查:           http://localhost:8001/health
echo ℹ️  服务信息:           http://localhost:8001/info
echo 🔗 服务注册:           http://localhost:8500/v1/catalog/service/product-similarity
echo ==================================

echo %INFO% 常用命令:
echo 查看服务状态: docker-compose -f docker-compose.business.yml ps
echo 查看日志:     docker-compose -f docker-compose.business.yml logs -f product-similarity
echo 停止服务:     docker-compose -f docker-compose.business.yml down
echo 重启服务:     docker-compose -f docker-compose.business.yml restart

REM 运行测试
:run_tests
if "%1"=="--test" goto run_basic_test
if "%1"=="--full-test" goto run_full_test
goto deployment_complete

:run_basic_test
echo %INFO% 运行基础测试...
timeout /t 10 /nobreak >nul

REM 设置测试目标URL
set TEST_BASE_URL=http://localhost:8001
python test_test_product_ids.py
if errorlevel 1 (
    echo %WARNING% 基础测试失败，请检查服务状态
) else (
    echo %SUCCESS% 基础测试通过
)
goto deployment_complete

:run_full_test
echo %INFO% 运行完整测试套件...
timeout /t 10 /nobreak >nul

REM 设置测试目标URL
set TEST_BASE_URL=http://localhost:8001

REM 运行基础测试
python test_test_product_ids.py
if errorlevel 1 (
    echo %WARNING% 基础测试失败，请检查服务状态
) else (
    echo %SUCCESS% 基础测试通过
)

REM 运行关键词匹配测试
python test_keyword_real_data.py
if errorlevel 1 (
    echo %WARNING% 关键词匹配测试失败，请检查服务状态
) else (
    echo %SUCCESS% 关键词匹配测试通过
)

goto deployment_complete

:deployment_complete
echo %SUCCESS% 业务服务部署完成！

REM 主程序
:main
echo %INFO% 开始部署产品相似度业务服务...

call :check_docker
call :check_network
call :check_env_file
call :build_image
call :start_services
call :wait_for_services
call :health_check
call :show_service_info
call :run_tests

goto :eof

REM 帮助信息
:show_help
echo 产品相似度业务服务 Docker 部署脚本 (Windows版本)
echo 使用 docker-compose.business.yml 进行部署
echo.
echo 用法: %0 [选项]
echo.
echo 选项:
echo   --help        显示帮助信息
echo   --test        部署后运行基础测试
echo   --full-test   部署后运行完整测试套件
echo.
echo 示例:
echo   %0                # 仅部署业务服务
echo   %0 --test         # 部署并运行基础测试
echo   %0 --full-test    # 部署并运行完整测试
echo.
echo 注意:
echo   - 确保外部网络 microservices 存在
echo   - 确保 .env 文件配置正确
echo   - 服务将在端口 8001 上运行
goto :eof

REM 参数处理
if "%1"=="--help" goto show_help
if "%1"=="-h" goto show_help
if "%1"=="--test" goto main
if "%1"=="--full-test" goto main
if "%1"=="" goto main

echo %ERROR% 未知参数: %1
call :show_help
exit /b 1
