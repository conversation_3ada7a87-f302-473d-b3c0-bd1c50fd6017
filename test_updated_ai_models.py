#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的AI模型配置
第一个模型: deepseek-ai/DeepSeek-V3
第二个模型: glm-4.5
"""

import asyncio
import aiohttp
import json
import time

# 测试配置
SERVICE_URL = "http://localhost:8001"

async def test_updated_ai_models():
    """测试更新后的AI模型配置"""
    print("🤖 测试更新后的AI模型配置")
    print("第一个模型: deepseek-ai/DeepSeek-V3")
    print("第二个模型: glm-4.5")
    print("=" * 60)
    
    # 使用新的关键词确保不会命中缓存
    test_cases = [
        {
            "keyword": "умная люстра с пультом управления",
            "target_product_id": 253486273,
            "description": "智能遥控吊灯"
        },
        {
            "keyword": "энергосберегающий светильник для офиса",
            "target_product_id": 316527894,
            "description": "办公室节能灯具"
        },
        {
            "keyword": "декоративная настольная лампа винтаж",
            "target_product_id": 253486274,
            "description": "复古装饰台灯"
        },
        {
            "keyword": "подсветка для кухонного гарнитура",
            "target_product_id": 316531871,
            "description": "厨房橱柜灯带"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}/{len(test_cases)}: {test_case['description']}")
        print(f"关键词: {test_case['keyword']}")
        print(f"目标产品ID: {test_case['target_product_id']}")
        
        try:
            start_time = time.time()
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{SERVICE_URL}/keyword-matching",
                    json={
                        "keyword": test_case["keyword"],
                        "target_product_id": test_case["target_product_id"]
                    },
                    timeout=aiohttp.ClientTimeout(total=600)  # 10分钟超时
                ) as response:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        result = data.get('data', {})
                        
                        print(f"✅ 测试成功:")
                        print(f"   • 相似度: {result.get('avg_similarity', 'N/A')}")
                        print(f"   • 相似产品数: {result.get('similar_count', 'N/A')}")
                        print(f"   • 竞争产品数: {result.get('competitor_count', 'N/A')}")
                        print(f"   • 有效评分数: {result.get('valid_scores', 'N/A')}")
                        print(f"   • 处理时间: {processing_time:.2f}秒")
                        print(f"   • 缓存状态: {result.get('from_cache', 'N/A')}")
                        
                        # 分析处理时间来判断AI模型使用情况
                        if not result.get('from_cache', True):
                            if processing_time > 60:
                                print(f"   🤖 使用了AI模型进行深度分析")
                            else:
                                print(f"   ⚡ 快速AI分析完成")
                        else:
                            print(f"   💾 使用了缓存结果")
                        
                        results.append({
                            'success': True,
                            'similarity': result.get('avg_similarity', 0),
                            'similar_count': result.get('similar_count', 0),
                            'competitor_count': result.get('competitor_count', 0),
                            'valid_scores': result.get('valid_scores', 0),
                            'processing_time': processing_time,
                            'from_cache': result.get('from_cache', True)
                        })
                        
                    else:
                        error_text = await response.text()
                        print(f"❌ 测试失败: {response.status}")
                        print(f"   错误信息: {error_text[:200]}")
                        results.append({
                            'success': False,
                            'error': f"HTTP {response.status}",
                            'processing_time': processing_time
                        })
                        
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append({
                'success': False,
                'error': str(e),
                'processing_time': 0
            })
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 AI模型测试结果总结")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r['success'])
    total_tests = len(results)
    
    print(f"📈 成功率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count > 0:
        successful_results = [r for r in results if r['success']]
        
        # 计算统计数据
        avg_similarity = sum(r.get('similarity', 0) for r in successful_results) / len(successful_results)
        avg_time = sum(r['processing_time'] for r in successful_results) / len(successful_results)
        cache_hits = sum(1 for r in successful_results if r.get('from_cache', True))
        
        # 相似度分布
        similarities = [r.get('similarity', 0) for r in successful_results]
        high_similarity = sum(1 for s in similarities if s >= 65)
        medium_similarity = sum(1 for s in similarities if 30 <= s < 65)
        low_similarity = sum(1 for s in similarities if s < 30)
        
        print(f"📊 平均相似度: {avg_similarity:.1f}")
        print(f"⏱️ 平均处理时间: {avg_time:.2f}秒")
        print(f"💾 缓存命中: {cache_hits}/{len(successful_results)} ({cache_hits/len(successful_results)*100:.1f}%)")
        
        print(f"\n🎯 相似度分布:")
        print(f"   • 高相似度 (≥65): {high_similarity}个")
        print(f"   • 中等相似度 (30-64): {medium_similarity}个")
        print(f"   • 低相似度 (<30): {low_similarity}个")
        
        # 产品发现统计
        total_similar = sum(r.get('similar_count', 0) for r in successful_results)
        total_competitors = sum(r.get('competitor_count', 0) for r in successful_results)
        avg_valid_scores = sum(r.get('valid_scores', 0) for r in successful_results) / len(successful_results)
        
        print(f"\n🔍 产品发现统计:")
        print(f"   • 总相似产品数: {total_similar}个")
        print(f"   • 总竞争产品数: {total_competitors}个")
        print(f"   • 平均有效评分数: {avg_valid_scores:.1f}个")
        
        # 检查是否有使用新AI模型的测试
        new_ai_tests = [r for r in successful_results if not r.get('from_cache', True)]
        if new_ai_tests:
            print(f"\n🤖 AI模型分析:")
            print(f"   • 新分析次数: {len(new_ai_tests)}次")
            avg_new_ai_time = sum(r['processing_time'] for r in new_ai_tests) / len(new_ai_tests)
            print(f"   • 平均AI分析时间: {avg_new_ai_time:.2f}秒")
            
            # 分析AI模型性能
            if avg_new_ai_time < 30:
                print(f"   ⚡ AI响应速度: 非常快")
            elif avg_new_ai_time < 60:
                print(f"   🚀 AI响应速度: 快速")
            elif avg_new_ai_time < 120:
                print(f"   ⏱️ AI响应速度: 正常")
            else:
                print(f"   🐌 AI响应速度: 较慢")
        else:
            print(f"\n💾 所有测试都命中了缓存，未使用新AI模型")
    
    # 最终评估
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过！AI模型配置更新成功！")
        print(f"✅ deepseek-ai/DeepSeek-V3 + glm-4.5 组合工作正常")
    elif success_count >= total_tests * 0.75:
        print(f"\n⚠️ 大部分测试通过，AI模型基本正常")
        print(f"成功率: {success_count/total_tests*100:.1f}%")
    else:
        print(f"\n🚨 多项测试失败，请检查AI模型配置")
        print(f"成功率: {success_count/total_tests*100:.1f}%")

if __name__ == "__main__":
    asyncio.run(test_updated_ai_models())
