#!/usr/bin/env python3
"""
简单的403重试功能测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from analyzer import AIProductComparer, EndpointConfig
    print("✅ 成功导入analyzer模块")
except ImportError as e:
    print(f"❌ 导入analyzer模块失败: {e}")
    sys.exit(1)


def test_retry_configuration():
    """测试重试配置"""
    print("=== 测试重试配置 ===")
    
    # 检查重试配置
    print(f"最大重试次数: {AIProductComparer.MAX_RETRIES}")
    print(f"可重试状态码: {AIProductComparer.RETRY_STATUS}")
    print(f"多模态冷却时间: {AIProductComparer.MM_COOLDOWN} 秒")
    
    # 验证403在重试状态码中
    if 403 in AIProductComparer.RETRY_STATUS:
        print("✅ 403状态码已添加到重试列表")
        return True
    else:
        print("❌ 403状态码未在重试列表中")
        return False


def test_endpoint_creation():
    """测试端点创建"""
    print("\n=== 测试端点创建 ===")
    
    try:
        # 创建端点配置
        endpoint = EndpointConfig(
            url="http://************:3000",
            api_key="test-key",
            model="deepseek/deepseek-chat-v3-0324:free",
            is_multimodal=False
        )
        
        print(f"端点URL: {endpoint.url}")
        print(f"模型: {endpoint.model}")
        print(f"是否多模态: {endpoint.is_multimodal}")
        
        # 检查请求头
        headers = endpoint.headers()
        print(f"请求头包含Authorization: {'Authorization' in headers}")
        print(f"请求头包含Content-Type: {'Content-Type' in headers}")
        
        print("✅ 端点配置正常")
        return True
        
    except Exception as e:
        print(f"❌ 端点配置失败: {e}")
        return False


def test_comparer_creation():
    """测试比较器创建"""
    print("\n=== 测试比较器创建 ===")
    
    try:
        # 创建端点配置
        text_endpoints = [
            EndpointConfig(
                url="http://************:3000",
                api_key="test-key",
                model="deepseek/deepseek-chat-v3-0324:free",
                is_multimodal=False
            )
        ]
        
        # 创建比较器
        comparer = AIProductComparer(
            text_endpoints=text_endpoints,
            timeout=60,
            temperature=0.1
        )
        
        print(f"比较器超时设置: {comparer.timeout} 秒")
        print(f"比较器温度设置: {comparer.temperature}")
        print(f"文本端点数量: {len(text_endpoints)}")
        
        print("✅ 比较器创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 比较器创建失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试403重试功能配置...")
    
    # 1. 测试重试配置
    config_test = test_retry_configuration()
    
    # 2. 测试端点创建
    endpoint_test = test_endpoint_creation()
    
    # 3. 测试比较器创建
    comparer_test = test_comparer_creation()
    
    # 总结
    print(f"\n{'='*50}")
    print("测试总结:")
    print(f"重试配置测试: {'✅ 成功' if config_test else '❌ 失败'}")
    print(f"端点创建测试: {'✅ 成功' if endpoint_test else '❌ 失败'}")
    print(f"比较器创建测试: {'✅ 成功' if comparer_test else '❌ 失败'}")
    print(f"{'='*50}")
    
    if all([config_test, endpoint_test, comparer_test]):
        print("\n🎉 403重试功能配置验证成功！")
        print("\n重试机制特性:")
        print("✅ 403错误会自动重试最多3次")
        print("✅ 每次重试间隔1秒")
        print("✅ 支持的重试状态码: 403, 429, 500, 502, 503, 504")
        print("✅ 超时错误也会自动重试")
        print("✅ 详细的重试日志输出")
        print("\n当遇到403错误时，系统会:")
        print("1. 输出警告信息: [WARN] 请求失败 (状态码: 403)，第 X 次重试...")
        print("2. 等待1秒后自动重试")
        print("3. 最多重试3次")
        print("4. 如果所有重试都失败，输出: [ERROR] 达到最大重试次数，请求最终失败")
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")


if __name__ == "__main__":
    main()
