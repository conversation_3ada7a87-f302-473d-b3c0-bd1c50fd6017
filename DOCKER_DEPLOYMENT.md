# Docker 部署指南

本文档详细介绍如何使用Docker部署产品相似度微服务。

## 🚀 快速开始

### 前置要求

- Docker Desktop (Windows/Mac) 或 Docker Engine (Linux)
- Docker Compose
- 至少 4GB 可用内存
- 至少 2GB 可用磁盘空间

### 一键部署

#### Linux/Mac 系统
```bash
# 克隆项目
git clone <repository-url>
cd product_comparison

# 一键部署
./deploy.sh

# 部署并运行测试
./deploy.sh --test
```

#### Windows 系统
```cmd
REM 克隆项目
git clone <repository-url>
cd product_comparison

REM 一键部署
deploy.bat

REM 部署并运行测试
deploy.bat --test
```

## 📋 详细部署步骤

### 1. 环境准备

```bash
# 检查Docker版本
docker --version
docker-compose --version

# 确保Docker服务运行
docker info
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env  # Linux/Mac
notepad .env  # Windows
```

关键配置项：
```env
# 数据库配置
PG_HOST=************
PG_USER=lens
PG_PASSWORD=Ls.3956573
PG_DB=lens

# AI模型配置
TEXT_ENDPOINTS=[{"url":"http://************:3000","api_key":"sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK","model":"deepseek-ai/DeepSeek-V3","is_multimodal":false}]

# 图片描述服务
IMAGE_DESCRIPTION_API_URL=http://localhost:8001/describe-image
```

### 3. 构建镜像

```bash
# 构建产品相似度服务镜像
docker build -t product-similarity:latest .

# 验证镜像构建成功
docker images | grep product-similarity
```

### 4. 启动服务

```bash
# 启动所有服务
docker-compose -f docker-compose.consul.yml up -d

# 查看服务状态
docker-compose -f docker-compose.consul.yml ps
```

### 5. 验证部署

```bash
# 等待服务启动 (约30-60秒)
sleep 60

# 检查Consul
curl http://localhost:8500/v1/status/leader

# 检查产品相似度服务
curl http://localhost:8000/health

# 查看API文档
open http://localhost:8000/docs  # Mac
start http://localhost:8000/docs  # Windows
```

## 🔧 服务配置

### 服务端口映射

| 服务 | 内部端口 | 外部端口 | 描述 |
|------|----------|----------|------|
| Consul | 8500 | 8500 | 服务发现UI |
| Product Similarity | 8000 | 8000 | 主要API服务 |

### 环境变量说明

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `CONSUL_HOST` | Consul服务地址 | consul | 是 |
| `SERVICE_PORT` | 服务端口 | 8000 | 是 |
| `PG_HOST` | PostgreSQL地址 | - | 是 |
| `TEXT_ENDPOINTS` | AI模型配置 | - | 是 |
| `IMAGE_DESCRIPTION_API_URL` | 图片描述API | - | 是 |

## 🔍 监控和管理

### 查看服务状态

```bash
# 查看所有服务状态
docker-compose -f docker-compose.consul.yml ps

# 查看特定服务日志
docker-compose -f docker-compose.consul.yml logs -f product-similarity

# 查看Consul服务
docker-compose -f docker-compose.consul.yml logs -f consul
```

### 服务管理命令

```bash
# 重启服务
docker-compose -f docker-compose.consul.yml restart product-similarity

# 停止所有服务
docker-compose -f docker-compose.consul.yml down

# 重新构建并启动
docker-compose -f docker-compose.consul.yml up --build -d

# 扩容服务实例
docker-compose -f docker-compose.consul.yml up -d --scale product-similarity=3
```

### 健康检查

访问以下端点检查服务健康状态：

- **Consul UI**: http://localhost:8500
- **服务健康检查**: http://localhost:8000/health
- **服务信息**: http://localhost:8000/info
- **API文档**: http://localhost:8000/docs

## 🧪 测试验证

### 基础功能测试

```bash
# 健康检查
curl http://localhost:8000/health

# 获取服务信息
curl http://localhost:8000/info

# 测试产品信息获取
curl http://localhost:8000/product/123456
```

### 运行测试套件

```bash
# 基础测试
python test_test_product_ids.py

# 关键词匹配测试
python test_keyword_real_data.py

# 指定测试目标
TEST_BASE_URL=http://localhost:8000 python test_test_product_ids.py
```

## 🚨 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 查看详细日志
docker-compose -f docker-compose.consul.yml logs

# 检查端口占用
netstat -tulpn | grep :8000
netstat -tulpn | grep :8500
```

#### 2. 基础镜像不存在
```bash
# 检查基础镜像
docker images | grep say_img_description

# 如果不存在，修改Dockerfile中的基础镜像
# 或者使用标准Python镜像
```

#### 3. 数据库连接失败
```bash
# 检查数据库连接
telnet ************ 5432

# 验证环境变量
docker-compose -f docker-compose.consul.yml exec product-similarity env | grep PG_
```

#### 4. AI服务连接失败
```bash
# 测试AI服务连接
curl -X POST http://************:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK" \
  -d '{"model":"deepseek-ai/DeepSeek-V3","messages":[{"role":"user","content":"test"}]}'
```

### 日志分析

```bash
# 查看最近的错误日志
docker-compose -f docker-compose.consul.yml logs --tail=100 product-similarity | grep ERROR

# 实时监控日志
docker-compose -f docker-compose.consul.yml logs -f product-similarity

# 导出日志到文件
docker-compose -f docker-compose.consul.yml logs product-similarity > service.log
```

## 🔄 更新和维护

### 更新服务

```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker build -t product-similarity:latest .

# 重启服务
docker-compose -f docker-compose.consul.yml up -d --force-recreate product-similarity
```

### 数据备份

```bash
# 备份Consul数据
docker exec consul-server consul snapshot save /consul/data/backup.snap

# 备份PostgreSQL数据 (如果使用本地数据库)
docker exec postgres-container pg_dump -U username dbname > backup.sql
```

### 清理资源

```bash
# 停止并删除所有容器
docker-compose -f docker-compose.consul.yml down

# 删除未使用的镜像
docker image prune -f

# 删除未使用的卷
docker volume prune -f
```

## 📈 性能优化

### 资源限制

在 `docker-compose.consul.yml` 中添加资源限制：

```yaml
services:
  product-similarity:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

### 并发配置

调整环境变量优化性能：

```env
MAX_CONCURRENT_REQUESTS=20
REQUEST_TIMEOUT=60
CACHE_TTL=7200
AI_TIMEOUT=180
```

## 📞 支持

如遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查服务日志
3. 提交Issue并附上详细的错误信息和日志
4. 联系维护团队

---

**注意**: 确保所有外部依赖服务（数据库、AI服务、图片描述服务）正常运行后再启动本服务。
