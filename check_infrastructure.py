#!/usr/bin/env python3
"""
检查基础设施服务连通性
"""
import asyncio
import httpx

async def check_infrastructure():
    """检查基础设施服务"""
    
    print("🔍 检查基础设施服务连通性")
    print("=" * 50)
    
    # 1. 检查Consul
    print("\n📊 1. 检查Consul服务发现")
    print("-" * 30)
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get("http://localhost:8500/v1/status/leader")
            if response.status_code == 200:
                leader = response.text.strip('"')
                print(f"✅ Consul Leader: {leader}")
                
                # 检查已注册的服务
                services_response = await client.get("http://localhost:8500/v1/catalog/services")
                if services_response.status_code == 200:
                    services = services_response.json()
                    print(f"📋 已注册服务数量: {len(services)}")
                    for service_name, tags in services.items():
                        print(f"  - {service_name}: {tags}")
            else:
                print(f"❌ Consul连接失败: {response.status_code}")
    except Exception as e:
        print(f"❌ Consul连接异常: {e}")
    
    # 2. 检查网关
    print("\n🌐 2. 检查Nginx网关")
    print("-" * 30)
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get("http://localhost/gateway/status")
            if response.status_code == 200:
                print("✅ Nginx网关正常")
                print(f"响应: {response.text}")
            else:
                print(f"❌ 网关连接失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 网关连接异常: {e}")
    
    # 3. 检查微服务网络
    print("\n🔗 3. 检查微服务网络")
    print("-" * 30)
    import subprocess
    try:
        result = subprocess.run(
            ["docker", "network", "ls", "--filter", "name=microservices"],
            capture_output=True, text=True
        )
        if result.returncode == 0:
            networks = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            if networks and networks[0]:
                print("✅ 微服务网络存在")
                for network in networks:
                    print(f"  {network}")
            else:
                print("❌ 微服务网络不存在")
        else:
            print(f"❌ 网络检查失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 网络检查异常: {e}")

if __name__ == "__main__":
    asyncio.run(check_infrastructure())
