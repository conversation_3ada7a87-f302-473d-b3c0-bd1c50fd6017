#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
"""

import requests
import json

def test_health():
    try:
        response = requests.get("http://localhost:8001/health", timeout=10)
        print(f"健康检查: {response.status_code}")
        if response.status_code == 200:
            print("✅ 服务正常运行")
            return True
        else:
            print("❌ 服务异常")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_api():
    try:
        response = requests.post(
            "http://localhost:8001/keyword-matching",
            json={
                "keyword": "люстра",
                "target_product_id": 253486273
            },
            timeout=30
        )
        print(f"API测试: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API正常，相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            return True
        else:
            print(f"❌ API异常: {response.text}")
            return False
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 快速测试开始")
    
    health_ok = test_health()
    if health_ok:
        api_ok = test_api()
        if api_ok:
            print("🎉 所有测试通过！")
        else:
            print("⚠️ API测试失败")
    else:
        print("⚠️ 健康检查失败")
