#!/usr/bin/env python3
"""
测试更新后的部署
验证修改后的代码是否正确部署
"""

import asyncio
import aiohttp
import json
import time

async def test_updated_deployment():
    """测试更新后的部署"""
    
    print("🔄 测试更新后的部署")
    print("=" * 50)
    
    # 测试参数
    test_keyword = "люстра потолочная светодиодная"
    target_product_id = 253486273
    
    request_data = {
        "keyword": test_keyword,
        "target_product_id": target_product_id
    }
    
    print(f"📝 测试参数:")
    print(f"   关键词: {test_keyword}")
    print(f"   目标产品ID: {target_product_id}")
    print("=" * 50)
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            print("🚀 发送请求...")
            
            async with session.post(
                "http://localhost:8001/keyword-matching",
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=300)
            ) as response:
                end_time = time.time()
                processing_time = end_time - start_time
                
                print(f"📊 响应状态: {response.status}")
                print(f"⏱️  处理时间: {processing_time:.1f} 秒")
                
                if response.status == 200:
                    result = await response.json()
                    
                    print("\n✅ 请求成功!")
                    print("📋 响应结果:")
                    print(json.dumps(result, ensure_ascii=False, indent=2))
                    
                    # 检查数据结构
                    data = result.get('data', {})
                    if data:
                        print(f"\n📈 关键数据:")
                        print(f"   关键词: {data.get('keyword', 'N/A')}")
                        print(f"   目标产品ID: {data.get('target_product_id', 'N/A')}")
                        print(f"   平均相似度: {data.get('avg_similarity', 0)}")
                        print(f"   相似产品数: {data.get('similar_count', 0)}")
                        print(f"   竞争产品数: {data.get('competitor_count', 0)}")
                        print(f"   有效评分数: {data.get('valid_scores', 0)}")
                        print(f"   缓存状态: {'缓存' if data.get('from_cache', False) else '新计算'}")
                        print(f"   创建时间: {data.get('created_at', 'N/A')}")
                        
                        # 验证数据有效性
                        if data.get('avg_similarity', 0) > 0 and data.get('valid_scores', 0) > 0:
                            print("\n🎉 数据验证通过!")
                            print("✅ 相似度计算正常")
                            print("✅ 有效评分数正常")
                            print("✅ 修改后的代码部署成功")
                        else:
                            print("\n⚠️  数据验证失败!")
                            print(f"❌ 相似度: {data.get('avg_similarity', 0)}")
                            print(f"❌ 有效评分: {data.get('valid_scores', 0)}")
                    else:
                        print("\n❌ 响应数据为空")
                        
                else:
                    error_text = await response.text()
                    print(f"\n❌ 请求失败!")
                    print(f"状态码: {response.status}")
                    print(f"错误信息: {error_text}")
                    
    except asyncio.TimeoutError:
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"\n⏰ 请求超时! 耗时: {processing_time:.1f} 秒")
        
    except Exception as e:
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"\n❌ 请求异常! 耗时: {processing_time:.1f} 秒")
        print(f"异常信息: {str(e)}")

async def test_docker_logs():
    """检查Docker日志中的修改内容"""
    print("\n" + "=" * 50)
    print("📋 检查Docker日志")
    print("=" * 50)
    print("请手动检查Docker日志以确认修改生效:")
    print("docker logs product-similarity-server --tail 50")
    print("\n预期看到的修改:")
    print("1. image_descriptions 字段而不是 images_description_text")
    print("2. 产品ID前缀的完整产品信息日志")
    print("3. 简化的日志格式")

async def main():
    """主函数"""
    print("🔄 测试更新后的keywordMatching部署")
    print("📝 验证代码修改是否正确部署到Docker")
    
    await test_updated_deployment()
    await test_docker_logs()

if __name__ == "__main__":
    asyncio.run(main())
