#!/usr/bin/env python3
"""
关键词匹配功能压力测试
"""
import asyncio
import time
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.product_similarity.services.keyword_matching import keywordMatching
from src.product_similarity.db import init_pool, close_pool

# 测试关键词列表（前10个用于快速测试）
KEYWORDS = [
    "люстра потолочная для кухни современная",
    "лампы светодиодные для кухни",
    "люстра светодиодная на потолок",
    "люстры",
    "люстра на кухню",
    "светильник потолочный",
    "люстра светодиодная",
    "светодиодные светильники на потолок",
    "люстра для кухни",
    "плафон потолочный"
]

# 目标产品ID
TARGET_PRODUCT_ID = 253486273


async def test_single_keyword_with_details(keyword: str, target_id: int) -> dict:
    """测试单个关键词并获取详细的相似度对比结果"""
    start_time = time.time()

    try:
        # 导入需要的函数
        from src.product_similarity.services.wildberries_search import search_wildberries, extract_product_ids_from_search
        from src.product_similarity.services.similarity import compare_products_by_ids

        print(f"  🔍 开始搜索关键词: '{keyword}'")

        # 1. 搜索产品
        search_result = await search_wildberries(keyword)
        if not search_result:
            return {
                "keyword": keyword,
                "status": "error",
                "duration": time.time() - start_time,
                "error": "搜索失败"
            }

        # 2. 提取产品ID
        product_ids = extract_product_ids_from_search(search_result, limit=50)
        print(f"  📦 找到 {len(product_ids)} 个产品")

        # 3. 过滤掉目标产品ID
        comparison_ids = [pid for pid in product_ids if pid != target_id]
        print(f"  🎯 将与 {len(comparison_ids)} 个产品进行相似度对比")

        # 4. 逐个进行相似度对比并打印结果
        similarity_scores = []
        print(f"  📊 详细相似度对比结果:")

        for i, product_id in enumerate(comparison_ids, 1):
            try:
                comparison_result = await compare_products_by_ids(target_id, product_id)
                if comparison_result and 'similar_scores' in comparison_result:
                    score = comparison_result.get('similar_scores', 0)
                    similarity_scores.append(score)

                    # 根据相似度分数设置颜色标记
                    if score >= 80:
                        status_icon = "🔥"  # 竞品
                    elif score >= 65:
                        status_icon = "✅"  # 相似
                    else:
                        status_icon = "⚪"  # 普通

                    print(f"    {i:2d}. {status_icon} 产品ID: {product_id} -> 相似度: {score}分")
                else:
                    print(f"    {i:2d}. ❌ 产品ID: {product_id} -> 对比失败")
            except Exception as e:
                print(f"    {i:2d}. ❌ 产品ID: {product_id} -> 错误: {str(e)}")

        # 5. 计算统计数据
        if similarity_scores:
            avg_similarity = sum(similarity_scores) / len(similarity_scores)
            similar_count = sum(1 for score in similarity_scores if score > 65)
            competitor_count = sum(1 for score in similarity_scores if score > 80)

            print(f"  📈 统计结果:")
            print(f"    - 平均相似度: {avg_similarity:.1f}分")
            print(f"    - 相似产品(>65分): {similar_count}个")
            print(f"    - 竞品产品(>80分): {competitor_count}个")
            print(f"    - 有效对比数: {len(similarity_scores)}个")

        end_time = time.time()

        return {
            "keyword": keyword,
            "status": "success",
            "duration": end_time - start_time,
            "similarity_scores": similarity_scores,
            "product_ids": comparison_ids,
            "avg_similarity": avg_similarity if similarity_scores else 0,
            "similar_count": similar_count if similarity_scores else 0,
            "competitor_count": competitor_count if similarity_scores else 0
        }

    except Exception as e:
        end_time = time.time()
        return {
            "keyword": keyword,
            "status": "error",
            "duration": end_time - start_time,
            "error": str(e)
        }


async def run_stress_test():
    """运行压力测试"""
    print("🚀 开始关键词匹配压力测试")
    print(f"📊 测试关键词数量: {len(KEYWORDS)}")
    print(f"🎯 目标产品ID: {TARGET_PRODUCT_ID}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 初始化数据库连接池
        await init_pool()
        print("✅ 数据库连接池初始化完成")
        
        # 记录总体开始时间
        total_start_time = time.time()
        
        # 存储所有测试结果
        results = []
        
        # 逐个测试关键词
        for i, keyword in enumerate(KEYWORDS, 1):
            print(f"\n{'='*60}")
            print(f"[{i}/{len(KEYWORDS)}] 测试关键词: '{keyword}'")
            print(f"{'='*60}")

            result = await test_single_keyword_with_details(keyword, TARGET_PRODUCT_ID)
            results.append(result)

            if result["status"] == "success":
                print(f"  ✅ 完成 | 耗时: {result['duration']:.2f}s")
            else:
                print(f"  ❌ 失败 | 耗时: {result['duration']:.2f}s | 错误: {result['error']}")

            # 添加分隔线
            print(f"{'='*60}\n")
        
        # 计算总体统计
        total_end_time = time.time()
        total_duration = total_end_time - total_start_time
        
        # 统计结果
        success_count = sum(1 for r in results if r["status"] == "success")
        error_count = len(results) - success_count
        avg_duration = sum(r["duration"] for r in results) / len(results)
        
        # 统计相似度数据
        similarity_scores = []
        total_similar_count = 0
        total_competitor_count = 0

        for result in results:
            if result["status"] == "success":
                similarity_scores.append(result.get("avg_similarity", 0))
                total_similar_count += result.get("similar_count", 0)
                total_competitor_count += result.get("competitor_count", 0)

        avg_similarity = sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0
        
        # 输出测试报告
        print("\n" + "=" * 80)
        print("📈 压力测试报告")
        print("=" * 80)
        print(f"🕐 总耗时: {total_duration:.2f}秒")
        print(f"📊 测试总数: {len(results)}")
        print(f"✅ 成功数量: {success_count}")
        print(f"❌ 失败数量: {error_count}")
        print(f"📈 成功率: {success_count/len(results)*100:.1f}%")
        print(f"⏱️  平均耗时: {avg_duration:.2f}秒/关键词")
        print(f"🎯 平均相似度: {avg_similarity:.1f}分")
        print(f"✅ 总相似产品数(>65分): {total_similar_count}")
        print(f"🔥 总竞品数(>80分): {total_competitor_count}")
        
        # 输出失败的关键词
        if error_count > 0:
            print(f"\n❌ 失败的关键词:")
            for result in results:
                if result["status"] == "error":
                    print(f"  - '{result['keyword']}': {result['error']}")
        
        # 输出相似度最高的关键词
        if similarity_scores:
            print(f"\n🏆 相似度最高的关键词:")
            sorted_results = sorted(
                [r for r in results if r["status"] == "success"],
                key=lambda x: x.get("avg_similarity", 0),
                reverse=True
            )[:5]

            for i, result in enumerate(sorted_results, 1):
                print(f"  {i}. '{result['keyword']}' - {result.get('avg_similarity', 0):.1f}分 (相似:{result.get('similar_count', 0)}, 竞品:{result.get('competitor_count', 0)})")
        
        print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 压力测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("🔒 数据库连接池已关闭")


if __name__ == "__main__":
    asyncio.run(run_stress_test())
