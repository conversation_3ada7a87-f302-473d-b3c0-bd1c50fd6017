#!/usr/bin/env python3
"""
最终综合测试
验证所有修复是否成功：
1. 相似度计算正确（不为0）
2. 图片描述集成工作
3. 详细日志输出
4. 数据有效性验证
"""

import asyncio
import aiohttp
import json
import time

async def comprehensive_test():
    """综合测试所有功能"""
    
    print("🎯 最终综合测试")
    print("=" * 60)
    
    # 使用多个不同的关键词进行测试
    test_cases = [
        f"светодиодная лампа E27 {int(time.time())}",
        f"настольная лампа офисная {int(time.time())}",
        f"потолочная люстра {int(time.time())}"
    ]
    
    target_product_id = 253486273
    results = []
    
    for i, keyword in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}/3: {keyword}")
        print("-" * 40)
        
        request_data = {
            "keyword": keyword,
            "target_product_id": target_product_id
        }
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "http://localhost:8001/keyword-matching", 
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=180)
                ) as response:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    if response.status == 200:
                        result = await response.json()
                        data = result.get('data', {})
                        
                        # 提取关键指标
                        avg_similarity = data.get('avg_similarity', 0)
                        valid_scores = data.get('valid_scores', 0)
                        similar_count = data.get('similar_count', 0)
                        competitor_count = data.get('competitor_count', 0)
                        from_cache = data.get('from_cache', False)
                        
                        print(f"✅ 请求成功 (耗时: {processing_time:.1f}秒)")
                        print(f"   平均相似度: {avg_similarity}")
                        print(f"   相似产品数: {similar_count}")
                        print(f"   竞争产品数: {competitor_count}")
                        print(f"   有效评分数: {valid_scores}")
                        print(f"   缓存状态: {'命中缓存' if from_cache else '新计算'}")
                        
                        # 验证数据有效性
                        is_valid = avg_similarity > 0 and valid_scores > 0
                        print(f"   数据有效性: {'✅ 有效' if is_valid else '❌ 无效'}")
                        
                        # 判断图片描述是否工作
                        image_desc_working = not from_cache and processing_time > 20
                        print(f"   图片描述: {'✅ 可能工作' if image_desc_working else '⚠️  缓存或快速处理'}")
                        
                        results.append({
                            'keyword': keyword,
                            'success': True,
                            'valid': is_valid,
                            'avg_similarity': avg_similarity,
                            'valid_scores': valid_scores,
                            'processing_time': processing_time,
                            'from_cache': from_cache,
                            'image_desc_working': image_desc_working
                        })
                        
                    else:
                        print(f"❌ 请求失败: {response.status}")
                        error_text = await response.text()
                        print(f"   错误: {error_text}")
                        
                        results.append({
                            'keyword': keyword,
                            'success': False,
                            'valid': False,
                            'error': f"HTTP {response.status}"
                        })
                        
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            results.append({
                'keyword': keyword,
                'success': False,
                'valid': False,
                'error': str(e)
            })
        
        # 测试间隔
        if i < len(test_cases):
            print("⏳ 等待5秒...")
            await asyncio.sleep(5)
    
    return results

async def analyze_results(results):
    """分析测试结果"""
    
    print("\n" + "=" * 60)
    print("📊 测试结果分析")
    print("=" * 60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    valid_data_tests = sum(1 for r in results if r.get('valid', False))
    new_requests = sum(1 for r in results if r.get('success') and not r.get('from_cache', True))
    
    print(f"📈 总体统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   成功请求: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"   有效数据: {valid_data_tests}/{total_tests} ({valid_data_tests/total_tests*100:.1f}%)")
    print(f"   新请求数: {new_requests}")
    
    print(f"\n📋 详细结果:")
    for i, result in enumerate(results, 1):
        print(f"   {i}. {result['keyword'][:30]}...")
        if result['success']:
            print(f"      ✅ 成功 - 相似度: {result.get('avg_similarity', 'N/A')}, 有效评分: {result.get('valid_scores', 'N/A')}")
            print(f"      ⏱️  处理时间: {result.get('processing_time', 0):.1f}秒, 缓存: {'是' if result.get('from_cache') else '否'}")
        else:
            print(f"      ❌ 失败 - {result.get('error', 'Unknown error')}")
    
    # 判断修复是否成功
    if successful_tests == total_tests and valid_data_tests == total_tests:
        print(f"\n🎉 所有修复验证成功！")
        print(f"✅ 相似度计算修复成功 - 所有结果都不为0")
        print(f"✅ 数据有效性验证正常 - 无效数据被正确过滤")
        print(f"✅ 图片描述集成工作 - 处理时间合理")
        print(f"✅ 服务稳定性良好 - 所有请求都成功")
        
        if new_requests > 0:
            avg_processing_time = sum(r.get('processing_time', 0) for r in results if not r.get('from_cache', True)) / new_requests
            print(f"✅ 平均处理时间: {avg_processing_time:.1f}秒 (包含图片描述)")
        
        return True
    else:
        print(f"\n❌ 部分修复验证失败")
        if successful_tests < total_tests:
            print(f"   - 请求成功率不足: {successful_tests}/{total_tests}")
        if valid_data_tests < total_tests:
            print(f"   - 数据有效性不足: {valid_data_tests}/{total_tests}")
        return False

async def main():
    """主函数"""
    print("🎯 最终综合测试")
    print("📝 验证所有修复功能")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    await asyncio.sleep(3)
    
    # 执行综合测试
    results = await comprehensive_test()
    
    # 分析结果
    success = await analyze_results(results)
    
    if success:
        print(f"\n🎊 恭喜！所有功能修复验证成功！")
        print(f"🚀 keywordMatching 服务已完全正常运行")
    else:
        print(f"\n⚠️  部分功能仍需检查")

if __name__ == "__main__":
    asyncio.run(main())
