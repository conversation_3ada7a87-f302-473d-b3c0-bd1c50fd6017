#!/bin/bash

# 产品相似度微服务 Docker 部署脚本
# 作者: AI Assistant
# 日期: 2025-01-30

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            log_warning ".env 文件不存在，正在从 .env.example 复制..."
            cp .env.example .env
            log_info "请编辑 .env 文件配置必要的环境变量"
        else
            log_error ".env.example 文件不存在，无法创建环境配置"
            exit 1
        fi
    fi
    log_success "环境变量文件检查通过"
}

# 构建Docker镜像
build_image() {
    log_info "开始构建 Docker 镜像..."
    
    # 检查基础镜像是否存在
    if ! docker images | grep -q "say_img_description-say-img-description"; then
        log_warning "基础镜像 say_img_description-say-img-description:latest 不存在"
        log_info "请确保图片描述服务已经构建，或修改 Dockerfile 中的基础镜像"
    fi
    
    docker build -t product-similarity:latest . || {
        log_error "Docker 镜像构建失败"
        exit 1
    }
    
    log_success "Docker 镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 停止现有服务
    docker-compose -f docker-compose.consul.yml down 2>/dev/null || true
    
    # 启动服务
    docker-compose -f docker-compose.consul.yml up -d || {
        log_error "服务启动失败"
        exit 1
    }
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待Consul
    log_info "等待 Consul 服务..."
    for i in {1..30}; do
        if curl -s http://localhost:8500/v1/status/leader > /dev/null 2>&1; then
            log_success "Consul 服务就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Consul 服务启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待产品相似度服务
    log_info "等待产品相似度服务..."
    for i in {1..60}; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            log_success "产品相似度服务就绪"
            break
        fi
        if [ $i -eq 60 ]; then
            log_error "产品相似度服务启动超时"
            exit 1
        fi
        sleep 3
    done
}

# 运行健康检查
health_check() {
    log_info "运行健康检查..."
    
    # 检查Consul
    consul_status=$(curl -s http://localhost:8500/v1/status/leader)
    if [ -n "$consul_status" ]; then
        log_success "Consul 健康检查通过"
    else
        log_error "Consul 健康检查失败"
        return 1
    fi
    
    # 检查产品相似度服务
    health_response=$(curl -s http://localhost:8000/health)
    if echo "$health_response" | grep -q "healthy\|success"; then
        log_success "产品相似度服务健康检查通过"
    else
        log_error "产品相似度服务健康检查失败"
        log_error "响应: $health_response"
        return 1
    fi
    
    return 0
}

# 显示服务信息
show_service_info() {
    log_info "服务信息:"
    echo "=================================="
    echo "🔍 Consul UI:           http://localhost:8500"
    echo "📊 API 文档:            http://localhost:8000/docs"
    echo "❤️  健康检查:           http://localhost:8000/health"
    echo "ℹ️  服务信息:           http://localhost:8000/info"
    echo "=================================="
    
    log_info "常用命令:"
    echo "查看服务状态: docker-compose -f docker-compose.consul.yml ps"
    echo "查看日志:     docker-compose -f docker-compose.consul.yml logs -f product-similarity"
    echo "停止服务:     docker-compose -f docker-compose.consul.yml down"
    echo "重启服务:     docker-compose -f docker-compose.consul.yml restart"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 等待服务完全就绪
    sleep 10
    
    # 运行基础测试
    if python test_test_product_ids.py; then
        log_success "基础测试通过"
    else
        log_warning "基础测试失败，请检查服务状态"
    fi
    
    # 运行关键词匹配测试（可选）
    if [ "$1" = "--full-test" ]; then
        log_info "运行完整测试套件..."
        if python test_keyword_real_data.py; then
            log_success "完整测试通过"
        else
            log_warning "完整测试失败，请检查服务状态"
        fi
    fi
}

# 主函数
main() {
    log_info "开始部署产品相似度微服务..."
    
    # 检查环境
    check_docker
    check_env_file
    
    # 构建和启动
    build_image
    start_services
    wait_for_services
    
    # 健康检查
    if health_check; then
        log_success "所有服务健康检查通过"
        show_service_info
        
        # 运行测试
        if [ "$1" = "--test" ] || [ "$1" = "--full-test" ]; then
            run_tests "$1"
        fi
        
        log_success "部署完成！"
    else
        log_error "健康检查失败，请检查服务日志"
        docker-compose -f docker-compose.consul.yml logs --tail=50
        exit 1
    fi
}

# 帮助信息
show_help() {
    echo "产品相似度微服务 Docker 部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help        显示帮助信息"
    echo "  --test        部署后运行基础测试"
    echo "  --full-test   部署后运行完整测试套件"
    echo ""
    echo "示例:"
    echo "  $0                # 仅部署服务"
    echo "  $0 --test         # 部署并运行基础测试"
    echo "  $0 --full-test    # 部署并运行完整测试"
}

# 参数处理
case "$1" in
    --help|-h)
        show_help
        exit 0
        ;;
    --test|--full-test|"")
        main "$1"
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
