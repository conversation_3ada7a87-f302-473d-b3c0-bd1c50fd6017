#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品比较示例
演示如何使用 analyzer.py 中的功能通过产品ID直接比较产品相似度
"""

from analyzer import compare_products_by_ids, EndpointConfig, AIProductComparer


def main():
    """主函数：演示产品比较功能"""
    
    # ---------- 1. 配置API端点 ----------
    text_endpoints = [
        EndpointConfig(
            url="http://************:3000",  # 接口基础地址
            api_key="sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",  # API Key
            model="deepseek/deepseek-chat-v3-0324:free",  # 模型名
            is_multimodal=False  # 纯文本模型
        ),
        EndpointConfig(
            url="http://************:3000",
            api_key="sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",
            model="deepseek-ai/DeepSeek-V3",
            is_multimodal=False
        ),
    ]
    
    # ---------- 2. 方式一：使用便捷函数 ----------
    print("=== 方式一：使用便捷函数比较产品 ===")
    
    # 示例产品ID（请替换为实际的产品ID）
    product_id1 = 123456789
    product_id2 = 987654321
    
    try:
        result = compare_products_by_ids(
            product_id1=product_id1,
            product_id2=product_id2,
            text_endpoints=text_endpoints,
            mode="text",  # 使用纯文本模式
            timeout=60    # 60秒超时
        )
        
        print(f"产品 {product_id1} 与产品 {product_id2} 的比较结果:")
        print(f"  相似度评分: {result['similar_scores']}/100")
        print(f"  分析说明: {result['reson']}")
        
    except Exception as e:
        print(f"比较失败: {e}")
    
    # ---------- 3. 方式二：使用类方法 ----------
    print("\n=== 方式二：使用类方法比较产品 ===")
    
    # 创建比较器实例
    comparer = AIProductComparer(
        text_endpoints=text_endpoints,
        timeout=60,
        temperature=0.1
    )
    
    try:
        result = comparer.compare_by_ids(
            product_id1=product_id1,
            product_id2=product_id2,
            mode="text"
        )
        
        print(f"使用类方法的比较结果:")
        print(f"  相似度评分: {result['similar_scores']}/100")
        print(f"  分析说明: {result['reson']}")
        
    except Exception as e:
        print(f"比较失败: {e}")
    
    # ---------- 4. 批量比较示例 ----------
    print("\n=== 批量比较示例 ===")
    
    # 示例产品ID列表
    product_ids = [123456789, 987654321, 111222333, 444555666, 777888999]
    
    # 比较前两个产品
    if len(product_ids) >= 2:
        try:
            for i in range(min(3, len(product_ids) - 1)):  # 最多比较3对
                id1 = product_ids[i]
                id2 = product_ids[i + 1]
                
                result = comparer.compare_by_ids(id1, id2, mode="text")
                print(f"产品 {id1} vs {id2}: 相似度 {result['similar_scores']}/100")
                
        except Exception as e:
            print(f"批量比较失败: {e}")


def compare_specific_products():
    """比较特定产品的示例函数"""
    
    # 配置端点
    text_endpoints = [
        EndpointConfig(
            url="http://************:3000",
            api_key="sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",
            model="deepseek/deepseek-chat-v3-0324:free",
            is_multimodal=False
        )
    ]
    
    # 要比较的产品ID（请替换为实际的产品ID）
    products_to_compare = [
        (123456789, 987654321, "产品A vs 产品B"),
        (111222333, 444555666, "产品C vs 产品D"),
        (777888999, 123456789, "产品E vs 产品A"),
    ]
    
    print("=== 特定产品比较 ===")
    
    for id1, id2, description in products_to_compare:
        try:
            result = compare_products_by_ids(
                product_id1=id1,
                product_id2=id2,
                text_endpoints=text_endpoints,
                mode="text"
            )
            
            print(f"\n{description}:")
            print(f"  产品ID: {id1} vs {id2}")
            print(f"  相似度: {result['similar_scores']}/100")
            print(f"  说明: {result['reson']}")
            
            # 根据相似度给出建议
            score = result['similar_scores']
            if score >= 80:
                suggestion = "高度相似，可能是同类产品"
            elif score >= 60:
                suggestion = "中等相似，有一定关联性"
            elif score >= 40:
                suggestion = "低度相似，存在部分共同点"
            else:
                suggestion = "相似度很低，基本不相关"
            
            print(f"  建议: {suggestion}")
            
        except Exception as e:
            print(f"\n{description} 比较失败: {e}")


if __name__ == "__main__":
    print("产品比较功能演示")
    print("=" * 50)
    
    # 运行主要示例
    main()
    
    print("\n" + "=" * 50)
    
    # 运行特定产品比较示例
    compare_specific_products()
    
    print("\n使用说明:")
    print("1. 请将示例中的产品ID替换为实际的产品ID")
    print("2. 确保API端点配置正确")
    print("3. 相似度评分范围为1-100，分数越高表示越相似")
    print("4. 可以根据需要调整timeout和temperature参数")
