#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网关访问
"""

import requests
import json

def test_gateway():
    try:
        response = requests.post(
            "http://localhost/api/product-similarity/keyword-matching",
            json={
                "keyword": "люстра",
                "target_product_id": 253486273
            },
            timeout=30
        )
        print(f"网关测试: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 网关访问正常，相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            return True
        else:
            print(f"❌ 网关访问异常: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 网关访问失败: {e}")
        return False

if __name__ == "__main__":
    print("🌐 测试网关访问")
    test_gateway()
