#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网关访问测试脚本
测试通过Nginx网关访问keywordMatching服务
"""

import asyncio
import httpx
import json
import time
from datetime import datetime

# 测试配置
GATEWAY_BASE_URL = "http://localhost"
DIRECT_BASE_URL = "http://localhost:8001"
TEST_KEYWORD = "люстра потолочная"
TARGET_PRODUCT_ID = 253486273

async def test_gateway_health():
    """测试网关健康检查"""
    print("🏥 测试网关健康检查...")
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.get(f"{GATEWAY_BASE_URL}/api/product-similarity/health")
            
            if response.status_code == 200:
                print("✅ 网关健康检查通过")
                return True
            else:
                print(f"❌ 网关健康检查失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 网关健康检查异常: {str(e)}")
        return False

async def test_gateway_keyword_matching():
    """测试网关keywordMatching功能"""
    print("\n🔍 测试网关keywordMatching功能...")
    
    try:
        start_time = time.time()
        
        async with httpx.AsyncClient(timeout=120) as client:
            response = await client.post(
                f"{GATEWAY_BASE_URL}/api/product-similarity/keyword-matching",
                json={
                    "keyword": TEST_KEYWORD,
                    "target_product_id": TARGET_PRODUCT_ID
                }
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 网关keywordMatching功能正常")
                print(f"   处理时间: {processing_time:.2f}秒")
                print(f"   关键词: {data.get('data', {}).get('keyword', 'N/A')}")
                print(f"   相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
                print(f"   相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
                print(f"   竞争产品数: {data.get('data', {}).get('competitor_count', 'N/A')}")
                print(f"   缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
                return True
            else:
                print(f"❌ 网关keywordMatching功能失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 网关keywordMatching功能异常: {str(e)}")
        return False

async def test_direct_vs_gateway():
    """对比直接访问和网关访问的结果"""
    print("\n⚖️ 对比直接访问和网关访问...")
    
    test_keyword = "светильник LED"
    
    try:
        async with httpx.AsyncClient(timeout=120) as client:
            # 直接访问
            print("   📡 直接访问测试...")
            start_time = time.time()
            direct_response = await client.post(
                f"{DIRECT_BASE_URL}/keyword-matching",
                json={
                    "keyword": test_keyword,
                    "target_product_id": TARGET_PRODUCT_ID
                }
            )
            direct_time = time.time() - start_time
            
            # 网关访问
            print("   🌐 网关访问测试...")
            start_time = time.time()
            gateway_response = await client.post(
                f"{GATEWAY_BASE_URL}/api/product-similarity/keyword-matching",
                json={
                    "keyword": test_keyword,
                    "target_product_id": TARGET_PRODUCT_ID
                }
            )
            gateway_time = time.time() - start_time
            
            if direct_response.status_code == 200 and gateway_response.status_code == 200:
                direct_data = direct_response.json()
                gateway_data = gateway_response.json()
                
                print("✅ 直接访问和网关访问都成功")
                print(f"   📊 直接访问 - 相似度: {direct_data.get('data', {}).get('avg_similarity', 'N/A')}, 时间: {direct_time:.2f}秒")
                print(f"   📊 网关访问 - 相似度: {gateway_data.get('data', {}).get('avg_similarity', 'N/A')}, 时间: {gateway_time:.2f}秒")
                
                # 检查结果一致性
                direct_similarity = direct_data.get('data', {}).get('avg_similarity', 0)
                gateway_similarity = gateway_data.get('data', {}).get('avg_similarity', 0)
                
                if direct_similarity == gateway_similarity:
                    print("   ✅ 结果一致性验证通过")
                else:
                    print(f"   ⚠️ 结果不一致 - 直接: {direct_similarity}, 网关: {gateway_similarity}")
                
                return True
            else:
                print(f"❌ 对比测试失败")
                print(f"   直接访问: {direct_response.status_code}")
                print(f"   网关访问: {gateway_response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ 对比测试异常: {str(e)}")
        return False

async def test_gateway_error_handling():
    """测试网关错误处理"""
    print("\n🚨 测试网关错误处理...")
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            # 测试无效参数
            response = await client.post(
                f"{GATEWAY_BASE_URL}/api/product-similarity/keyword-matching",
                json={
                    "keyword": "",  # 空关键词
                    "target_product_id": "invalid"  # 无效产品ID
                }
            )
            
            if response.status_code == 400 or response.status_code == 422:
                print("✅ 网关错误处理正常")
                print(f"   错误状态码: {response.status_code}")
                return True
            else:
                print(f"⚠️ 网关错误处理异常: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 网关错误处理测试异常: {str(e)}")
        return False

async def test_gateway_performance():
    """测试网关性能"""
    print("\n⚡ 测试网关性能...")
    
    try:
        async with httpx.AsyncClient(timeout=180) as client:
            tasks = []
            keywords = ["люстра", "светильник", "лампа", "освещение", "потолочный"]
            
            print(f"   🚀 启动 {len(keywords)} 个并发请求...")
            
            start_time = time.time()
            
            for keyword in keywords:
                task = client.post(
                    f"{GATEWAY_BASE_URL}/api/product-similarity/keyword-matching",
                    json={
                        "keyword": keyword,
                        "target_product_id": TARGET_PRODUCT_ID
                    }
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            success_count = 0
            for i, response in enumerate(responses):
                if isinstance(response, Exception):
                    print(f"   ❌ 请求 {i+1} 异常: {response}")
                elif response.status_code == 200:
                    success_count += 1
                else:
                    print(f"   ❌ 请求 {i+1} 失败: {response.status_code}")
            
            success_rate = (success_count / len(keywords)) * 100
            avg_time = total_time / len(keywords)
            
            print(f"   📊 性能测试结果:")
            print(f"   ✅ 成功率: {success_rate:.1f}% ({success_count}/{len(keywords)})")
            print(f"   ⏱️ 总时间: {total_time:.2f}秒")
            print(f"   ⏱️ 平均时间: {avg_time:.2f}秒/请求")
            
            return success_rate >= 80  # 80%以上成功率认为正常
            
    except Exception as e:
        print(f"❌ 网关性能测试异常: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🌐 网关访问测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    results = []
    
    # 执行各项测试
    results.append(await test_gateway_health())
    results.append(await test_gateway_keyword_matching())
    results.append(await test_direct_vs_gateway())
    results.append(await test_gateway_error_handling())
    results.append(await test_gateway_performance())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 网关测试结果汇总:")
    
    test_names = [
        "网关健康检查",
        "网关keywordMatching功能",
        "直接访问vs网关访问对比",
        "网关错误处理",
        "网关性能测试"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 网关访问测试全部通过！")
        print("\n📝 网关访问信息:")
        print("   健康检查: http://localhost/api/product-similarity/health")
        print("   关键词匹配: http://localhost/api/product-similarity/keyword-matching")
        print("   服务信息: http://localhost/api/product-similarity/info")
    elif passed >= total * 0.8:
        print("⚠️ 网关访问基本正常，部分功能需要优化")
    else:
        print("❌ 网关访问存在严重问题，请检查配置")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    asyncio.run(main())
