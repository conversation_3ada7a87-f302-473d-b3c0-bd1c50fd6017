# 产品比较功能使用说明

本项目提供了通过产品ID直接比较产品相似度的功能，结合了 `get_product_detail.py` 中的产品信息获取功能和 `analyzer.py` 中的AI产品比较功能。

## 功能特点

- **直接通过产品ID比较**：无需预先获取产品信息，直接输入两个产品ID即可获得相似度评分
- **智能评分系统**：基于1-100分的评分体系，从多个维度评估产品相似度
- **多种使用方式**：提供便捷函数和类方法两种使用方式
- **批量处理支持**：支持批量比较多个产品
- **详细分析说明**：不仅提供评分，还提供中文分析说明

## 评分维度

系统从以下5个维度评估产品相似度：

1. **1级类目相似性** (5-10分)：判断两个产品的主要类别是否相同
2. **3级类目相似性** (15-25分)：判断产品的细分类别是否相似
3. **标题相似性** (15-25分)：比较产品标题中的关键信息
4. **价格相似性** (10-15分)：评估价格区间的相似程度
5. **图片描述相似性** (20-35分)：分析产品外观、功能等视觉特征

## 快速开始

### 1. 使用便捷函数（推荐）

```python
from analyzer import compare_products_by_ids, EndpointConfig

# 配置API端点
text_endpoints = [
    EndpointConfig(
        url="http://************:3000",
        api_key="your-api-key",
        model="deepseek/deepseek-chat-v3-0324:free",
        is_multimodal=False
    )
]

# 比较两个产品
result = compare_products_by_ids(
    product_id1=123456789,
    product_id2=987654321,
    text_endpoints=text_endpoints,
    mode="text"
)

print(f"相似度评分: {result['similar_scores']}/100")
print(f"分析说明: {result['reson']}")
```

### 2. 使用类方法

```python
from analyzer import AIProductComparer, EndpointConfig

# 创建比较器实例
comparer = AIProductComparer(
    text_endpoints=text_endpoints,
    timeout=60,
    temperature=0.1
)

# 比较产品
result = comparer.compare_by_ids(
    product_id1=123456789,
    product_id2=987654321,
    mode="text"
)
```

## 参数说明

### compare_products_by_ids 函数参数

- `product_id1` (int): 第一个产品的ID
- `product_id2` (int): 第二个产品的ID
- `text_endpoints` (List[EndpointConfig]): 文本模型端点配置列表
- `mm_endpoints` (Optional[List[EndpointConfig]]): 多模态模型端点配置列表（可选）
- `mode` (str): 比较模式，可选值：
  - `"text"`: 仅使用文本模型（推荐）
  - `"multimodal"`: 仅使用多模态模型
  - `"both"`: 优先使用多模态，失败时降级到文本模型
- `convert` (bool): 是否将产品信息转换为markdown格式，默认False
- `timeout` (int): 请求超时时间（秒），默认120秒
- `temperature` (float): 模型温度参数，默认0.1

### EndpointConfig 配置

```python
EndpointConfig(
    url="http://api.example.com",      # API基础地址
    api_key="your-api-key",            # API密钥
    model="model-name",                # 模型名称
    is_multimodal=False                # 是否为多模态模型
)
```

## 返回结果格式

```python
{
    "similar_scores": 85,              # 相似度评分 (1-100)
    "reson": "两个产品在类目、标题等方面相似度较高，但价格存在一定差异"  # 中文分析说明
}
```

## 评分解读

- **80-100分**：高度相似，可能是同类产品或相近产品
- **60-79分**：中等相似，有一定关联性
- **40-59分**：低度相似，存在部分共同点
- **20-39分**：相似度较低，基本不相关
- **1-19分**：完全不相似，不相关产品

## 批量比较示例

```python
import random
from concurrent.futures import ThreadPoolExecutor, as_completed

# 产品ID列表
product_ids = [123456789, 987654321, 111222333, 444555666]

# 批量比较函数
def batch_compare(comparer, product_ids, max_comparisons=10):
    results = []
    
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = []
        
        # 生成比较任务
        for i in range(min(max_comparisons, len(product_ids) - 1)):
            id1, id2 = random.sample(product_ids, 2)
            future = executor.submit(comparer.compare_by_ids, id1, id2, mode="text")
            futures.append((future, id1, id2))
        
        # 收集结果
        for future, id1, id2 in futures:
            try:
                result = future.result()
                results.append({
                    "id1": id1,
                    "id2": id2,
                    "score": result["similar_scores"],
                    "reason": result["reson"]
                })
            except Exception as e:
                print(f"比较 {id1} vs {id2} 失败: {e}")
    
    return results
```

## 错误处理

常见错误及解决方案：

1. **产品信息获取失败**
   ```
   ValueError: 无法获取产品ID xxx 的信息
   ```
   - 检查产品ID是否正确
   - 确认网络连接正常
   - 验证产品是否存在

2. **API调用失败**
   ```
   requests.HTTPError: 429 Too Many Requests
   ```
   - 降低并发数量
   - 增加请求间隔
   - 检查API配额

3. **超时错误**
   ```
   requests.Timeout
   ```
   - 增加timeout参数值
   - 检查网络连接
   - 尝试使用其他端点

## 注意事项

1. **产品ID格式**：确保使用正确的产品ID格式（整数）
2. **API配置**：确保API端点地址、密钥和模型名称正确
3. **并发限制**：避免过高的并发请求，以免触发API限制
4. **网络环境**：确保能够访问产品信息API和AI模型API
5. **错误处理**：在生产环境中添加适当的错误处理和重试机制

## 示例文件

运行 `product_comparison_example.py` 查看完整的使用示例：

```bash
python product_comparison_example.py
```

该文件包含了多种使用场景的完整示例代码。
