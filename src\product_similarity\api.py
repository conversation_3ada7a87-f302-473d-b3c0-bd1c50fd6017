"""
FastAPI应用主模块
包含所有API端点、健康检查、服务信息等
"""
from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn

from .config import settings
from .service import product_similarity_service
from .consul_service import consul_service
from .service_client import service_client
from .db import init_pool, close_pool
from .logging import log_success, log_error, log_info, logger
from .schemas import (
    CompareRequest, CompareResponse, BatchCompareRequest, BatchCompareResponse,
    ProductBatchRequest, ProductBatchResponse, ProductInfoResponse,
    SimilarityListResponse, TopSimilarityResponse, HealthCheckResponse,
    ServiceInfoResponse, StatsResponse, ErrorResponse,
    KeywordMatchingRequest, KeywordMatchingResponse
)

# ==================== 应用生命周期管理 ====================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    log_info("正在启动产品相似度微服务...")
    
    try:
        # 初始化数据库连接池
        await init_pool()
        log_success("数据库连接池初始化完成")
        
        # 等待Consul可用
        consul_available = await consul_service.wait_for_consul(max_retries=10)
        if consul_available:
            # 注册到Consul
            success = await consul_service.register()
            if success:
                log_success("服务注册到Consul成功")
            else:
                log_error("服务注册到Consul失败")
        else:
            log_error("Consul不可用，跳过服务注册")
        
        log_success("产品相似度微服务启动完成")
        
    except Exception as e:
        log_error("服务启动失败", error=e)
        raise
    
    yield
    
    # 关闭时执行
    log_info("正在关闭产品相似度微服务...")
    
    try:
        # 从Consul注销服务
        await consul_service.deregister()
        log_success("服务从Consul注销完成")
        
        # 关闭服务客户端
        await service_client.close()
        log_success("服务客户端关闭完成")
        
        # 关闭数据库连接池
        await close_pool()
        log_success("数据库连接池关闭完成")
        
        log_success("产品相似度微服务关闭完成")
        
    except Exception as e:
        log_error("服务关闭异常", error=e)

# ==================== 创建FastAPI应用 ====================

app = FastAPI(
    title="产品相似度微服务",
    description="基于AI的产品相似度比较服务",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ==================== 异常处理 ====================

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status": "error",
            "message": exc.detail,
            "error_code": f"HTTP_{exc.status_code}"
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    log_error("API请求异常", error=exc)
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": "内部服务器错误",
            "error_code": "INTERNAL_ERROR"
        }
    )

# ==================== 健康检查和服务信息 ====================

@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """健康检查端点"""
    try:
        result = await product_similarity_service.health_check()
        status_code = 200 if result["status"] in ["healthy", "degraded"] else 503
        return JSONResponse(content=result, status_code=status_code)
    except Exception as e:
        log_error("健康检查失败", error=e)
        return JSONResponse(
            content={
                "status": "error",
                "service": settings.SERVICE_NAME,
                "version": "1.0.0",
                "checks": {"system": "error"},
                "message": str(e)
            },
            status_code=503
        )

@app.get("/info", response_model=ServiceInfoResponse)
async def service_info():
    """服务信息端点"""
    try:
        result = await product_similarity_service.get_service_info()
        return JSONResponse(content=result)
    except Exception as e:
        log_error("获取服务信息失败", error=e)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats", response_model=StatsResponse)
async def get_statistics():
    """获取服务统计信息"""
    try:
        result = await product_similarity_service.get_statistics()
        if result["status"] == "error":
            raise HTTPException(status_code=500, detail=result["message"])
        return JSONResponse(content=result)
    except Exception as e:
        log_error("获取统计信息失败", error=e)
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 产品信息相关端点 ====================

@app.get("/product/{product_id}", response_model=ProductInfoResponse)
async def get_product(
    product_id: int,
    force_refresh: bool = Query(default=False, description="是否强制刷新缓存")
):
    """获取单个产品信息"""
    try:
        result = await product_similarity_service.get_product_info(
            product_id, force_refresh=force_refresh
        )
        
        if result["status"] == "error":
            raise HTTPException(status_code=404, detail=result["message"])
        
        return JSONResponse(content=result)
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"获取产品信息失败: {product_id}", error=e)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/products/batch", response_model=ProductBatchResponse)
async def get_products_batch(request: ProductBatchRequest):
    """批量获取产品信息"""
    try:
        result = await product_similarity_service.get_products_batch_info(
            request.product_ids, force_refresh=request.force_refresh
        )
        
        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["message"])
        
        return JSONResponse(content=result)
    except HTTPException:
        raise
    except Exception as e:
        log_error("批量获取产品信息失败", error=e)
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 产品相似度比较端点 ====================

@app.post("/compare", response_model=CompareResponse)
async def compare_products(request: CompareRequest):
    """比较两个产品的相似度"""
    try:
        result = await product_similarity_service.compare_products(
            request.product_id1,
            request.product_id2,
            mode=request.mode,
            convert=request.convert
        )
        
        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["message"])
        
        return JSONResponse(content=result)
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"产品比较失败: {request.product_id1} vs {request.product_id2}", error=e)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/compare/batch", response_model=BatchCompareResponse)
async def batch_compare_products(request: BatchCompareRequest):
    """批量比较产品相似度"""
    try:
        # 转换产品对格式
        product_pairs = [(pair[0], pair[1]) for pair in request.product_pairs]
        
        result = await product_similarity_service.batch_compare_products_service(
            product_pairs,
            mode=request.mode,
            convert=request.convert,
            max_concurrent=request.max_concurrent
        )
        
        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["message"])
        
        return JSONResponse(content=result)
    except HTTPException:
        raise
    except Exception as e:
        log_error("批量产品比较失败", error=e)
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 相似度查询端点 ====================

@app.get("/product/{product_id}/similarities", response_model=SimilarityListResponse)
async def get_product_similarities(
    product_id: int,
    limit: int = Query(default=10, ge=1, le=100, description="返回结果数量限制")
):
    """获取产品的相似度列表"""
    try:
        result = await product_similarity_service.get_product_similarities(
            product_id, limit=limit
        )
        
        if result["status"] == "error":
            raise HTTPException(status_code=404, detail=result["message"])
        
        return JSONResponse(content=result)
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"获取产品相似度列表失败: {product_id}", error=e)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/similarities/top", response_model=TopSimilarityResponse)
async def get_top_similarities(
    limit: int = Query(default=20, ge=1, le=100, description="返回结果数量限制")
):
    """获取相似度最高的产品对"""
    try:
        result = await product_similarity_service.get_top_similarities(limit=limit)
        
        if result["status"] == "error":
            raise HTTPException(status_code=500, detail=result["message"])
        
        return JSONResponse(content=result)
    except HTTPException:
        raise
    except Exception as e:
        log_error("获取高相似度产品对失败", error=e)
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 关键词匹配端点 ====================

@app.post("/keyword-matching", response_model=KeywordMatchingResponse)
async def keyword_matching_analysis(request: KeywordMatchingRequest):
    """关键词匹配分析"""
    try:
        result = await product_similarity_service.keyword_matching_analysis(
            keyword=request.keyword,
            target_product_id=request.target_product_id
        )

        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["message"])

        return JSONResponse(content=result)
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"关键词匹配分析失败: {request.keyword} -> {request.target_product_id}", error=e)
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 根路径 ====================

@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "产品相似度微服务",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "health": "/health",
        "info": "/info"
    }

# ==================== 启动函数 ====================

def run_server():
    """启动服务器"""
    uvicorn.run(
        "src.product_similarity.api:app",
        host=settings.HOST,
        port=settings.PORT,
        log_level=settings.LOG_LEVEL.lower(),
        reload=False
    )

if __name__ == "__main__":
    run_server()
