#!/usr/bin/env python3
"""
测试智能Basket系统
验证动态探测和缓存功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail, _parse_product_url_smart, _detect_correct_basket
from product_similarity.crud import get_basket_stats, cleanup_basket_samples, add_basket_sample
from product_similarity.db import init_pool, close_pool
from product_similarity.logging import log_success, log_error, log_info
from test_product_ids import nm_ids

async def test_traditional_vs_smart():
    """测试传统算法与智能算法的对比"""
    log_info("开始测试传统算法与智能算法对比")
    
    # 选择几个测试产品ID
    test_ids = [int(nm_id) for nm_id in nm_ids[:10]]
    
    for nm_id in test_ids:
        try:
            log_info(f"测试产品ID: {nm_id}")
            
            # 使用智能算法解析URL
            smart_data = await _parse_product_url_smart(nm_id)
            log_info(f"智能算法结果: basket={smart_data['basket']}, confidence={smart_data['confidence']}, certain={smart_data['is_certain']}")
            
            # 尝试获取产品详情
            product_info = await get_product_detail(nm_id)
            if product_info:
                log_success(f"成功获取产品信息: {nm_id}")
            else:
                log_error(f"获取产品信息失败: {nm_id}")
                
        except Exception as e:
            log_error(f"测试产品ID {nm_id} 失败", error=e)
    
    log_info("传统算法与智能算法对比测试完成")

async def test_dynamic_detection():
    """测试动态探测功能"""
    log_info("开始测试动态探测功能")
    
    # 选择一个可能需要动态探测的产品ID
    test_id = int(nm_ids[0])
    
    try:
        log_info(f"测试动态探测产品ID: {test_id}")
        
        # 直接调用动态探测函数
        detected_basket = await _detect_correct_basket(test_id)
        if detected_basket:
            log_success(f"动态探测成功: product_id={test_id}, basket={detected_basket}")
        else:
            log_error(f"动态探测失败: product_id={test_id}")
            
    except Exception as e:
        log_error(f"动态探测测试失败", error=e)
    
    log_info("动态探测功能测试完成")

async def test_basket_samples():
    """测试basket样本管理"""
    log_info("开始测试basket样本管理")
    
    try:
        # 添加一些测试样本
        await add_basket_sample("21", 4500, confidence=1.0)
        await add_basket_sample("22", 4600, confidence=0.8)
        await add_basket_sample("23", 4700, confidence=0.1)
        
        log_success("添加测试样本成功")
        
        # 获取统计信息
        stats = await get_basket_stats()
        log_info(f"Basket统计信息: {stats}")
        
        # 清理低置信度样本
        cleaned = await cleanup_basket_samples()
        log_info(f"清理低置信度样本: {cleaned} 条")
        
        # 再次获取统计信息
        stats_after = await get_basket_stats()
        log_info(f"清理后统计信息: {stats_after}")
        
    except Exception as e:
        log_error("basket样本管理测试失败", error=e)
    
    log_info("basket样本管理测试完成")

async def test_batch_products():
    """批量测试产品获取"""
    log_info("开始批量测试产品获取")
    
    # 选择前20个产品进行测试
    test_ids = [int(nm_id) for nm_id in nm_ids[:20]]
    
    success_count = 0
    fail_count = 0
    
    for nm_id in test_ids:
        try:
            product_info = await get_product_detail(nm_id)
            if product_info:
                success_count += 1
                log_success(f"成功: {nm_id}")
            else:
                fail_count += 1
                log_error(f"失败: {nm_id}")
                
        except Exception as e:
            fail_count += 1
            log_error(f"异常: {nm_id}", error=e)
    
    log_info(f"批量测试完成: 成功 {success_count}, 失败 {fail_count}")
    
    # 获取最终统计
    final_stats = await get_basket_stats()
    log_info(f"最终Basket统计: {final_stats}")

async def main():
    """主测试函数"""
    try:
        # 初始化数据库连接
        await init_pool()
        log_success("数据库连接初始化成功")
        
        # 运行各项测试
        await test_basket_samples()
        await test_traditional_vs_smart()
        await test_dynamic_detection()
        await test_batch_products()
        
        log_success("所有测试完成")
        
    except Exception as e:
        log_error("测试过程中发生错误", error=e)
    finally:
        # 关闭数据库连接
        await close_pool()
        log_info("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(main())
