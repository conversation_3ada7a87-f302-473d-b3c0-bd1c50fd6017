#!/usr/bin/env python3
"""
调试样本记录问题
"""

import asyncio
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 设置详细的日志级别
logging.basicConfig(level=logging.DEBUG)

from product_similarity.services.product import get_product_detail
from product_similarity.crud import get_basket_stats, add_basket_sample
from product_similarity.logging import log_info, log_error, log_success, log_warning, log_debug

async def test_direct_sample_recording():
    """直接测试样本记录功能"""
    log_info("=== 直接测试样本记录功能 ===")
    
    # 获取初始统计
    initial_stats = await get_basket_stats()
    initial_samples = initial_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"初始样本数: {initial_samples}")
    
    # 直接添加一个测试样本
    test_basket = "99"
    test_short_id = 9999
    
    log_info(f"直接添加测试样本: basket={test_basket}, short_id={test_short_id}")
    success = await add_basket_sample(test_basket, test_short_id)
    log_info(f"添加结果: {success}")
    
    # 检查样本数是否增加
    after_stats = await get_basket_stats()
    after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"添加后样本数: {after_samples}")
    
    if after_samples > initial_samples:
        log_success(f"✅ 直接添加成功: {initial_samples} → {after_samples}")
    else:
        log_warning(f"⚠️ 直接添加失败或重复: {initial_samples} → {after_samples}")

async def test_product_with_debug():
    """测试产品获取并开启调试"""
    log_info("\n=== 测试产品获取（调试模式） ===")
    
    # 选择一个测试产品
    test_product_id = 159973580  # 从之前的测试中选择一个
    
    log_info(f"测试产品ID: {test_product_id}")
    
    # 获取处理前统计
    before_stats = await get_basket_stats()
    before_samples = before_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"处理前样本数: {before_samples}")
    
    try:
        # 获取产品信息
        log_info("开始获取产品信息...")
        product_info = await get_product_detail(test_product_id)
        
        if product_info:
            log_success(f"产品获取成功: {test_product_id}")
            
            # 获取处理后统计
            after_stats = await get_basket_stats()
            after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
            log_info(f"处理后样本数: {after_samples}")
            
            if after_samples > before_samples:
                log_success(f"✅ 样本已记录: {before_samples} → {after_samples}")
            else:
                log_warning(f"⚠️ 样本未记录: {before_samples} → {after_samples}")
        else:
            log_error("产品信息为空")
            
    except Exception as e:
        log_error(f"获取产品失败: {test_product_id}", error=e)

async def test_manual_sample_add():
    """手动测试样本添加"""
    log_info("\n=== 手动测试样本添加 ===")
    
    # 使用一个真实的产品ID计算其basket和short_id
    test_product_id = 159973580
    short_id = test_product_id // 100000  # 1599
    
    # 使用传统算法计算basket
    from product_similarity.services.product import _parse_product_url
    traditional_data = _parse_product_url(test_product_id)
    basket = traditional_data["basket"]
    
    log_info(f"产品ID: {test_product_id}")
    log_info(f"Short ID: {short_id}")
    log_info(f"传统算法计算的basket: {basket}")
    
    # 获取添加前统计
    before_stats = await get_basket_stats()
    before_samples = before_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"添加前样本数: {before_samples}")
    
    # 手动添加样本
    log_info(f"手动添加样本: basket={basket}, short_id={short_id}")
    success = await add_basket_sample(basket, short_id)
    log_info(f"添加操作结果: {success}")
    
    # 获取添加后统计
    after_stats = await get_basket_stats()
    after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"添加后样本数: {after_samples}")
    
    if after_samples > before_samples:
        log_success(f"✅ 手动添加成功: {before_samples} → {after_samples}")
    else:
        log_info(f"ℹ️ 样本数未变化: {before_samples} → {after_samples} (可能是重复样本)")

async def main():
    """主测试函数"""
    log_info("开始调试样本记录问题")
    
    await test_direct_sample_recording()
    await test_manual_sample_add()
    await test_product_with_debug()
    
    log_success("调试测试完成")

if __name__ == "__main__":
    asyncio.run(main())
