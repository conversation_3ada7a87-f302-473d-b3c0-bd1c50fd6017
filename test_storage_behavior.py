#!/usr/bin/env python3
"""
测试存储行为 - 验证缓存和数据库存储逻辑
"""
import asyncio
import time
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.product_similarity.services.keyword_matching import keywordMatching
from src.product_similarity.db import init_pool, close_pool, get_pool

# 测试关键词 - 使用一些新的关键词来避免缓存
TEST_KEYWORDS = [
    f"люстра тест {datetime.now().strftime('%H%M%S')}_1",
    f"светильник тест {datetime.now().strftime('%H%M%S')}_2", 
    f"лампа тест {datetime.now().strftime('%H%M%S')}_3",
    f"плафон тест {datetime.now().strftime('%H%M%S')}_4",
    f"освещение тест {datetime.now().strftime('%H%M%S')}_5"
]

TARGET_PRODUCT_ID = 253486273


async def check_database_before_test():
    """测试前检查数据库状态"""
    print("📊 测试前数据库状态:")
    pool = await get_pool()
    async with pool.acquire() as conn:
        count = await conn.fetchval('SELECT COUNT(*) FROM pj_similar.product_analyze_similar_result')
        print(f"  当前记录数: {count}")
        
        # 检查今天的记录
        today_count = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM pj_similar.product_analyze_similar_result 
            WHERE DATE(created_at) = CURRENT_DATE
        """)
        print(f"  今天的记录数: {today_count}")
    return count


async def check_database_after_test(initial_count: int):
    """测试后检查数据库状态"""
    print("\n📊 测试后数据库状态:")
    pool = await get_pool()
    async with pool.acquire() as conn:
        final_count = await conn.fetchval('SELECT COUNT(*) FROM pj_similar.product_analyze_similar_result')
        print(f"  最终记录数: {final_count}")
        print(f"  新增记录数: {final_count - initial_count}")
        
        # 查看最新的记录
        if final_count > initial_count:
            print("\n  新增的记录:")
            new_records = await conn.fetch("""
                SELECT keyword, target_product_id, avg_similarity, created_at
                FROM pj_similar.product_analyze_similar_result 
                ORDER BY created_at DESC 
                LIMIT $1
            """, final_count - initial_count)
            
            for i, record in enumerate(new_records, 1):
                print(f"    {i}. '{record['keyword']}' -> {record['target_product_id']} | 相似度: {record['avg_similarity']}分 | 时间: {record['created_at']}")


async def test_single_keyword_storage(keyword: str, target_id: int, test_num: int):
    """测试单个关键词的存储行为"""
    print(f"\n🔍 [测试{test_num}] 测试关键词: '{keyword}'")
    
    # 第一次调用 - 应该执行完整流程并存储到数据库
    print("  第一次调用 (应该存储到数据库)...")
    start_time = time.time()
    result1 = await keywordMatching(keyword, target_id)
    duration1 = time.time() - start_time
    
    if result1['status'] == 'success':
        data1 = result1['data']
        from_cache1 = data1.get('from_cache', False)
        print(f"    ✅ 成功 | 耗时: {duration1:.2f}s | 相似度: {data1['avg_similarity']}分 | 缓存: {'是' if from_cache1 else '否'}")
    else:
        print(f"    ❌ 失败: {result1.get('message', '未知错误')}")
        return
    
    # 等待一小段时间确保数据库写入完成
    await asyncio.sleep(0.1)
    
    # 第二次调用 - 应该从数据库缓存中获取
    print("  第二次调用 (应该从数据库缓存获取)...")
    start_time = time.time()
    result2 = await keywordMatching(keyword, target_id)
    duration2 = time.time() - start_time
    
    if result2['status'] == 'success':
        data2 = result2['data']
        from_cache2 = data2.get('from_cache', False)
        print(f"    ✅ 成功 | 耗时: {duration2:.2f}s | 相似度: {data2['avg_similarity']}分 | 缓存: {'是' if from_cache2 else '否'}")
        
        # 验证两次结果是否一致
        if data1['avg_similarity'] == data2['avg_similarity'] and from_cache2:
            print(f"    🎯 缓存验证成功: 两次结果一致且第二次来自缓存")
        else:
            print(f"    ⚠️ 缓存验证异常: 结果不一致或缓存标记错误")
    else:
        print(f"    ❌ 失败: {result2.get('message', '未知错误')}")


async def test_concurrent_same_keyword():
    """测试并发访问同一关键词的行为"""
    print(f"\n🔄 测试并发访问同一关键词...")
    
    # 使用一个新的关键词
    concurrent_keyword = f"并发测试 {datetime.now().strftime('%H%M%S%f')}"
    
    # 创建5个并发任务
    tasks = []
    for i in range(5):
        task = keywordMatching(concurrent_keyword, TARGET_PRODUCT_ID)
        tasks.append(task)
    
    print(f"  同时启动5个任务访问关键词: '{concurrent_keyword}'")
    start_time = time.time()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    total_duration = time.time() - start_time
    
    print(f"  总耗时: {total_duration:.2f}s")
    
    # 分析结果
    success_count = 0
    cache_count = 0
    for i, result in enumerate(results, 1):
        if isinstance(result, Exception):
            print(f"    任务{i}: ❌ 异常 - {str(result)}")
        elif result['status'] == 'success':
            success_count += 1
            data = result['data']
            from_cache = data.get('from_cache', False)
            if from_cache:
                cache_count += 1
            print(f"    任务{i}: ✅ 成功 | 相似度: {data['avg_similarity']}分 | 缓存: {'是' if from_cache else '否'}")
        else:
            print(f"    任务{i}: ❌ 失败 - {result.get('message', '未知错误')}")
    
    print(f"  结果统计: 成功{success_count}/5, 缓存命中{cache_count}/5")


async def run_storage_behavior_test():
    """运行存储行为测试"""
    print("🧪 存储行为测试开始")
    print("=" * 80)
    print(f"🎯 目标产品ID: {TARGET_PRODUCT_ID}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 初始化数据库连接池
        await init_pool()
        print("✅ 数据库连接池初始化完成")
        
        # 检查测试前的数据库状态
        initial_count = await check_database_before_test()
        
        # 测试1: 单个关键词的存储行为
        print(f"\n{'='*60}")
        print("📝 测试1: 单个关键词的存储行为")
        print(f"{'='*60}")
        
        for i, keyword in enumerate(TEST_KEYWORDS, 1):
            await test_single_keyword_storage(keyword, TARGET_PRODUCT_ID, i)
        
        # 测试2: 并发访问同一关键词
        print(f"\n{'='*60}")
        print("📝 测试2: 并发访问同一关键词")
        print(f"{'='*60}")
        
        await test_concurrent_same_keyword()
        
        # 检查测试后的数据库状态
        await check_database_after_test(initial_count)
        
        print(f"\n{'='*80}")
        print("📈 存储行为测试总结")
        print(f"{'='*80}")
        print("✅ 测试完成，验证了以下行为:")
        print("  1. 新关键词会执行完整流程并存储到数据库")
        print("  2. 重复关键词会从数据库缓存中快速获取")
        print("  3. 并发访问同一关键词的处理机制")
        print("  4. 数据库存储的一致性和正确性")
        
        print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("🔒 数据库连接池已关闭")


if __name__ == "__main__":
    asyncio.run(run_storage_behavior_test())
