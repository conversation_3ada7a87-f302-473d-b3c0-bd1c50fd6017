# ==================== Web框架 ====================
fastapi==0.111.0
uvicorn[standard]==0.29.0

# ==================== 数据库 ====================
asyncpg==0.29.0

# ==================== HTTP客户端 ====================
httpx==0.27.0
aiohttp==3.9.5

# ==================== 数据验证和序列化 ====================
pydantic==2.7.1
pydantic-settings==2.3.4

# ==================== 环境变量管理 ====================
python-dotenv==1.0.1

# ==================== 日志和颜色输出 ====================
python-json-logger==2.0.7
colorama==0.4.6

# ==================== Redis客户端 ====================
redis[hiredis]==5.0.7

# ==================== 重试机制 ====================
tenacity==8.4.2

# ==================== 文档转换 ====================
# markdownify==0.12.1  # 已在上面定义

# ==================== 测试框架 ====================
pytest==8.2.2
pytest-asyncio==0.23.7
pytest-mock==3.14.0

# ==================== HTTP请求 (用于AI调用) ====================
requests==2.32.3

# ==================== 数据处理 ====================
markdownify==0.12.1

# ==================== 类型检查 ====================
mypy==1.10.1
types-requests==2.32.0.20240622

# ==================== 代码格式化 ====================
black==24.4.2
isort==5.13.2

# ==================== 代码检查 ====================
flake8==7.1.0
pylint==3.2.5

# ==================== 安全检查 ====================
bandit==1.7.9

# ==================== 依赖说明 ====================
#
# 核心依赖：
# - fastapi: Web框架
# - uvicorn: ASGI服务器
# - asyncpg: PostgreSQL异步客户端
# - httpx: 异步HTTP客户端
# - aiohttp: 异步HTTP客户端（用于Consul）
# - pydantic: 数据验证
# - python-dotenv: 环境变量管理
# - colorama: 终端颜色输出
#
# 可选依赖：
# - redis: Redis客户端（如果使用Redis缓存）
# - tenacity: 重试机制
# - markdownify: Markdown转换
# - requests: 同步HTTP请求（AI调用）
#
# 开发依赖：
# - pytest: 测试框架
# - mypy: 类型检查
# - black: 代码格式化
# - flake8: 代码检查
# - bandit: 安全检查
#
# 版本说明：
# - 使用固定版本号确保环境一致性
# - 定期更新依赖版本以获取安全修复
# - 测试新版本兼容性后再升级生产环境
