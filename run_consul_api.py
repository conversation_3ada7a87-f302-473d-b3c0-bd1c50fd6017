#!/usr/bin/env python3
"""
产品相似度微服务启动脚本
支持Consul集成和优雅关闭
"""
import asyncio
import signal
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.product_similarity.api import run_server
from src.product_similarity.config import settings
from src.product_similarity.logging import log_success, log_error, log_info, logger

class ServiceRunner:
    """服务运行器"""
    
    def __init__(self):
        self.shutdown_event = asyncio.Event()
        self.server_task = None
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            log_info(f"接收到信号 {signum}，开始优雅关闭...")
            self.shutdown_event.set()
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # Docker stop
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)  # 重新加载配置
    
    async def run_async_server(self):
        """异步运行服务器"""
        import uvicorn
        from src.product_similarity.api import app
        
        config = uvicorn.Config(
            app=app,
            host=settings.HOST,
            port=settings.PORT,
            log_level=settings.LOG_LEVEL.lower(),
            reload=False,
            access_log=True
        )
        
        server = uvicorn.Server(config)
        
        # 在后台任务中运行服务器
        server_task = asyncio.create_task(server.serve())
        
        try:
            # 等待关闭信号
            await self.shutdown_event.wait()
            log_info("开始关闭服务器...")
            
            # 优雅关闭服务器
            server.should_exit = True
            await server_task
            
        except Exception as e:
            log_error("服务器运行异常", error=e)
            raise
    
    def run(self):
        """运行服务"""
        log_info("=" * 60)
        log_info("🚀 启动产品相似度微服务")
        log_info("=" * 60)
        log_info(f"服务名称: {settings.SERVICE_NAME}")
        log_info(f"服务地址: {settings.HOST}:{settings.PORT}")
        log_info(f"运行环境: {settings.ENVIRONMENT}")
        log_info(f"日志级别: {settings.LOG_LEVEL}")
        log_info(f"Consul地址: {settings.CONSUL_HOST}:{settings.CONSUL_PORT}")
        log_info("=" * 60)
        
        try:
            # 设置信号处理器
            self.setup_signal_handlers()
            
            # 检查必要的环境变量
            self.check_environment()
            
            # 运行异步服务器
            if sys.version_info >= (3, 7):
                asyncio.run(self.run_async_server())
            else:
                # Python 3.6 兼容性
                loop = asyncio.get_event_loop()
                loop.run_until_complete(self.run_async_server())
                loop.close()
            
            log_success("服务已优雅关闭")
            
        except KeyboardInterrupt:
            log_info("接收到键盘中断，正在关闭...")
        except Exception as e:
            log_error("服务启动失败", error=e)
            sys.exit(1)
    
    def check_environment(self):
        """检查环境配置"""
        log_info("检查环境配置...")
        
        # 检查数据库配置
        if not all([settings.PG_HOST, settings.PG_USER, settings.PG_DB]):
            log_error("数据库配置不完整，请检查环境变量")
            sys.exit(1)
        
        # 检查AI端点配置
        openai_endpoints = settings.get_openai_endpoints()
        if not openai_endpoints:
            log_error("未配置AI端点，请检查OPENAI_CREDENTIALS或TEXT_ENDPOINTS环境变量")
            sys.exit(1)
        
        log_success("环境配置检查通过")

def main():
    """主函数"""
    # 确保工作目录正确
    os.chdir(project_root)
    
    # 创建并运行服务
    runner = ServiceRunner()
    runner.run()

if __name__ == "__main__":
    main()
