version: '3.9'

# 产品相似度业务服务
# 连接到已存在的微服务基础设施

networks:
  microservices:
    name: microservices
    external: true

services:
  # ==================== 产品相似度微服务 ====================
  # keywordMatching业务服务
  # 功能：关键词匹配、产品相似度分析、图片描述集成
  product-similarity:
    build: .
    container_name: product-similarity-server
    hostname: product-similarity
    environment:
      # Consul配置 - 连接到主服务的Consul
      - CONSUL_HOST=${CONSUL_HOST}
      - CONSUL_PORT=${CONSUL_PORT}

      # 服务配置
      - SERVICE_NAME=${SERVICE_NAME}
      - SERVICE_PORT=${SERVICE_PORT}
      - SERVICE_TAGS=${SERVICE_TAGS}
      - ENVIRONMENT=${ENVIRONMENT}

      # 服务器配置
      - HOST=${HOST}
      - PORT=${PORT}
      - LOG_LEVEL=${LOG_LEVEL}

      # 数据库配置 - 连接到主服务的数据库或外部数据库
      - PG_HOST=${PG_HOST}
      - PG_PORT=${PG_PORT}
      - PG_USER=${PG_USER}
      - PG_PASSWORD=${PG_PASSWORD}
      - PG_DB=${PG_DB}

      # Redis配置 - 连接到主服务的Redis或外部Redis
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=${REDIS_DB}

      # AI配置 - 从环境变量读取
      - TEXT_ENDPOINTS=${TEXT_ENDPOINTS}
      - OPENAI_CREDENTIALS=${OPENAI_CREDENTIALS}

      # 业务配置
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS}
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT}
      - CACHE_TTL=${CACHE_TTL}
      - AI_TIMEOUT=${AI_TIMEOUT}
      - AI_TEMPERATURE=${AI_TEMPERATURE}

    env_file:
      - .env
    ports:
      - "${SERVICE_PORT}:${PORT}"  # 使用环境变量配置端口映射
    networks:
      - microservices
    healthcheck:
      # 使用Python urllib进行健康检查（避免curl依赖问题）
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8001/health')"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    command: ["python", "run_consul_api.py"]

  # ==================== 可选：独立数据库（如果不使用主服务数据库）====================
  # product-similarity-postgres:
  #   image: postgres:16-alpine
  #   container_name: product-similarity-postgres
  #   hostname: product-similarity-postgres
  #   environment:
  #     - POSTGRES_DB=product_similarity
  #     - POSTGRES_USER=similarity_user
  #     - POSTGRES_PASSWORD=similarity_password
  #     - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
  #   volumes:
  #     - product_similarity_data:/var/lib/postgresql/data
  #     - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U similarity_user -d product_similarity"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #     start_period: 30s
  #   restart: unless-stopped

# volumes:
#   product_similarity_data:
#     driver: local

# ==================== 使用说明 ====================
#
# 1. 确保主服务（基础设施）正在运行：
#    docker-compose -f docker-compose.infrastructure.yml ps
#
# 2. 配置环境变量：
#    cp .env.business.example .env
#    # 编辑 .env 文件，配置数据库连接、AI端点等
#
# 3. 构建并启动业务服务：
#    docker-compose -f docker-compose.business.yml up --build -d
#
# 4. 查看服务状态：
#    docker-compose -f docker-compose.business.yml ps
#
# 5. 查看服务日志：
#    docker-compose -f docker-compose.business.yml logs -f product-similarity
#
# 6. 验证服务注册：
#    curl http://localhost:8500/v1/catalog/service/product-similarity
#
# 7. 通过网关访问服务：
#    curl http://localhost/api/product-similarity/health
#    curl http://localhost/api/product-similarity/info
#
# 8. 停止业务服务：
#    docker-compose -f docker-compose.business.yml down
#
# 9. 扩展服务实例（负载均衡测试）：
#    docker-compose -f docker-compose.business.yml up -d --scale product-similarity=3
#
# 10. 跨主机部署：
#     - 修改 .env 中的 CONSUL_HOST、PG_HOST、REDIS_HOST 为主服务IP
#     - 确保网络连通性
#     - 创建相同名称的外部网络：docker network create microservices
#
# ==================== 部署验证 ====================
#
# 验证部署是否成功：
#    python verify_current_deployment.py
#
# 快速功能测试：
#    python quick_test.py
#
# 网关访问测试：
#    python test_gateway_access.py
#
# ==================== 更新历史 ====================
#
# 2025-07-30:
# - 修复健康检查配置（使用Python urllib而非curl）
# - 集成图片描述API功能
# - 修复相似度计算字段名不匹配问题
# - 添加数据验证防止0值存储
# - 完善错误处理和日志记录
# - 验证网关路由和服务注册
