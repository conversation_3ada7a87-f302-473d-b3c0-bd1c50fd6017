#!/usr/bin/env python3
"""
测试重构后的智能Basket系统（无置信度版本）
"""

import asyncio
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.crud import (
    add_basket_sample, 
    get_basket_by_short_id, 
    cleanup_basket_samples,
    get_basket_stats
)
from product_similarity.services.product import get_product_detail
from product_similarity.db import get_pool
from product_similarity.logging import log_info, log_error, log_success, log_warning

async def test_basket_crud():
    """测试Basket CRUD操作"""
    log_info("=== 测试Basket CRUD操作 ===")
    
    try:
        # 测试添加样本
        success = await add_basket_sample("13", 1999)
        log_info(f"添加样本结果: {success}")
        
        success = await add_basket_sample("13", 2000)
        log_info(f"添加样本结果: {success}")
        
        success = await add_basket_sample("13", 2001)
        log_info(f"添加样本结果: {success}")
        
        # 测试查询
        result = await get_basket_by_short_id(2000)
        log_info(f"查询结果: {result}")
        
        # 测试统计
        stats = await get_basket_stats()
        log_info(f"统计信息: {stats}")
        
        log_success("Basket CRUD测试完成")
        
    except Exception as e:
        log_error("Basket CRUD测试失败", error=e)

async def test_product_fetch():
    """测试产品获取功能"""
    log_info("=== 测试产品获取功能 ===")
    
    # 从test_product_ids.py获取一些测试ID
    try:
        from test_product_ids import product_ids
        test_ids = product_ids[:10]  # 测试前10个
    except ImportError:
        log_warning("无法导入test_product_ids，使用默认测试ID")
        test_ids = [199999999, 200000001, 200000002]
    
    success_count = 0
    total_count = len(test_ids)
    
    for nm_id in test_ids:
        try:
            log_info(f"测试产品ID: {nm_id}")
            product_info = await get_product_detail(nm_id)
            
            if product_info:
                log_success(f"成功获取产品信息: {nm_id}")
                success_count += 1
            else:
                log_warning(f"产品信息为空: {nm_id}")
                
        except Exception as e:
            log_error(f"获取产品信息失败: {nm_id}", error=e)
    
    log_info(f"产品获取测试完成: {success_count}/{total_count} 成功")

async def test_basket_range_expansion():
    """测试basket范围动态扩展"""
    log_info("=== 测试Basket范围动态扩展 ===")
    
    try:
        # 添加一些样本来建立初始范围
        await add_basket_sample("14", 2100)
        await add_basket_sample("14", 2101)
        await add_basket_sample("14", 2102)
        
        # 查看当前范围
        stats = await get_basket_stats()
        log_info(f"初始统计: {stats}")
        
        # 添加超出范围的样本
        await add_basket_sample("14", 2050)  # 小于最小值
        await add_basket_sample("14", 2150)  # 大于最大值
        
        # 查看范围是否扩展
        stats = await get_basket_stats()
        log_info(f"扩展后统计: {stats}")
        
        # 测试查询
        result = await get_basket_by_short_id(2075)  # 在新范围内
        log_info(f"范围内查询结果: {result}")
        
        result = await get_basket_by_short_id(2200)  # 超出范围
        log_info(f"范围外查询结果: {result}")
        
        log_success("Basket范围扩展测试完成")
        
    except Exception as e:
        log_error("Basket范围扩展测试失败", error=e)

async def test_database_schema():
    """测试数据库表结构"""
    log_info("=== 测试数据库表结构 ===")
    
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            # 检查basket_samples表结构
            samples_columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'pj_similar' 
                AND table_name = 'basket_samples'
                ORDER BY ordinal_position
            """)
            
            log_info("basket_samples表结构:")
            for col in samples_columns:
                log_info(f"  {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
            
            # 检查basket_mapping表结构
            mapping_columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'pj_similar' 
                AND table_name = 'basket_mapping'
                ORDER BY ordinal_position
            """)
            
            log_info("basket_mapping表结构:")
            for col in mapping_columns:
                log_info(f"  {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
            
            # 测试存储过程
            result = await conn.fetchval("SELECT pj_similar.recalculate_basket_ranges()")
            log_info(f"重新计算范围结果: {result}")
            
            result = await conn.fetchval("SELECT pj_similar.cleanup_old_samples()")
            log_info(f"清理过期样本结果: {result}")
            
        log_success("数据库表结构测试完成")
        
    except Exception as e:
        log_error("数据库表结构测试失败", error=e)

async def main():
    """主测试函数"""
    log_info("开始测试重构后的智能Basket系统")
    
    # 运行所有测试
    await test_database_schema()
    await test_basket_crud()
    await test_basket_range_expansion()
    await test_product_fetch()
    
    log_success("所有测试完成")

if __name__ == "__main__":
    asyncio.run(main())
