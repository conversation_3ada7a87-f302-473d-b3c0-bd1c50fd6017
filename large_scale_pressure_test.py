#!/usr/bin/env python3
"""
大规模压力测试
基于稳定版测试成功，进行更大规模的并发测试
"""

import asyncio
import aiohttp
import json
import time
import random
from test_keywords import keyword_info

class LargeScalePressureTest:
    def __init__(self, base_url: str = "http://localhost:8001", target_product_id: int = 253486273):
        self.base_url = base_url
        self.target_product_id = target_product_id
        self.results = []
        self.start_time = None
        self.end_time = None
        
    async def single_keyword_test(self, session, keyword: str, test_id: int, semaphore) -> dict:
        """单个关键词测试"""
        async with semaphore:
            request_data = {
                "keyword": keyword,
                "target_product_id": self.target_product_id
            }
            
            request_start = time.time()
            
            try:
                async with session.post(
                    f"{self.base_url}/keyword-matching",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时
                ) as response:
                    request_end = time.time()
                    processing_time = request_end - request_start
                    
                    if response.status == 200:
                        result = await response.json()
                        data = result.get('data', {})
                        
                        avg_similarity = data.get('avg_similarity', 0)
                        valid_scores = data.get('valid_scores', 0)
                        data_valid = avg_similarity > 0 and valid_scores > 0
                        
                        return {
                            'test_id': test_id,
                            'keyword': keyword,
                            'success': True,
                            'processing_time': processing_time,
                            'avg_similarity': avg_similarity,
                            'valid_scores': valid_scores,
                            'similar_count': data.get('similar_count', 0),
                            'competitor_count': data.get('competitor_count', 0),
                            'from_cache': data.get('from_cache', False),
                            'data_valid': data_valid,
                            'error': None
                        }
                    else:
                        error_text = await response.text()
                        return {
                            'test_id': test_id,
                            'keyword': keyword,
                            'success': False,
                            'processing_time': processing_time,
                            'error': f"HTTP {response.status}: {error_text[:100]}",
                            'data_valid': False
                        }
                        
            except asyncio.TimeoutError:
                request_end = time.time()
                processing_time = request_end - request_start
                return {
                    'test_id': test_id,
                    'keyword': keyword,
                    'success': False,
                    'processing_time': processing_time,
                    'error': "Timeout",
                    'data_valid': False
                }
            except Exception as e:
                request_end = time.time()
                processing_time = request_end - request_start
                return {
                    'test_id': test_id,
                    'keyword': keyword,
                    'success': False,
                    'processing_time': processing_time,
                    'error': str(e)[:100],
                    'data_valid': False
                }
    
    async def large_scale_test(self, keywords: list, max_concurrent: int = 50) -> list:
        """大规模测试"""
        print(f"🚀 开始大规模压力测试")
        print(f"📊 测试参数:")
        print(f"   关键词数量: {len(keywords)}")
        print(f"   最大并发数: {max_concurrent}")
        print(f"   目标产品ID: {self.target_product_id}")
        print("=" * 60)
        
        self.start_time = time.time()
        
        # 创建连接器和会话
        connector = aiohttp.TCPConnector(
            limit=max_concurrent * 2,  # 总连接数
            limit_per_host=max_concurrent,  # 每个主机的连接数
            ttl_dns_cache=300,  # DNS缓存
            use_dns_cache=True
        )
        
        async with aiohttp.ClientSession(connector=connector) as session:
            # 创建信号量控制并发数
            semaphore = asyncio.Semaphore(max_concurrent)
            
            # 创建所有任务
            tasks = [
                self.single_keyword_test(session, keyword, i + 1, semaphore)
                for i, keyword in enumerate(keywords)
            ]
            
            # 执行所有任务并显示进度
            completed = 0
            results = []
            
            print("🚀 开始执行测试...")
            print(f"进度更新间隔: 每完成{max(10, len(keywords)//20)}个任务")
            
            for coro in asyncio.as_completed(tasks):
                try:
                    result = await coro
                    results.append(result)
                    completed += 1
                    
                    # 显示进度
                    progress_interval = max(10, len(keywords) // 20)
                    if completed % progress_interval == 0 or completed == len(tasks):
                        elapsed = time.time() - self.start_time
                        progress = completed / len(tasks) * 100
                        rate = completed / elapsed if elapsed > 0 else 0
                        
                        # 统计当前状态
                        current_success = sum(1 for r in results if r['success'])
                        current_valid = sum(1 for r in results if r.get('data_valid', False))
                        current_cached = sum(1 for r in results if r.get('from_cache', False))
                        
                        print(f"📈 进度: {completed}/{len(tasks)} ({progress:.1f}%) | "
                              f"耗时: {elapsed:.1f}s | 速率: {rate:.2f}/s | "
                              f"成功: {current_success} | 有效: {current_valid} | 缓存: {current_cached}")
                        
                except Exception as e:
                    print(f"❌ 任务异常: {e}")
                    completed += 1
        
        self.end_time = time.time()
        self.results = results
        
        return results
    
    def analyze_results(self) -> dict:
        """分析测试结果"""
        if not self.results:
            return {}
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r['success'])
        valid_data_tests = sum(1 for r in self.results if r.get('data_valid', False))
        cached_results = sum(1 for r in self.results if r.get('from_cache', False))
        new_requests = total_tests - cached_results
        
        # 时间统计
        total_time = self.end_time - self.start_time if self.start_time and self.end_time else 0
        processing_times = [r['processing_time'] for r in self.results if r['success']]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # 新请求时间统计
        new_processing_times = [r['processing_time'] for r in self.results if r['success'] and not r.get('from_cache', True)]
        avg_new_processing_time = sum(new_processing_times) / len(new_processing_times) if new_processing_times else 0
        
        # 缓存请求时间统计
        cached_processing_times = [r['processing_time'] for r in self.results if r['success'] and r.get('from_cache', False)]
        avg_cached_processing_time = sum(cached_processing_times) / len(cached_processing_times) if cached_processing_times else 0
        
        # 相似度统计
        similarities = [r.get('avg_similarity', 0) for r in self.results if r.get('data_valid', False)]
        avg_similarity = sum(similarities) / len(similarities) if similarities else 0
        min_similarity = min(similarities) if similarities else 0
        max_similarity = max(similarities) if similarities else 0
        
        # 有效评分统计
        valid_scores = [r.get('valid_scores', 0) for r in self.results if r.get('data_valid', False)]
        avg_valid_scores = sum(valid_scores) / len(valid_scores) if valid_scores else 0
        
        # 错误统计
        error_types = {}
        for r in self.results:
            if not r['success'] and r.get('error'):
                error_type = r['error'].split(':')[0] if ':' in r['error'] else r['error']
                error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'valid_data_tests': valid_data_tests,
            'success_rate': successful_tests / total_tests * 100 if total_tests > 0 else 0,
            'data_validity_rate': valid_data_tests / total_tests * 100 if total_tests > 0 else 0,
            'cache_hit_rate': cached_results / total_tests * 100 if total_tests > 0 else 0,
            'new_requests': new_requests,
            'cached_requests': cached_results,
            'total_time': total_time,
            'avg_processing_time': avg_processing_time,
            'avg_new_processing_time': avg_new_processing_time,
            'avg_cached_processing_time': avg_cached_processing_time,
            'avg_similarity': avg_similarity,
            'min_similarity': min_similarity,
            'max_similarity': max_similarity,
            'avg_valid_scores': avg_valid_scores,
            'throughput': total_tests / total_time if total_time > 0 else 0,
            'error_types': error_types
        }
    
    def print_analysis(self, analysis: dict):
        """打印分析结果"""
        print("\n" + "=" * 60)
        print("📊 大规模压力测试结果分析")
        print("=" * 60)
        
        print(f"📈 基础统计:")
        print(f"   总测试数: {analysis['total_tests']}")
        print(f"   成功请求: {analysis['successful_tests']} ({analysis['success_rate']:.1f}%)")
        print(f"   有效数据: {analysis['valid_data_tests']} ({analysis['data_validity_rate']:.1f}%)")
        print(f"   新请求数: {analysis['new_requests']}")
        print(f"   缓存命中: {analysis['cached_requests']} ({analysis['cache_hit_rate']:.1f}%)")
        
        print(f"\n⏱️  性能统计:")
        print(f"   总耗时: {analysis['total_time']:.1f} 秒")
        print(f"   整体吞吐量: {analysis['throughput']:.2f} 请求/秒")
        print(f"   平均处理时间: {analysis['avg_processing_time']:.1f} 秒")
        print(f"   新请求平均时间: {analysis['avg_new_processing_time']:.1f} 秒")
        print(f"   缓存请求平均时间: {analysis['avg_cached_processing_time']:.3f} 秒")
        
        print(f"\n📊 数据质量:")
        print(f"   平均相似度: {analysis['avg_similarity']:.1f}")
        print(f"   相似度范围: {analysis['min_similarity']:.0f} - {analysis['max_similarity']:.0f}")
        print(f"   平均有效评分数: {analysis['avg_valid_scores']:.1f}")
        
        # 错误统计
        if analysis['error_types']:
            print(f"\n❌ 错误统计:")
            for error_type, count in analysis['error_types'].items():
                print(f"   - {error_type}: {count}次")
        
        # 判断测试结果
        success_rate = analysis['success_rate']
        validity_rate = analysis['data_validity_rate']
        
        if success_rate >= 95 and validity_rate >= 90:
            print(f"\n🎉 大规模压力测试通过！")
            print(f"✅ 优秀成功率: {success_rate:.1f}%")
            print(f"✅ 优秀数据有效性: {validity_rate:.1f}%")
            print(f"✅ 高并发处理能力强")
            print(f"✅ 服务稳定性优秀")
        elif success_rate >= 85 and validity_rate >= 80:
            print(f"\n✅ 大规模压力测试基本通过")
            print(f"✅ 良好成功率: {success_rate:.1f}%")
            print(f"✅ 良好数据有效性: {validity_rate:.1f}%")
            print(f"⚠️  可考虑优化并发处理")
        else:
            print(f"\n⚠️  大规模压力测试需要优化")
            if success_rate < 85:
                print(f"   - 成功率偏低: {success_rate:.1f}%")
            if validity_rate < 80:
                print(f"   - 数据有效性偏低: {validity_rate:.1f}%")

async def main():
    """主函数"""
    print("🎯 keywordMatching 大规模压力测试")
    print("📝 基于稳定版测试成功，进行更大规模测试")
    print("=" * 60)
    
    # 测试配置选项
    test_configs = [
        {"name": "中等规模", "keywords_count": 100, "concurrent": 20, "description": "100个关键词, 20并发"},
        {"name": "大规模", "keywords_count": 200, "concurrent": 30, "description": "200个关键词, 30并发"},
        {"name": "超大规模", "keywords_count": 500, "concurrent": 50, "description": "500个关键词, 50并发"},
        {"name": "极限测试", "keywords_count": 1000, "concurrent": 100, "description": "1000个关键词, 100并发"}
    ]
    
    print("请选择测试规模:")
    for i, config in enumerate(test_configs, 1):
        print(f"   {i}. {config['name']} - {config['description']}")
    
    try:
        choice = int(input("请输入选择 (1-4): ")) - 1
        if choice < 0 or choice >= len(test_configs):
            choice = 0
    except:
        choice = 0
    
    config = test_configs[choice]
    print(f"\n✅ 选择: {config['name']} - {config['description']}")
    
    # 筛选照明相关关键词（基于稳定版测试的成功经验）
    lighting_keywords = []
    lighting_terms = ['люстра', 'светильник', 'лампа', 'светодиод', 'освещение', 'плафон', 'бра', 'торшер', 'подсветка']
    
    for item in keyword_info:
        keyword = item['keyword'].lower()
        if any(term in keyword for term in lighting_terms):
            lighting_keywords.append(item['keyword'])
    
    print(f"📊 找到 {len(lighting_keywords)} 个照明相关关键词")
    
    # 选择测试关键词
    test_keywords_count = min(config['keywords_count'], len(lighting_keywords))
    test_keywords = random.sample(lighting_keywords, test_keywords_count)
    
    print(f"📝 准备测试 {test_keywords_count} 个关键词")
    print(f"🔄 并发数: {config['concurrent']}")
    
    # 执行测试
    tester = LargeScalePressureTest()
    results = await tester.large_scale_test(test_keywords, config['concurrent'])
    analysis = tester.analyze_results()
    tester.print_analysis(analysis)
    
    # 保存详细结果
    timestamp = int(time.time())
    result_file = f"large_scale_test_results_{config['name']}_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump({
            'config': config,
            'analysis': analysis,
            'detailed_results': results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: {result_file}")

if __name__ == "__main__":
    asyncio.run(main())
