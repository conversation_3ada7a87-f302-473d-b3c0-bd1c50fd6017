import requests
from typing import Optional, Dict, Any, List
import time
import json


def get_image_description(image_url: str, api_base_url: str = "http://localhost") -> Optional[str]:
    """
    获取图片描述信息
    
    Args:
        image_url: 图片URL
        api_base_url: API基础URL，默认为 http://localhost
        
    Returns:
        图片描述文本，如果失败返回None
    """
    try:
        # 构建API请求URL
        api_url = f"{api_base_url}/api/say-img-description/describe"
        
        # 请求参数
        params = {"url": image_url}
        
        # 发送请求
        response = requests.get(api_url, params=params, timeout=30)
        response.raise_for_status()
        
        # 解析响应
        result = response.json()
        
        # 返回描述信息
        return result.get("description", "")
        
    except requests.exceptions.RequestException as e:
        print(f"请求图片描述API失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"解析图片描述响应失败: {e}")
        return None
    except Exception as e:
        print(f"获取图片描述时发生未知错误: {e}")
        return None


def get_images_descriptions(image_urls: List[str], api_base_url: str = "http://localhost") -> List[Dict[str, Any]]:
    """
    批量获取多张图片的描述信息
    
    Args:
        image_urls: 图片URL列表
        api_base_url: API基础URL，默认为 http://localhost
        
    Returns:
        包含图片URL和描述的字典列表
    """
    results = []
    
    for url in image_urls:
        description = get_image_description(url, api_base_url)
        results.append({
            "url": url,
            "description": description
        })
        
        # 添加小延迟避免请求过于频繁
        time.sleep(0.1)
    
    return results


def add_image_descriptions_to_product(product_info: Dict[str, Any], api_base_url: str = "http://localhost") -> Dict[str, Any]:
    """
    为产品信息添加图片描述

    Args:
        product_info: 产品信息字典
        api_base_url: API基础URL，默认为 http://localhost

    Returns:
        添加了图片描述的产品信息字典
    """
    if not product_info:
        return product_info

    # 获取产品图片URLs
    image_urls = product_info.get("product_img_urls", [])

    if not image_urls:
        print("产品信息中没有找到图片URL")
        return product_info

    print(f"正在获取 {len(image_urls)} 张图片的描述...")

    # 获取图片描述
    image_descriptions = get_images_descriptions(image_urls, api_base_url)

    # 只添加描述文本字段
    descriptions_text = []
    for img_desc in image_descriptions:
        if img_desc["description"]:
            descriptions_text.append(img_desc["description"])

    product_info["images_description_text"] = " ".join(descriptions_text)

    print(f"成功获取了 {len([d for d in image_descriptions if d['description']])} 张图片的描述")

    return product_info


def test_image_description_api():
    """
    测试图片描述API功能
    """
    # 测试URL
    test_url = "https://basket-12.wbcontent.net/vol1754/part175431/175431678/images/big/1.webp"
    
    print("=== 测试图片描述API ===")
    print(f"测试图片URL: {test_url}")
    
    # 获取描述
    description = get_image_description(test_url)
    
    if description:
        print("✅ API测试成功!")
        print(f"图片描述: {description[:200]}...")
    else:
        print("❌ API测试失败!")
    
    return description is not None


if __name__ == "__main__":
    # 运行测试
    test_image_description_api()
