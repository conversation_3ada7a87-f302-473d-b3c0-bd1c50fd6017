#!/usr/bin/env python3
"""
简单的keywordMatching服务测试请求示例
"""
import requests
import json
import time

def test_keyword_matching_simple():
    """简单测试关键词匹配服务"""
    
    print("🧪 测试 keywordMatching 服务")
    print("=" * 50)
    
    # 服务地址（两种方式任选一种）
    # 方式1：直接访问业务服务
    direct_url = "http://localhost:8001/keyword-matching"
    
    # 方式2：通过网关访问（推荐）
    gateway_url = "http://localhost/api/product-similarity/keyword-matching"
    
    # 测试数据
    test_data = {
        "keyword": "люстра на потолок",  # 吊灯
        "target_product_id": 253486273
    }
    
    print(f"测试关键词: {test_data['keyword']}")
    print(f"目标产品ID: {test_data['target_product_id']}")
    print()
    
    # 测试两种访问方式
    urls = [
        ("直接访问业务服务", direct_url),
        ("通过网关访问", gateway_url)
    ]
    
    for method_name, url in urls:
        print(f"📊 {method_name}")
        print("-" * 30)
        
        try:
            # 发送POST请求
            start_time = time.time()
            response = requests.post(
                url,
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"状态码: {response.status_code}")
            print(f"耗时: {duration:.2f}秒")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 请求成功！")
                
                if result.get('status') == 'success':
                    data = result.get('data', {})
                    print(f"\n📈 分析结果:")
                    print(f"  关键词: {data.get('keyword')}")
                    print(f"  目标产品ID: {data.get('target_product_id')}")
                    print(f"  平均相似度: {data.get('avg_similarity')}分")
                    print(f"  相似产品数(>65分): {data.get('similar_count')}")
                    print(f"  竞品数(>80分): {data.get('competitor_count')}")
                    print(f"  有效样本数: {data.get('valid_scores')}")
                    print(f"  是否来自缓存: {data.get('from_cache')}")
                    print(f"  搜索产品数: {data.get('search_products_count')}")
                    print(f"  比较产品数: {data.get('compared_products_count')}")
                else:
                    print(f"❌ 服务返回错误: {result.get('message')}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        
        print()

def test_with_real_keywords():
    """使用test_keywords.py中的真实关键词测试"""
    
    print("🔍 使用真实关键词测试")
    print("=" * 50)
    
    try:
        # 导入真实关键词
        import sys
        sys.path.append('.')
        from test_keywords import keyword_info
        
        # 选择前5个关键词进行测试
        test_keywords = keyword_info[:5]
        target_product_id = 253486273
        
        gateway_url = "http://localhost/api/product-similarity/keyword-matching"
        
        print(f"目标产品ID: {target_product_id}")
        print(f"测试关键词数量: {len(test_keywords)}")
        print()
        
        for i, kw_info in enumerate(test_keywords, 1):
            keyword = kw_info['keyword']
            count = kw_info['count']
            
            print(f"📊 测试 {i}/{len(test_keywords)}: '{keyword}' (搜索量: {count})")
            print("-" * 40)
            
            test_data = {
                "keyword": keyword,
                "target_product_id": target_product_id
            }
            
            try:
                start_time = time.time()
                response = requests.post(
                    gateway_url,
                    json=test_data,
                    headers={"Content-Type": "application/json"},
                    timeout=60
                )
                end_time = time.time()
                
                duration = end_time - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('status') == 'success':
                        data = result.get('data', {})
                        avg_sim = data.get('avg_similarity', 0)
                        similar_count = data.get('similar_count', 0)
                        competitor_count = data.get('competitor_count', 0)
                        from_cache = data.get('from_cache', False)
                        
                        cache_status = "缓存" if from_cache else "新计算"
                        print(f"✅ 成功 ({duration:.2f}s, {cache_status})")
                        print(f"   相似度: {avg_sim}分 | 相似: {similar_count} | 竞品: {competitor_count}")
                    else:
                        print(f"❌ 服务错误: {result.get('message')}")
                else:
                    print(f"❌ 请求失败: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ 请求异常: {e}")
            
            print()
            
    except ImportError:
        print("❌ 无法导入 test_keywords.py，请确保文件存在")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    print("🚀 keywordMatching 服务测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # 1. 简单测试
    test_keyword_matching_simple()
    
    print("\n" + "=" * 60 + "\n")
    
    # 2. 使用真实关键词测试
    test_with_real_keywords()
    
    print("🎉 测试完成！")
