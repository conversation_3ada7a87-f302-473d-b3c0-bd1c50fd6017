#!/usr/bin/env python3
"""
微服务集成测试脚本
测试产品相似度服务与微服务集群的集成情况
"""
import asyncio
import aiohttp
import json
import time
from typing import Dict, Any, List
import sys
import os

# 测试配置
CONSUL_URL = "http://localhost:8500"
SERVICE_URL = "http://localhost:8000"
GATEWAY_URL = "http://localhost"
SERVICE_NAME = "product-similarity"

class MicroserviceIntegrationTest:
    """微服务集成测试类"""
    
    def __init__(self):
        self.session = None
        self.test_results = []
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def log_test(self, test_name: str, success: bool, message: str = "", details: Any = None):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        if details and isinstance(details, dict):
            print(f"    详情: {json.dumps(details, ensure_ascii=False, indent=2)}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "details": details
        })
    
    async def test_consul_connection(self) -> bool:
        """测试Consul连接"""
        try:
            async with self.session.get(f"{CONSUL_URL}/v1/status/leader", timeout=5) as response:
                if response.status == 200:
                    leader = await response.text()
                    self.log_test("Consul连接测试", True, f"Leader: {leader.strip()}")
                    return True
                else:
                    self.log_test("Consul连接测试", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("Consul连接测试", False, f"连接异常: {e}")
            return False
    
    async def test_service_registration(self) -> bool:
        """测试服务注册"""
        try:
            url = f"{CONSUL_URL}/v1/catalog/service/{SERVICE_NAME}"
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    services = await response.json()
                    if services:
                        service_info = services[0]
                        self.log_test("服务注册测试", True, 
                                    f"发现 {len(services)} 个服务实例", 
                                    {
                                        "service_name": service_info.get("ServiceName"),
                                        "service_address": service_info.get("ServiceAddress"),
                                        "service_port": service_info.get("ServicePort"),
                                        "service_tags": service_info.get("ServiceTags")
                                    })
                        return True
                    else:
                        self.log_test("服务注册测试", False, "未找到服务实例")
                        return False
                else:
                    self.log_test("服务注册测试", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("服务注册测试", False, f"查询异常: {e}")
            return False
    
    async def test_service_health(self) -> bool:
        """测试服务健康检查"""
        try:
            url = f"{CONSUL_URL}/v1/health/service/{SERVICE_NAME}?passing=true"
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    health_data = await response.json()
                    if health_data:
                        healthy_count = len(health_data)
                        self.log_test("服务健康检查", True, 
                                    f"{healthy_count} 个健康实例")
                        return True
                    else:
                        self.log_test("服务健康检查", False, "没有健康的服务实例")
                        return False
                else:
                    self.log_test("服务健康检查", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("服务健康检查", False, f"检查异常: {e}")
            return False
    
    async def test_direct_service_access(self) -> bool:
        """测试直接访问服务"""
        try:
            # 测试健康检查端点
            async with self.session.get(f"{SERVICE_URL}/health", timeout=10) as response:
                if response.status == 200:
                    health_data = await response.json()
                    self.log_test("直接服务访问", True, 
                                "健康检查端点正常", health_data)
                    return True
                else:
                    self.log_test("直接服务访问", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("直接服务访问", False, f"访问异常: {e}")
            return False
    
    async def test_gateway_routing(self) -> bool:
        """测试网关路由"""
        try:
            # 测试通过网关访问服务
            url = f"{GATEWAY_URL}/api/{SERVICE_NAME}/health"
            async with self.session.get(url, timeout=15) as response:
                if response.status == 200:
                    health_data = await response.json()
                    self.log_test("网关路由测试", True, 
                                "通过网关访问成功", health_data)
                    return True
                else:
                    self.log_test("网关路由测试", False, 
                                f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("网关路由测试", False, f"路由异常: {e}")
            return False
    
    async def test_service_api_endpoints(self) -> bool:
        """测试服务API端点"""
        try:
            # 测试服务信息端点
            async with self.session.get(f"{SERVICE_URL}/info", timeout=10) as response:
                if response.status == 200:
                    info_data = await response.json()
                    self.log_test("API端点测试", True, 
                                "服务信息端点正常", 
                                {
                                    "service": info_data.get("service"),
                                    "version": info_data.get("version"),
                                    "environment": info_data.get("environment")
                                })
                    return True
                else:
                    self.log_test("API端点测试", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("API端点测试", False, f"端点异常: {e}")
            return False
    
    async def test_load_balancing(self) -> bool:
        """测试负载均衡（如果有多个实例）"""
        try:
            # 获取服务实例列表
            url = f"{CONSUL_URL}/v1/catalog/service/{SERVICE_NAME}"
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    services = await response.json()
                    instance_count = len(services)
                    
                    if instance_count > 1:
                        # 多次请求测试负载均衡
                        responses = []
                        for i in range(5):
                            try:
                                async with self.session.get(
                                    f"{GATEWAY_URL}/api/{SERVICE_NAME}/info", 
                                    timeout=10
                                ) as resp:
                                    if resp.status == 200:
                                        data = await resp.json()
                                        responses.append(data.get("instance_id", "unknown"))
                            except:
                                pass
                        
                        unique_responses = set(responses)
                        if len(unique_responses) > 1:
                            self.log_test("负载均衡测试", True, 
                                        f"检测到负载均衡，{len(unique_responses)} 个不同实例响应")
                        else:
                            self.log_test("负载均衡测试", True, 
                                        f"单实例响应（{instance_count} 个实例）")
                        return True
                    else:
                        self.log_test("负载均衡测试", True, 
                                    f"单实例部署（{instance_count} 个实例）")
                        return True
                else:
                    self.log_test("负载均衡测试", False, "无法获取服务实例信息")
                    return False
        except Exception as e:
            self.log_test("负载均衡测试", False, f"测试异常: {e}")
            return False
    
    async def test_business_functionality(self) -> bool:
        """测试业务功能"""
        try:
            # 从test_product_ids.py获取真实产品ID
            sys.path.append('.')
            try:
                from test_product_ids import get_test_product_ids
                product_ids = get_test_product_ids()
                if len(product_ids) >= 2:
                    test_id1, test_id2 = product_ids[0], product_ids[1]
                else:
                    # 使用默认测试ID
                    test_id1, test_id2 = 233681605, 233681606
            except:
                # 使用默认测试ID
                test_id1, test_id2 = 233681605, 233681606
            
            # 测试产品信息获取
            async with self.session.get(
                f"{SERVICE_URL}/product/{test_id1}", 
                timeout=30
            ) as response:
                if response.status == 200:
                    product_data = await response.json()
                    self.log_test("业务功能测试-产品信息", True, 
                                f"成功获取产品 {test_id1} 信息")
                    
                    # 测试产品比较
                    compare_data = {
                        "product_id1": test_id1,
                        "product_id2": test_id2,
                        "mode": "text"
                    }
                    
                    async with self.session.post(
                        f"{SERVICE_URL}/compare",
                        json=compare_data,
                        timeout=60
                    ) as compare_response:
                        if compare_response.status == 200:
                            compare_result = await compare_response.json()
                            self.log_test("业务功能测试-产品比较", True, 
                                        f"成功比较产品 {test_id1} vs {test_id2}")
                            return True
                        else:
                            self.log_test("业务功能测试-产品比较", False, 
                                        f"比较失败 HTTP {compare_response.status}")
                            return False
                else:
                    self.log_test("业务功能测试-产品信息", False, 
                                f"获取产品信息失败 HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test("业务功能测试", False, f"功能测试异常: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始微服务集成测试...")
        print("=" * 60)
        
        # 基础设施测试
        print("\n📋 基础设施测试")
        await self.test_consul_connection()
        
        # 服务注册测试
        print("\n🔗 服务注册测试")
        await self.test_service_registration()
        await self.test_service_health()
        
        # 服务访问测试
        print("\n🌐 服务访问测试")
        await self.test_direct_service_access()
        await self.test_gateway_routing()
        await self.test_service_api_endpoints()
        
        # 高级功能测试
        print("\n⚖️ 高级功能测试")
        await self.test_load_balancing()
        
        # 业务功能测试
        print("\n💼 业务功能测试")
        await self.test_business_functionality()
        
        # 测试总结
        print("\n" + "=" * 60)
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 测试总结")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print(f"\n{'🎉 所有测试通过！' if failed_tests == 0 else '⚠️ 部分测试失败，请检查配置'}")

async def main():
    """主函数"""
    async with MicroserviceIntegrationTest() as tester:
        await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
