#!/usr/bin/env python3
"""
检查API端点是否正确部署
"""
import asyncio
import httpx
import json

async def check_api_endpoints():
    """检查API端点"""
    
    base_url = "http://localhost:8000"
    
    print("🔍 检查API端点部署情况")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=30) as client:
        
        # 1. 检查根路径
        print("\n📋 1. 检查根路径")
        print("-" * 30)
        try:
            response = await client.get(f"{base_url}/")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print("✅ 根路径访问成功")
                print(json.dumps(data, indent=2, ensure_ascii=False))
            else:
                print(f"❌ 根路径访问失败: {response.text}")
        except Exception as e:
            print(f"❌ 根路径访问异常: {e}")
        
        # 2. 检查OpenAPI文档
        print("\n📚 2. 检查OpenAPI文档")
        print("-" * 30)
        try:
            response = await client.get(f"{base_url}/openapi.json")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                openapi_data = response.json()
                print("✅ OpenAPI文档获取成功")
                
                # 检查路径
                paths = openapi_data.get("paths", {})
                print(f"\n📍 可用的API端点 ({len(paths)}个):")
                for path, methods in paths.items():
                    method_list = list(methods.keys())
                    print(f"  {path} - {', '.join(method_list).upper()}")
                
                # 特别检查keyword-matching端点
                if "/keyword-matching" in paths:
                    print("\n✅ keyword-matching端点已定义")
                    km_methods = paths["/keyword-matching"]
                    for method, details in km_methods.items():
                        print(f"  方法: {method.upper()}")
                        print(f"  摘要: {details.get('summary', 'N/A')}")
                else:
                    print("\n❌ keyword-matching端点未找到")
                    
            else:
                print(f"❌ OpenAPI文档获取失败: {response.status_code}")
                print(response.text)
        except Exception as e:
            print(f"❌ OpenAPI文档获取异常: {e}")
        
        # 3. 测试所有端点
        print("\n🧪 3. 测试主要端点")
        print("-" * 30)
        
        endpoints_to_test = [
            ("GET", "/health", "健康检查"),
            ("GET", "/info", "服务信息"),
            ("GET", "/stats", "统计信息"),
            ("POST", "/keyword-matching", "关键词匹配"),
            ("POST", "/compare", "产品比较"),
            ("GET", "/product/123", "获取产品信息")
        ]
        
        for method, endpoint, description in endpoints_to_test:
            try:
                if method == "GET":
                    response = await client.get(f"{base_url}{endpoint}")
                elif method == "POST":
                    # 使用测试数据
                    if endpoint == "/keyword-matching":
                        test_data = {
                            "keyword": "люстра на потолок",
                            "target_product_id": 253486273
                        }
                    elif endpoint == "/compare":
                        test_data = {
                            "product_ids": [253486273, 123456789]
                        }
                    else:
                        test_data = {}
                    
                    response = await client.post(f"{base_url}{endpoint}", json=test_data)
                
                print(f"{method} {endpoint} ({description}): {response.status_code}")
                
                if response.status_code == 404:
                    print(f"  ❌ 端点未找到")
                elif response.status_code == 422:
                    print(f"  ⚠️  请求参数错误 (正常，因为使用测试数据)")
                elif response.status_code == 200:
                    print(f"  ✅ 端点正常")
                elif response.status_code >= 500:
                    print(f"  ❌ 服务器错误")
                else:
                    print(f"  ℹ️  状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"{method} {endpoint} ({description}): ❌ 异常 - {e}")

async def check_service_status():
    """检查服务运行状态"""
    
    print("\n" + "=" * 50)
    print("🔧 检查服务运行状态")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient(timeout=10) as client:
        
        # 检查健康状态
        try:
            response = await client.get(f"{base_url}/health")
            if response.status_code == 200:
                health_data = response.json()
                print("✅ 服务健康状态:")
                print(json.dumps(health_data, indent=2, ensure_ascii=False))
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 无法连接到服务: {e}")
            return False
        
        # 检查服务信息
        try:
            response = await client.get(f"{base_url}/info")
            if response.status_code == 200:
                info_data = response.json()
                print("\n📊 服务信息:")
                print(json.dumps(info_data, indent=2, ensure_ascii=False))
            else:
                print(f"❌ 服务信息获取失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 服务信息获取异常: {e}")
    
    return True

async def main():
    """主函数"""
    print("🚀 API端点部署检查")
    print("检查时间:", __import__('time').strftime("%Y-%m-%d %H:%M:%S"))
    
    # 检查服务状态
    service_running = await check_service_status()
    
    if service_running:
        # 检查API端点
        await check_api_endpoints()
    else:
        print("❌ 服务未运行，无法检查API端点")
    
    print("\n" + "=" * 50)
    print("🏁 检查完成")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
