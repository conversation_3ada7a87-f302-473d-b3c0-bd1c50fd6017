#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试glm-4.5模型的功能
使用全新的关键词确保不会命中缓存
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# 测试配置
SERVICE_URL = "http://localhost:8001"

async def test_glm45_model():
    """测试glm-4.5模型的keywordMatching功能"""
    print("🤖 专门测试 glm-4.5 模型")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 使用全新的关键词，确保不会命中缓存
    # 使用当前时间戳作为关键词的一部分
    timestamp = int(time.time())
    
    test_cases = [
        {
            "keyword": f"современная светодиодная люстра для спальни {timestamp}",
            "target_product_id": 253486273,
            "description": "现代LED卧室吊灯"
        },
        {
            "keyword": f"умный потолочный светильник с датчиком движения {timestamp}",
            "target_product_id": 316527894,
            "description": "智能感应吸顶灯"
        },
        {
            "keyword": f"настольная лампа для чтения с USB зарядкой {timestamp}",
            "target_product_id": 253486274,
            "description": "USB充电阅读台灯"
        },
        {
            "keyword": f"декоративная подсветка для гостиной RGB {timestamp}",
            "target_product_id": 316531871,
            "description": "RGB客厅装饰灯"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}/{len(test_cases)}: {test_case['description']}")
        print(f"关键词: {test_case['keyword']}")
        print(f"目标产品ID: {test_case['target_product_id']}")
        
        try:
            start_time = time.time()
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{SERVICE_URL}/keyword-matching",
                    json={
                        "keyword": test_case["keyword"],
                        "target_product_id": test_case["target_product_id"]
                    },
                    timeout=aiohttp.ClientTimeout(total=600)  # 10分钟超时
                ) as response:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        result = data.get('data', {})
                        
                        print(f"✅ 测试成功:")
                        print(f"   • 相似度: {result.get('avg_similarity', 'N/A')}")
                        print(f"   • 相似产品数: {result.get('similar_count', 'N/A')}")
                        print(f"   • 竞争产品数: {result.get('competitor_count', 'N/A')}")
                        print(f"   • 有效评分数: {result.get('valid_scores', 'N/A')}")
                        print(f"   • 处理时间: {processing_time:.2f}秒")
                        print(f"   • 缓存状态: {result.get('from_cache', 'N/A')}")
                        
                        # 分析AI模型使用情况
                        if not result.get('from_cache', True):
                            if processing_time > 60:
                                print(f"   🤖 使用了AI模型进行深度分析 (glm-4.5)")
                            elif processing_time > 30:
                                print(f"   🚀 AI模型快速分析完成 (glm-4.5)")
                            else:
                                print(f"   ⚡ AI模型超快速分析 (glm-4.5)")
                        else:
                            print(f"   💾 使用了缓存结果")
                        
                        results.append({
                            'success': True,
                            'similarity': result.get('avg_similarity', 0),
                            'processing_time': processing_time,
                            'from_cache': result.get('from_cache', True),
                            'similar_count': result.get('similar_count', 0),
                            'competitor_count': result.get('competitor_count', 0),
                            'valid_scores': result.get('valid_scores', 0)
                        })
                        
                    else:
                        print(f"❌ 测试失败: HTTP {response.status}")
                        error_text = await response.text()
                        print(f"   错误信息: {error_text}")
                        results.append({'success': False, 'error': f"HTTP {response.status}"})
                        
        except asyncio.TimeoutError:
            print(f"⏰ 测试超时")
            results.append({'success': False, 'error': 'timeout'})
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append({'success': False, 'error': str(e)})
        
        # 每个测试之间稍作停顿
        if i < len(test_cases):
            await asyncio.sleep(2)
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 glm-4.5 模型测试结果总结")
    print("=" * 60)
    
    successful_results = [r for r in results if r.get('success', False)]
    success_count = len(successful_results)
    total_tests = len(test_cases)
    
    print(f"📈 成功率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if successful_results:
        avg_similarity = sum(r['similarity'] for r in successful_results) / len(successful_results)
        avg_time = sum(r['processing_time'] for r in successful_results) / len(successful_results)
        cache_hits = sum(1 for r in successful_results if r.get('from_cache', True))
        
        print(f"📊 平均相似度: {avg_similarity:.1f}")
        print(f"⏱️ 平均处理时间: {avg_time:.2f}秒")
        print(f"💾 缓存命中: {cache_hits}/{len(successful_results)} ({cache_hits/len(successful_results)*100:.1f}%)")
        
        # 相似度分布
        high_sim = sum(1 for r in successful_results if r['similarity'] >= 65)
        med_sim = sum(1 for r in successful_results if 30 <= r['similarity'] < 65)
        low_sim = sum(1 for r in successful_results if r['similarity'] < 30)
        
        print(f"\n🎯 相似度分布:")
        print(f"   • 高相似度 (≥65): {high_sim}个")
        print(f"   • 中等相似度 (30-64): {med_sim}个")
        print(f"   • 低相似度 (<30): {low_sim}个")
        
        # 产品发现统计
        total_similar = sum(r['similar_count'] for r in successful_results)
        total_competitors = sum(r['competitor_count'] for r in successful_results)
        avg_valid_scores = sum(r['valid_scores'] for r in successful_results) / len(successful_results)
        
        print(f"\n🔍 产品发现统计:")
        print(f"   • 总相似产品数: {total_similar}个")
        print(f"   • 总竞争产品数: {total_competitors}个")
        print(f"   • 平均有效评分数: {avg_valid_scores:.1f}个")
        
        # 检查是否有使用新AI模型的测试
        new_ai_tests = [r for r in successful_results if not r.get('from_cache', True)]
        if new_ai_tests:
            print(f"\n🤖 glm-4.5 模型分析:")
            print(f"   • 新分析次数: {len(new_ai_tests)}次")
            avg_new_ai_time = sum(r['processing_time'] for r in new_ai_tests) / len(new_ai_tests)
            print(f"   • 平均AI分析时间: {avg_new_ai_time:.2f}秒")
            
            # 分析AI模型性能
            if avg_new_ai_time < 30:
                print(f"   ⚡ glm-4.5 响应速度: 非常快")
            elif avg_new_ai_time < 60:
                print(f"   🚀 glm-4.5 响应速度: 快速")
            elif avg_new_ai_time < 120:
                print(f"   ⏱️ glm-4.5 响应速度: 正常")
            else:
                print(f"   🐌 glm-4.5 响应速度: 较慢")
        else:
            print(f"\n💾 所有测试都命中了缓存，未使用 glm-4.5 模型")
    
    # 最终评估
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过！glm-4.5 模型工作正常！")
        if any(not r.get('from_cache', True) for r in successful_results):
            print(f"✅ glm-4.5 模型成功处理了新的关键词分析")
        else:
            print(f"💾 所有请求都命中缓存，建议清除缓存后重新测试")
    elif success_count >= total_tests * 0.75:
        print(f"\n⚠️ 大部分测试通过，glm-4.5 模型基本正常")
        print(f"成功率: {success_count/total_tests*100:.1f}%")
    else:
        print(f"\n🚨 多项测试失败，请检查 glm-4.5 模型配置")
        print(f"成功率: {success_count/total_tests*100:.1f}%")

if __name__ == "__main__":
    asyncio.run(test_glm45_model())
