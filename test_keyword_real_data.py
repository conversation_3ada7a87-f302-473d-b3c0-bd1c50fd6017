"""
使用真实数据测试 keywordMatching 函数
- 使用 test_product_ids.py 中的真实产品ID
- 使用 test_keywords.py 中的真实关键词
- 20个并发压力测试
"""
import asyncio
import sys
import os
import json
import time
import random
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.keyword_matching import keywordMatching
from product_similarity.db import init_pool, close_pool
from test_keywords import keyword_info
from test_product_ids import nm_ids

class RealDataKeywordTest:
    def __init__(self, concurrent_limit=20, test_count=25):
        self.concurrent_limit = concurrent_limit
        self.test_count = test_count
        self.results = []
        self.success_count = 0
        self.error_count = 0
        self.image_desc_success_count = 0
        self.image_desc_empty_count = 0
        self.start_time = None
        self.semaphore = asyncio.Semaphore(concurrent_limit)
    
    async def test_single_request(self, keyword: str, target_product_id: int, test_index: int):
        """测试单个真实请求"""
        async with self.semaphore:
            print(f"\n{'='*80}")
            print(f"🧪 测试 #{test_index} - 关键词: '{keyword}' -> 产品ID: {target_product_id}")
            print(f"{'='*80}")
            
            start_time = time.time()
            result = {
                "test_index": test_index,
                "keyword": keyword,
                "target_product_id": target_product_id,
                "start_time": datetime.now().strftime("%H:%M:%S"),
                "status": "unknown",
                "duration": 0,
                "error": None,
                "response_data": None,
                "image_desc_check": {
                    "target_has_desc": False,
                    "target_desc_length": 0,
                    "similar_products_with_desc": 0,
                    "total_similar_products": 0
                }
            }
            
            try:
                # 调用真实的 keywordMatching 函数
                response_data = await keywordMatching(keyword, target_product_id)
                duration = time.time() - start_time
                
                result.update({
                    "status": "success",
                    "duration": duration,
                    "response_data": response_data
                })
                
                # 检查图片描述字段
                image_check = self.check_image_descriptions(response_data)
                result["image_desc_check"] = image_check
                
                # 打印成功结果
                print(f"✅ 测试成功 - 耗时: {duration:.2f}秒")
                print(f"📊 匹配结果:")
                print(f"  状态: {response_data.get('status', 'N/A')}")
                print(f"  平均相似度: {response_data.get('avg_similarity', 'N/A')}")
                print(f"  相似产品数量: {response_data.get('similar_count', 'N/A')}")
                print(f"  竞品数量: {response_data.get('competitor_count', 'N/A')}")
                
                # 打印图片描述检查结果
                print(f"🖼️ 图片描述检查:")
                print(f"  目标产品有描述: {'✅' if image_check['target_has_desc'] else '❌'}")
                print(f"  目标产品描述长度: {image_check['target_desc_length']} 字符")
                print(f"  相似产品中有描述的: {image_check['similar_products_with_desc']}/{image_check['total_similar_products']}")
                
                if image_check['target_has_desc']:
                    self.image_desc_success_count += 1
                else:
                    self.image_desc_empty_count += 1
                
                # 打印详细的产品信息
                self.print_product_details(response_data)
                
                self.success_count += 1
                
            except Exception as e:
                duration = time.time() - start_time
                result.update({
                    "status": "error",
                    "duration": duration,
                    "error": str(e)
                })
                
                print(f"❌ 测试失败 - 耗时: {duration:.2f}秒")
                print(f"异常信息: {e}")
                
                self.error_count += 1
            
            self.results.append(result)
            return result
    
    def check_image_descriptions(self, response_data: dict) -> dict:
        """检查响应数据中的图片描述字段"""
        check_result = {
            "target_has_desc": False,
            "target_desc_length": 0,
            "similar_products_with_desc": 0,
            "total_similar_products": 0,
            "has_old_field": False
        }
        
        # 检查目标产品
        target_product = response_data.get("target_product", {})
        if target_product:
            product_info = target_product.get("product_info", {})
            if product_info:
                desc_text = product_info.get("images_description_text", "")
                if desc_text and desc_text.strip():
                    check_result["target_has_desc"] = True
                    check_result["target_desc_length"] = len(desc_text)
                
                # 检查是否还有旧字段
                if 'image_descriptions' in product_info:
                    check_result["has_old_field"] = True
        
        # 检查相似产品
        similar_products = response_data.get("similar_products", [])
        check_result["total_similar_products"] = len(similar_products)
        
        for product in similar_products:
            product_info = product.get("product_info", {})
            if product_info:
                desc_text = product_info.get("images_description_text", "")
                if desc_text and desc_text.strip():
                    check_result["similar_products_with_desc"] += 1
                
                # 检查旧字段
                if 'image_descriptions' in product_info:
                    check_result["has_old_field"] = True
        
        return check_result
    
    def print_product_details(self, response_data: dict):
        """打印产品详细信息"""
        print(f"\n📋 产品详细信息:")
        print(f"{'='*60}")
        
        # 目标产品信息
        target_product = response_data.get("target_product", {})
        if target_product:
            product_info = target_product.get("product_info", {})
            print(f"🎯 目标产品:")
            print(f"  产品名称: {product_info.get('imt_name', 'N/A')}")
            print(f"  产品ID: {product_info.get('nm_id', 'N/A')}")
            print(f"  图片URL数量: {len(product_info.get('product_img_urls', []))}")
            
            desc_text = product_info.get('images_description_text', '')
            if desc_text:
                print(f"  图片描述: {desc_text[:100]}...")
            else:
                print(f"  图片描述: 无")
            
            # 检查旧字段
            if 'image_descriptions' in product_info:
                print(f"  ⚠️ 警告: 仍包含 image_descriptions 字段！")
            else:
                print(f"  ✅ 确认: 已删除 image_descriptions 字段")
        
        # 相似产品信息（显示前3个）
        similar_products = response_data.get("similar_products", [])
        if similar_products:
            print(f"\n🔍 相似产品 (前3个):")
            for i, product in enumerate(similar_products[:3], 1):
                product_info = product.get("product_info", {})
                similarity = product.get("similarity", 0)
                print(f"  {i}. {product_info.get('imt_name', 'N/A')[:40]}... (相似度: {similarity})")
                
                desc_text = product_info.get('images_description_text', '')
                if desc_text:
                    print(f"     图片描述: {desc_text[:80]}...")
                else:
                    print(f"     图片描述: 无")
                
                # 检查旧字段
                if 'image_descriptions' in product_info:
                    print(f"     ⚠️ 警告: 仍包含 image_descriptions 字段！")
        
        print(f"{'='*60}")
    
    async def run_real_data_test(self):
        """运行真实数据测试"""
        print(f"🚀 开始 keywordMatching 真实数据压力测试")
        print(f"测试数量: {self.test_count}")
        print(f"并发限制: {self.concurrent_limit}")
        print(f"可用关键词数量: {len(keyword_info)}")
        print(f"可用产品ID数量: {len(nm_ids)}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 初始化数据库连接池
        try:
            await init_pool()
            print("✅ 数据库连接池初始化成功")
        except Exception as e:
            print(f"❌ 数据库连接池初始化失败: {e}")
            return
        
        self.start_time = time.time()
        
        # 准备测试数据 - 使用真实的关键词和产品ID
        test_cases = []
        for i in range(self.test_count):
            # 从真实关键词中随机选择
            keyword_obj = random.choice(keyword_info)
            if isinstance(keyword_obj, dict):
                keyword = keyword_obj.get('keyword', str(keyword_obj))
            else:
                keyword = str(keyword_obj)
            
            # 从真实产品ID中随机选择
            target_product_id = int(random.choice(nm_ids))
            test_cases.append((keyword, target_product_id, i + 1))
        
        print(f"📝 准备的测试案例:")
        for i, (keyword, product_id, _) in enumerate(test_cases[:5], 1):
            print(f"  {i}. '{keyword}' -> {product_id}")
        if len(test_cases) > 5:
            print(f"  ... 还有 {len(test_cases) - 5} 个测试案例")
        
        # 创建并发任务
        tasks = []
        for keyword, target_product_id, test_index in test_cases:
            task = asyncio.create_task(
                self.test_single_request(keyword, target_product_id, test_index)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # 关闭数据库连接池
        try:
            await close_pool()
            print("✅ 数据库连接池关闭成功")
        except Exception as e:
            print(f"⚠️ 数据库连接池关闭失败: {e}")
        
        # 打印总结
        await self.print_summary()
    
    async def print_summary(self):
        """打印测试总结"""
        total_duration = time.time() - self.start_time
        
        print(f"\n{'='*80}")
        print(f"📊 keywordMatching 真实数据压力测试总结")
        print(f"{'='*80}")
        print(f"总测试数量: {self.test_count}")
        print(f"成功数量: {self.success_count}")
        print(f"失败数量: {self.error_count}")
        print(f"成功率: {(self.success_count/self.test_count)*100:.1f}%")
        print(f"总耗时: {total_duration:.2f}秒")
        print(f"平均耗时: {total_duration/self.test_count:.2f}秒/个")
        
        # 图片描述统计
        print(f"\n🖼️ 图片描述字段检查:")
        print(f"目标产品有描述: {self.image_desc_success_count}/{self.success_count}")
        print(f"目标产品无描述: {self.image_desc_empty_count}/{self.success_count}")
        if self.success_count > 0:
            desc_success_rate = (self.image_desc_success_count / self.success_count) * 100
            print(f"图片描述成功率: {desc_success_rate:.1f}%")
        
        # 检查是否有旧字段
        old_field_count = 0
        for result in self.results:
            if result["status"] == "success":
                if result["image_desc_check"].get("has_old_field", False):
                    old_field_count += 1
        
        print(f"发现旧字段的产品: {old_field_count}/{self.success_count}")
        
        # 性能统计
        successful_results = [r for r in self.results if r["status"] == "success"]
        if successful_results:
            durations = [r["duration"] for r in successful_results]
            avg_duration = sum(durations) / len(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            
            print(f"\n⚡ 性能统计 (仅成功测试):")
            print(f"平均响应时间: {avg_duration:.2f}秒")
            print(f"最快响应时间: {min_duration:.2f}秒")
            print(f"最慢响应时间: {max_duration:.2f}秒")
        
        # 并发测试结果
        print(f"\n🔄 并发测试结果:")
        print(f"并发数量: {self.concurrent_limit}")
        if self.success_count == self.test_count:
            print(f"✅ 所有 {self.concurrent_limit} 个并发请求都成功处理")
        else:
            print(f"⚠️ {self.concurrent_limit} 个并发请求中有 {self.error_count} 个失败")
        
        # 显示失败的测试
        failed_results = [r for r in self.results if r["status"] == "error"]
        if failed_results:
            print(f"\n❌ 失败的测试:")
            for result in failed_results[:5]:  # 只显示前5个
                print(f"  #{result['test_index']} - {result['keyword'][:30]}... -> {result['target_product_id']}")
                print(f"    错误: {result['error']}")
        
        print(f"{'='*80}")
        
        # 最终结论
        if self.success_count == self.test_count and self.image_desc_success_count > 0:
            print(f"🎉 真实数据压力测试完全成功！")
            print(f"✅ 并发处理: {self.concurrent_limit}个并发请求全部通过")
            print(f"✅ 图片描述: images_description_text 字段正常工作")
            print(f"✅ 真实数据: 使用真实关键词和产品ID测试")
            print(f"✅ 字段清理: {'已完全删除' if old_field_count == 0 else '部分删除'} image_descriptions 字段")
        else:
            print(f"⚠️ 测试存在问题，需要进一步检查")

async def main():
    """主函数"""
    # 测试参数
    CONCURRENT_LIMIT = 20  # 20个并发
    TEST_COUNT = 25        # 25个测试
    
    print("🔥 keywordMatching 真实数据压力测试")
    print(f"将进行 {TEST_COUNT} 个测试，{CONCURRENT_LIMIT} 个并发")
    print("使用真实的关键词和产品ID数据")
    
    # 创建测试实例
    tester = RealDataKeywordTest(
        concurrent_limit=CONCURRENT_LIMIT,
        test_count=TEST_COUNT
    )
    
    # 运行测试
    await tester.run_real_data_test()

if __name__ == "__main__":
    asyncio.run(main())
