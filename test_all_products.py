#!/usr/bin/env python3
"""
测试所有产品ID
对test_product_ids.py中的所有产品进行全面测试
"""

import asyncio
import sys
import os
import time
from typing import Dict, List, Any
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail
from product_similarity.crud import get_basket_stats
from product_similarity.db import init_pool, close_pool
from product_similarity.logging import log_success, log_error, log_info, log_warning
from test_product_ids import nm_ids

class TestResults:
    """测试结果统计类"""
    def __init__(self):
        self.total_tests = 0
        self.success_count = 0
        self.fail_count = 0
        self.error_count = 0
        self.dynamic_detection_count = 0
        self.total_time = 0
        self.failed_ids = []
        self.error_ids = []
        self.slow_requests = []  # 耗时超过5秒的请求
        
    def add_success(self, product_id: int, duration: float):
        self.success_count += 1
        self.total_tests += 1
        self.total_time += duration
        
        if duration > 5.0:  # 耗时超过5秒可能经历了动态探测
            self.slow_requests.append((product_id, duration))
            self.dynamic_detection_count += 1
    
    def add_failure(self, product_id: int, duration: float):
        self.fail_count += 1
        self.total_tests += 1
        self.total_time += duration
        self.failed_ids.append(product_id)
    
    def add_error(self, product_id: int, error: str, duration: float):
        self.error_count += 1
        self.total_tests += 1
        self.total_time += duration
        self.error_ids.append((product_id, error))
    
    def get_summary(self) -> Dict[str, Any]:
        avg_time = self.total_time / self.total_tests if self.total_tests > 0 else 0
        success_rate = (self.success_count / self.total_tests * 100) if self.total_tests > 0 else 0
        
        return {
            "total_tests": self.total_tests,
            "success_count": self.success_count,
            "fail_count": self.fail_count,
            "error_count": self.error_count,
            "success_rate": success_rate,
            "dynamic_detection_count": self.dynamic_detection_count,
            "total_time": self.total_time,
            "average_time": avg_time,
            "failed_ids": self.failed_ids,
            "error_ids": self.error_ids,
            "slow_requests": self.slow_requests
        }

async def test_single_product(product_id: int, results: TestResults, index: int, total: int):
    """测试单个产品"""
    start_time = time.time()
    
    try:
        # 显示进度
        if index % 10 == 0 or index == total - 1:
            log_info(f"进度: {index + 1}/{total} ({((index + 1) / total * 100):.1f}%) - 测试产品ID: {product_id}")
        
        product_info = await get_product_detail(product_id)
        duration = time.time() - start_time
        
        if product_info:
            results.add_success(product_id, duration)
            
            # 记录详细信息（仅对前几个产品）
            if index < 5:
                product_name = product_info.get('name', 'N/A')[:50]
                log_success(f"成功: {product_id} - {product_name}... (耗时: {duration:.2f}s)")
        else:
            results.add_failure(product_id, duration)
            log_error(f"失败: {product_id} - 产品信息为空")
            
    except Exception as e:
        duration = time.time() - start_time
        error_msg = str(e)[:100]
        results.add_error(product_id, error_msg, duration)
        log_error(f"异常: {product_id} - {error_msg}")

async def test_products_batch(product_ids: List[int], batch_size: int = 5) -> TestResults:
    """批量测试产品（控制并发数）"""
    results = TestResults()
    total = len(product_ids)
    
    log_info(f"开始批量测试 {total} 个产品，批次大小: {batch_size}")
    
    # 分批处理以控制并发
    for i in range(0, total, batch_size):
        batch = product_ids[i:i + batch_size]
        batch_start_time = time.time()
        
        # 并发测试当前批次
        tasks = []
        for j, product_id in enumerate(batch):
            task = test_single_product(product_id, results, i + j, total)
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        batch_duration = time.time() - batch_start_time
        log_info(f"批次 {i//batch_size + 1} 完成，耗时: {batch_duration:.2f}s")
        
        # 每批次后短暂休息，避免过度请求
        if i + batch_size < total:
            await asyncio.sleep(1)
    
    return results

async def analyze_basket_patterns(results: TestResults):
    """分析basket模式"""
    log_info("=== 分析Basket模式 ===")
    
    try:
        stats = await get_basket_stats()
        
        log_info("Basket系统统计:")
        sample_stats = stats.get('sample_stats', {})
        mapping_stats = stats.get('mapping_stats', {})
        
        log_info(f"  样本数据:")
        log_info(f"    - 总样本数: {sample_stats.get('total_samples', 0)}")
        log_info(f"    - 唯一basket数: {sample_stats.get('unique_baskets', 0)}")
        log_info(f"    - 平均置信度: {sample_stats.get('avg_confidence', 0):.3f}")
        log_info(f"    - 高置信度样本: {sample_stats.get('high_confidence_samples', 0)}")
        log_info(f"    - 低置信度样本: {sample_stats.get('low_confidence_samples', 0)}")
        
        log_info(f"  范围映射:")
        log_info(f"    - 总映射数: {mapping_stats.get('total_mappings', 0)}")
        log_info(f"    - 映射样本总数: {mapping_stats.get('total_mapped_samples', 0)}")
        log_info(f"    - 平均映射置信度: {mapping_stats.get('avg_mapping_confidence', 0):.3f}")
        
        # 计算系统质量指标
        total_samples = sample_stats.get('total_samples', 0)
        high_confidence = sample_stats.get('high_confidence_samples', 0)
        
        if total_samples > 0:
            quality_score = (high_confidence / total_samples) * 100
            log_info(f"  系统质量评分: {quality_score:.1f}%")
            
            if quality_score >= 80:
                log_success("系统质量: 优秀")
            elif quality_score >= 60:
                log_info("系统质量: 良好")
            else:
                log_warning("系统质量: 需要改进")
        
        # 分析动态探测效果
        detection_rate = (results.dynamic_detection_count / results.total_tests * 100) if results.total_tests > 0 else 0
        log_info(f"  动态探测率: {detection_rate:.1f}% ({results.dynamic_detection_count}/{results.total_tests})")
        
    except Exception as e:
        log_error("分析basket模式失败", error=e)

def save_test_report(results: TestResults, filename: str = "test_report.json"):
    """保存测试报告"""
    try:
        summary = results.get_summary()
        
        # 添加时间戳
        summary["test_time"] = time.strftime("%Y-%m-%d %H:%M:%S")
        summary["total_products_in_file"] = len(nm_ids)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        log_success(f"测试报告已保存到: {filename}")
        
    except Exception as e:
        log_error(f"保存测试报告失败: {filename}", error=e)

def print_detailed_summary(results: TestResults):
    """打印详细的测试总结"""
    summary = results.get_summary()
    
    log_info("=" * 60)
    log_info("详细测试总结")
    log_info("=" * 60)
    
    log_info(f"测试概况:")
    log_info(f"  总测试数: {summary['total_tests']}")
    log_info(f"  成功数: {summary['success_count']}")
    log_info(f"  失败数: {summary['fail_count']}")
    log_info(f"  异常数: {summary['error_count']}")
    log_info(f"  成功率: {summary['success_rate']:.2f}%")
    
    log_info(f"性能指标:")
    log_info(f"  总耗时: {summary['total_time']:.2f}秒")
    log_info(f"  平均耗时: {summary['average_time']:.2f}秒/个")
    log_info(f"  动态探测次数: {summary['dynamic_detection_count']}")
    
    # 显示失败的产品ID
    if summary['failed_ids']:
        log_warning(f"失败的产品ID ({len(summary['failed_ids'])}个):")
        for i, product_id in enumerate(summary['failed_ids'][:10]):  # 只显示前10个
            log_warning(f"  {product_id}")
        if len(summary['failed_ids']) > 10:
            log_warning(f"  ... 还有 {len(summary['failed_ids']) - 10} 个")
    
    # 显示异常的产品ID
    if summary['error_ids']:
        log_error(f"异常的产品ID ({len(summary['error_ids'])}个):")
        for i, (product_id, error) in enumerate(summary['error_ids'][:5]):  # 只显示前5个
            log_error(f"  {product_id}: {error}")
        if len(summary['error_ids']) > 5:
            log_error(f"  ... 还有 {len(summary['error_ids']) - 5} 个")
    
    # 显示慢请求（可能的动态探测）
    if summary['slow_requests']:
        log_info(f"慢请求 (>5秒, 可能经历动态探测) ({len(summary['slow_requests'])}个):")
        for product_id, duration in summary['slow_requests'][:10]:
            log_info(f"  {product_id}: {duration:.2f}秒")
        if len(summary['slow_requests']) > 10:
            log_info(f"  ... 还有 {len(summary['slow_requests']) - 10} 个")

async def main():
    """主测试函数"""
    log_info("开始全面测试所有产品ID")
    log_info(f"总产品数: {len(nm_ids)}")
    
    try:
        # 初始化数据库连接
        await init_pool()
        log_success("数据库连接初始化成功")
        
        # 获取测试前的系统状态
        log_info("=== 测试前系统状态 ===")
        await analyze_basket_patterns(TestResults())  # 传入空结果只为获取当前统计
        
        # 转换产品ID为整数
        product_ids = [int(nm_id) for nm_id in nm_ids]
        
        # 开始全面测试
        start_time = time.time()
        results = await test_products_batch(product_ids, batch_size=3)  # 降低并发数避免过载
        total_duration = time.time() - start_time
        
        log_success(f"全面测试完成，总耗时: {total_duration:.2f}秒")
        
        # 分析结果
        print_detailed_summary(results)
        
        # 分析测试后的basket模式
        log_info("=== 测试后系统状态 ===")
        await analyze_basket_patterns(results)
        
        # 保存测试报告
        save_test_report(results)
        
        # 最终总结
        summary = results.get_summary()
        if summary['success_rate'] >= 95:
            log_success("🎉 测试结果优秀！系统表现非常好")
        elif summary['success_rate'] >= 85:
            log_success("✅ 测试结果良好！系统表现不错")
        elif summary['success_rate'] >= 70:
            log_warning("⚠️ 测试结果一般，系统需要优化")
        else:
            log_error("❌ 测试结果较差，系统需要重大改进")
        
    except Exception as e:
        log_error("全面测试过程中发生错误", error=e)
    finally:
        # 关闭数据库连接
        await close_pool()
        log_info("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(main())
