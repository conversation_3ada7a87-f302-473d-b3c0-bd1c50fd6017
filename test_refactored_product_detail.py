"""
测试重构后的 get_product_detail 函数
"""
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import (
    parse_product_urls,
    fetch_image_description,
    fetch_and_cache_product_info,
    merge_product_data,
    get_product_detail
)

async def test_parse_product_urls():
    """测试URL解析功能"""
    print("🔍 测试URL解析功能...")
    
    test_product_id = 175431678
    url_info = parse_product_urls(test_product_id)
    
    print(f"产品ID: {test_product_id}")
    print(f"产品URL: {url_info['product_url']}")
    print(f"主图URL: {url_info['main_image_url']}")
    print(f"Basket: {url_info['basket']}")
    print("✅ URL解析测试完成\n")
    
    return url_info

async def test_image_description():
    """测试图片描述获取功能"""
    print("🖼️ 测试图片描述获取功能...")
    
    # 使用一个测试图片URL
    test_image_url = "https://basket-12.wbbasket.ru/vol1754/part175431/175431678/images/big/1.webp"
    
    try:
        description = await fetch_image_description(test_image_url)
        print(f"图片URL: {test_image_url}")
        print(f"描述长度: {len(description)}")
        print(f"描述预览: {description[:100]}...")
        print("✅ 图片描述获取测试成功\n")
        return description
    except Exception as e:
        print(f"❌ 图片描述获取测试失败: {e}\n")
        return None

async def test_product_info():
    """测试产品信息获取功能"""
    print("📦 测试产品信息获取功能...")
    
    test_product_id = 175431678
    url_info = parse_product_urls(test_product_id)
    
    try:
        product_info = await fetch_and_cache_product_info(
            url_info["product_url"], 
            test_product_id, 
            force_refresh=True
        )
        print(f"产品ID: {test_product_id}")
        print(f"产品名称: {product_info.get('name', 'N/A')}")
        print(f"产品品牌: {product_info.get('brand', 'N/A')}")
        print(f"产品价格: {product_info.get('priceU', 'N/A')}")
        print("✅ 产品信息获取测试成功\n")
        return product_info
    except Exception as e:
        print(f"❌ 产品信息获取测试失败: {e}\n")
        return None

async def test_complete_flow():
    """测试完整的产品详情获取流程"""
    print("🚀 测试完整的产品详情获取流程...")
    
    test_product_id = 175431678
    
    try:
        result = await get_product_detail(test_product_id, force_refresh=True)
        
        print(f"产品ID: {test_product_id}")
        print(f"产品名称: {result.get('name', 'N/A')}")
        print(f"产品品牌: {result.get('brand', 'N/A')}")
        print(f"主图URL: {result.get('product_img_urls', ['N/A'])[0]}")
        
        # 检查图片描述
        image_descriptions = result.get('image_descriptions', [])
        if image_descriptions:
            first_desc = image_descriptions[0].get('description', 'N/A')
            print(f"图片描述长度: {len(first_desc)}")
            print(f"图片描述预览: {first_desc[:100]}...")
        
        combined_desc = result.get('images_description_text', '')
        print(f"合并描述长度: {len(combined_desc)}")
        
        print("✅ 完整流程测试成功\n")
        return True
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}\n")
        return False

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 重构后的 get_product_detail 函数测试")
    print("=" * 60)
    
    # 1. 测试URL解析
    url_info = await test_parse_product_urls()
    
    # 2. 测试图片描述获取
    description = await test_image_description()
    
    # 3. 测试产品信息获取
    product_info = await test_product_info()
    
    # 4. 测试完整流程
    success = await test_complete_flow()
    
    print("=" * 60)
    if success:
        print("🎉 所有测试完成！重构成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
