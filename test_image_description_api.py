#!/usr/bin/env python3
"""
测试图片描述API
检查为什么图片描述API失败
"""

import asyncio
import aiohttp
import json

async def test_image_description_api():
    """测试图片描述API"""
    
    print("🖼️  测试图片描述API")
    print("=" * 60)
    
    # 测试图片URL
    test_image_url = "https://basket-01.wbbasket.ru/vol3804/part380496/380496894/images/big/1.webp"
    
    print(f"📸 测试图片URL: {test_image_url}")
    
    # 测试不同的API端点
    endpoints = [
        "http://localhost:8000/api/say-img-description/describe",
        "http://localhost:8000/describe", 
        "http://localhost/api/say-img-description/describe"
    ]
    
    for endpoint in endpoints:
        print(f"\n🔍 测试端点: {endpoint}")
        
        try:
            async with aiohttp.ClientSession() as session:
                # 测试GET请求
                params = {"url": test_image_url}
                async with session.get(endpoint, params=params, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    print(f"   GET 请求状态: {response.status}")
                    
                    if response.status == 200:
                        try:
                            result = await response.json()
                            print(f"   ✅ 成功获取描述: {result.get('description', 'N/A')[:100]}...")
                        except:
                            text = await response.text()
                            print(f"   ✅ 成功获取文本: {text[:100]}...")
                    else:
                        error_text = await response.text()
                        print(f"   ❌ 错误响应: {error_text[:200]}")
                        
        except asyncio.TimeoutError:
            print(f"   ⏰ 请求超时")
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

async def test_health_check():
    """测试健康检查"""
    print(f"\n🏥 测试健康检查")
    
    endpoints = [
        "http://localhost:8000/health",
        "http://localhost/api/say-img-description/health"
    ]
    
    for endpoint in endpoints:
        print(f"\n🔍 测试健康检查: {endpoint}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    print(f"   状态: {response.status}")
                    
                    if response.status == 200:
                        try:
                            result = await response.json()
                            print(f"   ✅ 健康状态: {json.dumps(result, ensure_ascii=False, indent=2)}")
                        except:
                            text = await response.text()
                            print(f"   ✅ 响应文本: {text}")
                    else:
                        error_text = await response.text()
                        print(f"   ❌ 错误响应: {error_text}")
                        
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

async def test_consul_registration():
    """测试Consul服务注册"""
    print(f"\n🔗 测试Consul服务注册")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 查看所有服务
            async with session.get("http://localhost:8500/v1/catalog/services") as response:
                if response.status == 200:
                    services = await response.json()
                    print(f"   注册的服务: {list(services.keys())}")
                    
                    # 查看图片描述服务详情
                    if 'say-img-description' in services:
                        async with session.get("http://localhost:8500/v1/catalog/service/say-img-description") as detail_response:
                            if detail_response.status == 200:
                                service_details = await detail_response.json()
                                print(f"   say-img-description 服务详情:")
                                for service in service_details:
                                    print(f"     地址: {service.get('ServiceAddress', 'N/A')}:{service.get('ServicePort', 'N/A')}")
                                    print(f"     标签: {service.get('ServiceTags', [])}")
                    else:
                        print(f"   ⚠️  say-img-description 服务未注册")
                else:
                    print(f"   ❌ 无法获取服务列表: {response.status}")
                    
    except Exception as e:
        print(f"   ❌ Consul查询异常: {e}")

async def main():
    """主函数"""
    print("🔍 图片描述API诊断测试")
    print("=" * 60)
    
    # 测试健康检查
    await test_health_check()
    
    # 测试Consul注册
    await test_consul_registration()
    
    # 测试图片描述API
    await test_image_description_api()
    
    print("\n" + "=" * 60)
    print("📋 诊断完成")

if __name__ == "__main__":
    asyncio.run(main())
