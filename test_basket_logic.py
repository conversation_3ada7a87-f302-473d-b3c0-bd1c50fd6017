#!/usr/bin/env python3
"""
测试新的Basket计算逻辑
"""

import asyncio
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail, _parse_product_url_smart, _parse_product_url
from product_similarity.crud import get_basket_by_short_id, get_basket_stats
from product_similarity.logging import log_info, log_error, log_success, log_warning

async def test_basket_logic_flow():
    """测试basket计算逻辑流程"""
    log_info("=== 测试Basket计算逻辑流程 ===")
    
    # 测试用例：不同范围的产品ID
    test_cases = [
        {"product_id": 199999999, "short_id": 1999, "expected_traditional": "13"},  # 在固定范围内 (1920-2045)
        {"product_id": 200000001, "short_id": 2000, "expected_traditional": "13"},  # 在固定范围内 (1920-2045)
        {"product_id": 449105617, "short_id": 4491, "expected_traditional": "25"},  # 需要动态计算
    ]
    
    for case in test_cases:
        product_id = case["product_id"]
        short_id = case["short_id"]
        expected = case["expected_traditional"]
        
        log_info(f"\n--- 测试产品ID: {product_id} (short_id: {short_id}) ---")
        
        # 1. 测试传统算法
        traditional_result = _parse_product_url(product_id)
        log_info(f"传统算法结果: basket={traditional_result['basket']}")
        assert traditional_result['basket'] == expected, f"传统算法错误: 期望{expected}, 实际{traditional_result['basket']}"
        
        # 2. 查看数据库中是否有范围
        basket_info = await get_basket_by_short_id(short_id)
        if basket_info:
            log_info(f"数据库查询结果: {basket_info}")
        else:
            log_info("数据库中无相关范围")
        
        # 3. 测试智能算法
        smart_result = await _parse_product_url_smart(product_id)
        log_info(f"智能算法结果: basket={smart_result['basket']}, source={smart_result['source']}, certain={smart_result['is_certain']}")
        
        # 4. 测试实际获取产品信息
        try:
            product_info = await get_product_detail(product_id)
            if product_info:
                log_success(f"成功获取产品信息: {product_id}")
            else:
                log_warning(f"产品信息为空: {product_id}")
        except Exception as e:
            log_error(f"获取产品信息失败: {product_id}", error=e)

async def test_database_learning():
    """测试数据库学习过程"""
    log_info("\n=== 测试数据库学习过程 ===")
    
    # 获取初始统计
    initial_stats = await get_basket_stats()
    log_info(f"初始统计: {initial_stats}")
    
    # 测试一些产品，观察数据库变化
    test_ids = [313067529, 376736954, 387577046]  # 从test_product_ids.py中选择
    
    for product_id in test_ids:
        short_id = product_id // 100000
        log_info(f"\n测试产品: {product_id} (short_id: {short_id})")
        
        # 查看处理前的数据库状态
        before_info = await get_basket_by_short_id(short_id)
        log_info(f"处理前数据库状态: {before_info}")
        
        try:
            # 尝试获取产品信息
            product_info = await get_product_detail(product_id)
            if product_info:
                log_success(f"成功获取: {product_id}")
                
                # 查看处理后的数据库状态
                after_info = await get_basket_by_short_id(short_id)
                log_info(f"处理后数据库状态: {after_info}")
                
        except Exception as e:
            log_error(f"获取失败: {product_id}", error=e)
    
    # 获取最终统计
    final_stats = await get_basket_stats()
    log_info(f"\n最终统计: {final_stats}")
    
    # 比较变化
    initial_samples = initial_stats.get('sample_stats', {}).get('total_samples', 0)
    final_samples = final_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"样本数量变化: {initial_samples} → {final_samples} (+{final_samples - initial_samples})")

async def test_range_expansion():
    """测试范围扩展机制"""
    log_info("\n=== 测试范围扩展机制 ===")
    
    # 选择一个basket进行测试
    test_basket = "13"
    
    # 查看当前范围
    stats = await get_basket_stats()
    log_info(f"当前统计: {stats}")
    
    # 模拟添加一些边界样本
    from product_similarity.crud import add_basket_sample
    
    # 添加一些样本
    test_samples = [
        (test_basket, 1995),  # 可能扩展最小值
        (test_basket, 2050),  # 可能扩展最大值
    ]
    
    for basket, short_id in test_samples:
        success = await add_basket_sample(basket, short_id)
        log_info(f"添加样本 basket={basket}, short_id={short_id}: {success}")
        
        # 查看范围变化
        updated_info = await get_basket_by_short_id(short_id)
        log_info(f"更新后查询结果: {updated_info}")

async def main():
    """主测试函数"""
    log_info("开始测试新的Basket计算逻辑")
    
    # 运行各项测试
    await test_basket_logic_flow()
    await test_database_learning()
    await test_range_expansion()
    
    log_success("所有测试完成")

if __name__ == "__main__":
    asyncio.run(main())
