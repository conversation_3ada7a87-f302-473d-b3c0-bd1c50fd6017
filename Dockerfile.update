# 基于现有的 product-similarity 镜像更新代码
FROM product-similarity:latest

# 切换到 root 用户以便复制文件
USER root

# 复制更新的代码文件
COPY src/product_similarity/services/product.py /app/src/product_similarity/services/product.py
COPY src/product_similarity/services/keyword_matching.py /app/src/product_similarity/services/keyword_matching.py
COPY src/product_similarity/services/wildberries_search.py /app/src/product_similarity/services/wildberries_search.py
COPY src/product_similarity/crud.py /app/src/product_similarity/crud.py
COPY src/product_similarity/api.py /app/src/product_similarity/api.py
COPY src/product_similarity/schemas.py /app/src/product_similarity/schemas.py
COPY src/product_similarity/service.py /app/src/product_similarity/service.py

# 确保文件权限正确
RUN chown -R appuser:appuser /app/src/product_similarity/

# 切换回非root用户
USER appuser

# 启动命令
CMD ["python", "run_consul_api.py"]
