#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品比较功能测试脚本
用于测试通过产品ID比较产品相似度的功能
"""

import sys
import traceback
from analyzer import compare_products_by_ids, EndpointConfig, AIProductComparer
from get_product_detail import get_product_detail


def test_get_product_detail():
    """测试产品信息获取功能"""
    print("=== 测试产品信息获取功能 ===")
    
    # 使用一个示例产品ID进行测试（请替换为实际存在的产品ID）
    test_product_id = 123456789
    
    try:
        product_info = get_product_detail(test_product_id)
        
        if product_info is None:
            print(f"❌ 无法获取产品ID {test_product_id} 的信息")
            return False
        else:
            print(f"✅ 成功获取产品ID {test_product_id} 的信息")
            print(f"   产品标题: {product_info.get('name', '未知')}")
            print(f"   产品价格: {product_info.get('priceU', '未知')}")
            print(f"   产品品牌: {product_info.get('brand', '未知')}")
            return True
            
    except Exception as e:
        print(f"❌ 获取产品信息时出错: {e}")
        traceback.print_exc()
        return False


def test_endpoint_config():
    """测试端点配置"""
    print("\n=== 测试端点配置 ===")
    
    try:
        # 创建测试端点配置
        endpoint = EndpointConfig(
            url="http://************:3000",
            api_key="sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",
            model="deepseek/deepseek-chat-v3-0324:free",
            is_multimodal=False
        )
        
        print("✅ 端点配置创建成功")
        print(f"   URL: {endpoint.url}")
        print(f"   模型: {endpoint.model}")
        print(f"   多模态: {endpoint.is_multimodal}")
        
        # 测试headers方法
        headers = endpoint.headers()
        if "Authorization" in headers and "Content-Type" in headers:
            print("✅ 端点headers配置正确")
            return True
        else:
            print("❌ 端点headers配置错误")
            return False
            
    except Exception as e:
        print(f"❌ 端点配置测试失败: {e}")
        traceback.print_exc()
        return False


def test_comparer_initialization():
    """测试比较器初始化"""
    print("\n=== 测试比较器初始化 ===")
    
    try:
        text_endpoints = [
            EndpointConfig(
                url="http://************:3000",
                api_key="sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",
                model="deepseek/deepseek-chat-v3-0324:free",
                is_multimodal=False
            )
        ]
        
        comparer = AIProductComparer(
            text_endpoints=text_endpoints,
            timeout=60,
            temperature=0.1
        )
        
        print("✅ 比较器初始化成功")
        print(f"   超时时间: {comparer.timeout}秒")
        print(f"   温度参数: {comparer.temperature}")
        return comparer
        
    except Exception as e:
        print(f"❌ 比较器初始化失败: {e}")
        traceback.print_exc()
        return None


def test_convenience_function():
    """测试便捷函数"""
    print("\n=== 测试便捷函数 ===")
    
    # 配置端点
    text_endpoints = [
        EndpointConfig(
            url="http://************:3000",
            api_key="sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",
            model="deepseek/deepseek-chat-v3-0324:free",
            is_multimodal=False
        )
    ]
    
    # 测试产品ID（请替换为实际存在的产品ID）
    product_id1 = 123456789
    product_id2 = 987654321
    
    try:
        print(f"正在比较产品 {product_id1} 和 {product_id2}...")
        
        result = compare_products_by_ids(
            product_id1=product_id1,
            product_id2=product_id2,
            text_endpoints=text_endpoints,
            mode="text",
            timeout=30  # 较短的超时时间用于测试
        )
        
        print("✅ 便捷函数调用成功")
        print(f"   相似度评分: {result.get('similar_scores', '未知')}/100")
        print(f"   分析说明: {result.get('reson', '未知')}")
        return True
        
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {e}")
        # 如果是产品信息获取失败，这是预期的（因为使用的是示例ID）
        if "无法获取产品ID" in str(e):
            print("   这是预期的错误（使用了示例产品ID）")
            print("   请替换为实际存在的产品ID进行测试")
        else:
            traceback.print_exc()
        return False


def test_class_method(comparer):
    """测试类方法"""
    print("\n=== 测试类方法 ===")
    
    if comparer is None:
        print("❌ 比较器未初始化，跳过类方法测试")
        return False
    
    # 测试产品ID（请替换为实际存在的产品ID）
    product_id1 = 123456789
    product_id2 = 987654321
    
    try:
        print(f"正在使用类方法比较产品 {product_id1} 和 {product_id2}...")
        
        result = comparer.compare_by_ids(
            product_id1=product_id1,
            product_id2=product_id2,
            mode="text"
        )
        
        print("✅ 类方法调用成功")
        print(f"   相似度评分: {result.get('similar_scores', '未知')}/100")
        print(f"   分析说明: {result.get('reson', '未知')}")
        return True
        
    except Exception as e:
        print(f"❌ 类方法测试失败: {e}")
        # 如果是产品信息获取失败，这是预期的（因为使用的是示例ID）
        if "无法获取产品ID" in str(e):
            print("   这是预期的错误（使用了示例产品ID）")
            print("   请替换为实际存在的产品ID进行测试")
        else:
            traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("产品比较功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 1. 测试产品信息获取
    test_results.append(("产品信息获取", test_get_product_detail()))
    
    # 2. 测试端点配置
    test_results.append(("端点配置", test_endpoint_config()))
    
    # 3. 测试比较器初始化
    comparer = test_comparer_initialization()
    test_results.append(("比较器初始化", comparer is not None))
    
    # 4. 测试便捷函数
    test_results.append(("便捷函数", test_convenience_function()))
    
    # 5. 测试类方法
    test_results.append(("类方法", test_class_method(comparer)))
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！功能正常工作。")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
        print("\n注意事项:")
        print("1. 请确保使用实际存在的产品ID进行测试")
        print("2. 检查API端点配置是否正确")
        print("3. 确认网络连接正常")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
