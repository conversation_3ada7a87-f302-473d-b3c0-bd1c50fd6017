#!/usr/bin/env python3
"""
测试关键词匹配 API 端点
"""
import asyncio
import httpx
import json
from test_product_ids import nm_ids


async def test_keyword_matching_api():
    """测试关键词匹配 API"""
    
    # API 基础URL
    base_url = "http://localhost:8000"
    
    # 测试数据
    test_data = {
        "keyword": "настенный светильник для ванной",  # 浴室壁灯的俄文
        "target_product_id": int(nm_ids[1])  # 使用第二个真实产品ID
    }
    
    print(f"测试关键词匹配 API")
    print(f"关键词: {test_data['keyword']}")
    print(f"目标产品ID: {test_data['target_product_id']}")
    print("-" * 50)
    
    try:
        async with httpx.AsyncClient(timeout=300) as client:
            # 发送POST请求
            response = await client.post(
                f"{base_url}/keyword-matching",
                json=test_data
            )
            
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("API调用成功！")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 测试第二次调用（应该从缓存获取）
                print("\n" + "=" * 50)
                print("测试第二次调用（应该从缓存获取）")
                print("=" * 50)
                
                response2 = await client.post(
                    f"{base_url}/keyword-matching",
                    json=test_data
                )
                
                if response2.status_code == 200:
                    result2 = response2.json()
                    print("第二次API调用成功！")
                    print(f"是否来自缓存: {result2.get('data', {}).get('from_cache', False)}")
                    print(f"平均相似度: {result2.get('data', {}).get('avg_similarity', 0)}")
                else:
                    print(f"第二次API调用失败: {response2.status_code}")
                    print(response2.text)
                    
            else:
                print(f"API调用失败: {response.status_code}")
                print(response.text)
                
    except httpx.ConnectError:
        print("无法连接到API服务器。请确保服务器正在运行在 http://localhost:8000")
        print("启动服务器命令: python -m src.product_similarity.api")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_keyword_matching_api())
