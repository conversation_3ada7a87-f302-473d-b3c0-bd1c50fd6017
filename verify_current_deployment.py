#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证当前部署状态
"""

import requests
import json
import time
from datetime import datetime

def verify_deployment():
    """验证当前部署状态"""
    print("🔍 验证当前部署状态")
    print(f"⏰ 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    results = []
    
    # 1. 健康检查
    print("1. 健康检查测试")
    try:
        response = requests.get("http://localhost:8001/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 健康检查通过")
            print(f"   📊 服务状态: {data.get('status', 'N/A')}")
            print(f"   📊 数据库: {data.get('checks', {}).get('database', 'N/A')}")
            print(f"   📊 Consul: {data.get('checks', {}).get('consul', 'N/A')}")
            results.append(True)
        else:
            print(f"   ❌ 健康检查失败: {response.status_code}")
            results.append(False)
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
        results.append(False)
    
    # 2. keywordMatching功能测试
    print("\n2. keywordMatching功能测试")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8001/keyword-matching",
            json={
                "keyword": "люстра",
                "target_product_id": 253486273
            },
            timeout=60
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ keywordMatching功能正常")
            print(f"   📊 处理时间: {end_time - start_time:.2f}秒")
            print(f"   📊 相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            print(f"   📊 相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
            print(f"   📊 缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
            results.append(True)
        else:
            print(f"   ❌ keywordMatching功能失败: {response.status_code}")
            print(f"   📄 响应: {response.text}")
            results.append(False)
    except Exception as e:
        print(f"   ❌ keywordMatching功能异常: {e}")
        results.append(False)
    
    # 3. 图片描述集成测试
    print("\n3. 图片描述集成测试")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8001/keyword-matching",
            json={
                "keyword": "светильник LED",
                "target_product_id": 253486273
            },
            timeout=120
        )
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 图片描述集成正常")
            print(f"   📊 处理时间: {processing_time:.2f}秒")
            if processing_time > 20:
                print("   ✅ 长处理时间表明图片描述API被调用")
            print(f"   📊 相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            results.append(True)
        else:
            print(f"   ❌ 图片描述集成失败: {response.status_code}")
            results.append(False)
    except Exception as e:
        print(f"   ❌ 图片描述集成异常: {e}")
        results.append(False)
    
    # 4. Consul服务注册检查
    print("\n4. Consul服务注册检查")
    try:
        response = requests.get("http://localhost:8500/v1/catalog/service/product-similarity", timeout=10)
        if response.status_code == 200:
            services = response.json()
            if services:
                service = services[0]
                print("   ✅ Consul服务注册正常")
                print(f"   📍 服务地址: {service.get('ServiceAddress', 'N/A')}:{service.get('ServicePort', 'N/A')}")
                print(f"   🏷️ 服务标签: {service.get('ServiceTags', [])}")
                results.append(True)
            else:
                print("   ❌ 服务未在Consul中注册")
                results.append(False)
        else:
            print(f"   ❌ Consul查询失败: {response.status_code}")
            results.append(False)
    except Exception as e:
        print(f"   ❌ Consul检查异常: {e}")
        results.append(False)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 部署验证结果:")
    
    test_names = [
        "健康检查",
        "keywordMatching功能",
        "图片描述集成",
        "Consul服务注册"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 正常" if result else "❌ 异常"
        print(f"   {i+1}. {name}: {status}")
    
    print(f"\n🎯 总体状态: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("🎉 当前部署完全正常！")
        return True
    elif passed >= total * 0.75:
        print("⚠️ 当前部署基本正常，部分功能需要优化")
        return True
    else:
        print("❌ 当前部署存在严重问题")
        return False

if __name__ == "__main__":
    verify_deployment()
