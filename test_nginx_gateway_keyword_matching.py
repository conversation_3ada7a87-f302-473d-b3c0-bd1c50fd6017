#!/usr/bin/env python3
"""
测试通过Nginx网关访问关键词匹配API
"""
import asyncio
import httpx
import json
import time
from test_product_ids import nm_ids

async def test_nginx_gateway():
    """测试通过Nginx网关访问关键词匹配API"""
    
    # 网关URL
    gateway_url = "http://localhost/api/product-similarity"
    
    # 测试数据
    test_data = {
        "keyword": "люстра на потолок",  # 吊灯
        "target_product_id": 253486273  # 目标产品ID
    }
    
    print("🌐 测试通过Nginx网关访问关键词匹配API")
    print("=" * 60)
    print(f"网关URL: {gateway_url}")
    print(f"测试关键词: {test_data['keyword']}")
    print(f"目标产品ID: {test_data['target_product_id']}")
    print("=" * 60)
    
    try:
        async with httpx.AsyncClient(timeout=300) as client:
            
            # 1. 测试网关健康检查
            print("\n📊 1. 测试网关健康检查")
            print("-" * 40)
            try:
                health_response = await client.get(f"{gateway_url}/health")
                print(f"健康检查状态码: {health_response.status_code}")
                if health_response.status_code == 200:
                    health_data = health_response.json()
                    print("✅ 网关健康检查通过")
                    print(f"服务状态: {health_data.get('status', 'unknown')}")
                else:
                    print(f"❌ 网关健康检查失败: {health_response.status_code}")
                    print(f"响应内容: {health_response.text}")
            except Exception as e:
                print(f"❌ 网关健康检查异常: {e}")
            
            # 2. 测试服务信息
            print("\n📋 2. 测试服务信息")
            print("-" * 40)
            try:
                info_response = await client.get(f"{gateway_url}/info")
                print(f"服务信息状态码: {info_response.status_code}")
                if info_response.status_code == 200:
                    info_data = info_response.json()
                    print("✅ 服务信息获取成功")
                    print(f"服务名称: {info_data.get('service_name', 'unknown')}")
                    print(f"服务版本: {info_data.get('version', 'unknown')}")
                else:
                    print(f"❌ 服务信息获取失败: {info_response.status_code}")
            except Exception as e:
                print(f"❌ 服务信息获取异常: {e}")
            
            # 3. 测试关键词匹配API
            print("\n🎯 3. 测试关键词匹配API")
            print("-" * 40)
            
            start_time = time.time()
            
            try:
                # 发送POST请求到网关
                response = await client.post(
                    f"{gateway_url}/keyword-matching",
                    json=test_data,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"请求状态码: {response.status_code}")
                print(f"请求耗时: {duration:.2f}秒")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ 关键词匹配API调用成功！")
                    print("\n📊 响应结果:")
                    print(json.dumps(result, indent=2, ensure_ascii=False))
                    
                    # 解析结果
                    if result.get('status') == 'success':
                        data = result.get('data', {})
                        print(f"\n📈 业务结果:")
                        print(f"  关键词: {data.get('keyword')}")
                        print(f"  目标产品ID: {data.get('target_product_id')}")
                        print(f"  平均相似度: {data.get('avg_similarity')}分")
                        print(f"  相似产品数(>65分): {data.get('similar_count')}")
                        print(f"  竞品数(>80分): {data.get('competitor_count')}")
                        print(f"  有效样本数: {data.get('valid_scores')}")
                        print(f"  是否来自缓存: {data.get('from_cache')}")
                        print(f"  搜索产品数: {data.get('search_products_count')}")
                        print(f"  比较产品数: {data.get('compared_products_count')}")
                    
                elif response.status_code == 404:
                    print("❌ API端点未找到 (404)")
                    print("可能原因:")
                    print("  1. Nginx配置中没有正确的路由规则")
                    print("  2. 服务未正确注册到Consul")
                    print("  3. consul-template未生成正确的配置")
                    print(f"响应内容: {response.text}")
                    
                elif response.status_code == 502:
                    print("❌ 网关错误 (502 Bad Gateway)")
                    print("可能原因:")
                    print("  1. 后端服务未启动")
                    print("  2. 服务健康检查失败")
                    print("  3. 网络连接问题")
                    print(f"响应内容: {response.text}")
                    
                elif response.status_code == 503:
                    print("❌ 服务不可用 (503 Service Unavailable)")
                    print("可能原因:")
                    print("  1. 服务过载")
                    print("  2. 服务正在启动中")
                    print("  3. 健康检查失败")
                    print(f"响应内容: {response.text}")
                    
                else:
                    print(f"❌ 请求失败: {response.status_code}")
                    print(f"响应内容: {response.text}")
                    
            except httpx.ConnectError as e:
                print(f"❌ 连接错误: {e}")
                print("可能原因:")
                print("  1. Nginx网关未启动")
                print("  2. 端口80未开放")
                print("  3. 网络连接问题")
                
            except httpx.TimeoutException as e:
                print(f"❌ 请求超时: {e}")
                print("可能原因:")
                print("  1. 后端服务响应慢")
                print("  2. 网络延迟高")
                print("  3. 服务处理时间过长")
                
            except Exception as e:
                print(f"❌ 请求异常: {e}")
                import traceback
                traceback.print_exc()
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_direct_access():
    """测试直接访问服务（对比测试）"""
    
    print("\n" + "=" * 60)
    print("🔗 对比测试：直接访问服务")
    print("=" * 60)
    
    # 直接访问URL
    direct_url = "http://localhost:8000"
    
    test_data = {
        "keyword": "люстра на потолок",
        "target_product_id": 253486273
    }
    
    try:
        async with httpx.AsyncClient(timeout=300) as client:
            
            # 测试健康检查
            print("\n📊 直接访问健康检查")
            print("-" * 40)
            try:
                health_response = await client.get(f"{direct_url}/health")
                print(f"健康检查状态码: {health_response.status_code}")
                if health_response.status_code == 200:
                    print("✅ 直接访问健康检查通过")
                else:
                    print(f"❌ 直接访问健康检查失败: {health_response.status_code}")
            except Exception as e:
                print(f"❌ 直接访问健康检查异常: {e}")
            
            # 测试关键词匹配API
            print("\n🎯 直接访问关键词匹配API")
            print("-" * 40)
            
            try:
                start_time = time.time()
                response = await client.post(
                    f"{direct_url}/keyword-matching",
                    json=test_data
                )
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"请求状态码: {response.status_code}")
                print(f"请求耗时: {duration:.2f}秒")
                
                if response.status_code == 200:
                    print("✅ 直接访问关键词匹配API成功！")
                else:
                    print(f"❌ 直接访问失败: {response.status_code}")
                    print(f"响应内容: {response.text}")
                    
            except Exception as e:
                print(f"❌ 直接访问异常: {e}")
    
    except Exception as e:
        print(f"❌ 直接访问测试失败: {e}")

async def main():
    """主测试函数"""
    print("🧪 Nginx网关关键词匹配API测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 测试网关访问
    await test_nginx_gateway()
    
    # 测试直接访问（对比）
    await test_direct_access()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
