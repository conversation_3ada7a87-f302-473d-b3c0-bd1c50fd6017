#!/usr/bin/env python3
"""
智能Basket系统使用示例
展示如何在实际应用中使用智能basket功能
"""

import asyncio
import sys
import os
import time
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail, get_products_batch
from product_similarity.crud import get_basket_stats
from product_similarity.db import init_pool, close_pool
from product_similarity.logging import log_success, log_error, log_info, log_warning
from test_product_ids import nm_ids

async def example_single_product():
    """示例：获取单个产品信息"""
    log_info("=== 单个产品获取示例 ===")
    
    product_id = int(nm_ids[0])
    log_info(f"获取产品信息: {product_id}")
    
    start_time = time.time()
    try:
        product_info = await get_product_detail(product_id)
        end_time = time.time()
        
        if product_info:
            log_success(f"成功获取产品信息: {product_id}")
            log_info(f"产品名称: {product_info.get('name', 'N/A')}")
            log_info(f"品牌: {product_info.get('brand', 'N/A')}")
            log_info(f"价格: {product_info.get('priceU', 'N/A')}")
            log_info(f"获取耗时: {end_time - start_time:.2f}秒")
        else:
            log_error(f"获取产品信息失败: {product_id}")
            
    except Exception as e:
        log_error(f"获取产品信息异常: {product_id}", error=e)

async def example_batch_products():
    """示例：批量获取产品信息"""
    log_info("=== 批量产品获取示例 ===")
    
    # 选择前10个产品进行批量获取
    product_ids = [int(nm_id) for nm_id in nm_ids[:10]]
    log_info(f"批量获取 {len(product_ids)} 个产品信息")
    
    start_time = time.time()
    try:
        products = await get_products_batch(product_ids)
        end_time = time.time()
        
        success_count = len(products)
        log_success(f"批量获取完成: {success_count}/{len(product_ids)} 成功")
        log_info(f"批量获取耗时: {end_time - start_time:.2f}秒")
        log_info(f"平均每个产品耗时: {(end_time - start_time) / len(product_ids):.2f}秒")
        
        # 显示部分产品信息
        for i, (product_id, product_info) in enumerate(products.items()):
            if i < 3:  # 只显示前3个
                log_info(f"产品 {product_id}: {product_info.get('name', 'N/A')[:50]}...")
                
    except Exception as e:
        log_error("批量获取产品信息异常", error=e)

async def example_cache_performance():
    """示例：缓存性能测试"""
    log_info("=== 缓存性能测试示例 ===")
    
    product_id = int(nm_ids[0])
    
    # 第一次获取（从远程）
    log_info("第一次获取（从远程）")
    start_time = time.time()
    product_info1 = await get_product_detail(product_id, force_refresh=True)
    first_time = time.time() - start_time
    log_info(f"第一次获取耗时: {first_time:.2f}秒")
    
    # 第二次获取（从缓存）
    log_info("第二次获取（从缓存）")
    start_time = time.time()
    product_info2 = await get_product_detail(product_id)
    second_time = time.time() - start_time
    log_info(f"第二次获取耗时: {second_time:.2f}秒")
    
    # 性能提升
    if second_time > 0:
        speedup = first_time / second_time
        log_success(f"缓存性能提升: {speedup:.1f}倍")
    
    # 验证数据一致性
    if product_info1 and product_info2:
        if product_info1.get('name') == product_info2.get('name'):
            log_success("缓存数据一致性验证通过")
        else:
            log_warning("缓存数据一致性验证失败")

async def example_error_handling():
    """示例：错误处理和动态探测"""
    log_info("=== 错误处理和动态探测示例 ===")
    
    # 选择一些可能需要动态探测的产品ID
    test_ids = [int(nm_id) for nm_id in nm_ids[10:15]]
    
    for product_id in test_ids:
        log_info(f"测试产品ID: {product_id}")
        
        start_time = time.time()
        try:
            product_info = await get_product_detail(product_id)
            end_time = time.time()
            
            if product_info:
                log_success(f"成功获取: {product_id} (耗时: {end_time - start_time:.2f}秒)")
                
                # 如果耗时较长，可能经历了动态探测
                if end_time - start_time > 5:
                    log_info("  ↳ 可能经历了动态探测过程")
            else:
                log_error(f"获取失败: {product_id}")
                
        except Exception as e:
            log_error(f"获取异常: {product_id}", error=e)

async def example_basket_statistics():
    """示例：basket统计信息"""
    log_info("=== Basket统计信息示例 ===")
    
    try:
        stats = await get_basket_stats()
        
        log_info("当前Basket系统状态:")
        log_info(f"  样本数据:")
        log_info(f"    - 总样本数: {stats['sample_stats'].get('total_samples', 0)}")
        log_info(f"    - 唯一basket数: {stats['sample_stats'].get('unique_baskets', 0)}")
        log_info(f"    - 平均置信度: {stats['sample_stats'].get('avg_confidence', 0):.3f}")
        log_info(f"    - 高置信度样本: {stats['sample_stats'].get('high_confidence_samples', 0)}")
        log_info(f"    - 低置信度样本: {stats['sample_stats'].get('low_confidence_samples', 0)}")
        
        log_info(f"  范围映射:")
        log_info(f"    - 总映射数: {stats['mapping_stats'].get('total_mappings', 0)}")
        log_info(f"    - 映射样本总数: {stats['mapping_stats'].get('total_mapped_samples', 0)}")
        log_info(f"    - 平均映射置信度: {stats['mapping_stats'].get('avg_mapping_confidence', 0):.3f}")
        
        # 计算覆盖率
        total_samples = stats['sample_stats'].get('total_samples', 0)
        high_confidence = stats['sample_stats'].get('high_confidence_samples', 0)
        if total_samples > 0:
            coverage = (high_confidence / total_samples) * 100
            log_info(f"  系统质量:")
            log_info(f"    - 高置信度覆盖率: {coverage:.1f}%")
            
            if coverage >= 80:
                log_success("系统质量优秀")
            elif coverage >= 60:
                log_info("系统质量良好")
            else:
                log_warning("系统质量需要改进")
        
    except Exception as e:
        log_error("获取basket统计信息失败", error=e)

async def example_comprehensive_test():
    """示例：综合测试"""
    log_info("=== 综合测试示例 ===")
    
    # 选择不同范围的产品ID进行测试
    test_groups = {
        "低ID范围": [int(nm_id) for nm_id in nm_ids[:5] if int(nm_id) < 200000000],
        "中ID范围": [int(nm_id) for nm_id in nm_ids[:10] if 200000000 <= int(nm_id) < 400000000],
        "高ID范围": [int(nm_id) for nm_id in nm_ids[:10] if int(nm_id) >= 400000000]
    }
    
    total_success = 0
    total_tests = 0
    
    for group_name, product_ids in test_groups.items():
        if not product_ids:
            continue
            
        log_info(f"测试组: {group_name} ({len(product_ids)} 个产品)")
        
        group_success = 0
        start_time = time.time()
        
        for product_id in product_ids:
            try:
                product_info = await get_product_detail(product_id)
                if product_info:
                    group_success += 1
                    total_success += 1
                total_tests += 1
                
            except Exception as e:
                log_error(f"测试失败: {product_id}", error=e)
                total_tests += 1
        
        end_time = time.time()
        group_time = end_time - start_time
        
        success_rate = (group_success / len(product_ids)) * 100 if product_ids else 0
        avg_time = group_time / len(product_ids) if product_ids else 0
        
        log_info(f"  结果: {group_success}/{len(product_ids)} 成功 ({success_rate:.1f}%)")
        log_info(f"  耗时: {group_time:.2f}秒 (平均 {avg_time:.2f}秒/个)")
    
    # 总体结果
    overall_success_rate = (total_success / total_tests) * 100 if total_tests > 0 else 0
    log_success(f"综合测试完成: {total_success}/{total_tests} 成功 ({overall_success_rate:.1f}%)")

async def main():
    """主函数"""
    try:
        # 初始化数据库连接
        await init_pool()
        log_success("智能Basket系统初始化成功")
        
        # 运行各种示例
        await example_single_product()
        await example_batch_products()
        await example_cache_performance()
        await example_error_handling()
        await example_basket_statistics()
        await example_comprehensive_test()
        
        log_success("所有示例运行完成")
        
    except Exception as e:
        log_error("示例运行过程中发生错误", error=e)
    finally:
        # 关闭数据库连接
        await close_pool()
        log_info("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(main())
