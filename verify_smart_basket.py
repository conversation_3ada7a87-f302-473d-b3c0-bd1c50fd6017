#!/usr/bin/env python3
"""
验证智能Basket系统基本功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail, _parse_product_url_smart
from product_similarity.crud import get_basket_stats
from product_similarity.db import init_pool, close_pool
from product_similarity.logging import log_success, log_error, log_info
from test_product_ids import nm_ids

async def verify_basic_functionality():
    """验证基本功能"""
    log_info("=== 验证智能Basket系统基本功能 ===")
    
    try:
        # 初始化数据库连接
        await init_pool()
        log_success("数据库连接初始化成功")
        
        # 测试智能URL解析
        test_id = int(nm_ids[0])
        log_info(f"测试智能URL解析: {test_id}")
        
        smart_data = await _parse_product_url_smart(test_id)
        log_info(f"解析结果: basket={smart_data['basket']}, confidence={smart_data['confidence']}")
        
        # 测试产品信息获取
        log_info(f"测试产品信息获取: {test_id}")
        product_info = await get_product_detail(test_id)
        
        if product_info:
            log_success(f"成功获取产品信息: {product_info.get('name', 'N/A')[:50]}...")
        else:
            log_error("获取产品信息失败")
        
        # 获取系统统计
        stats = await get_basket_stats()
        log_info(f"系统统计: 样本数={stats['sample_stats'].get('total_samples', 0)}, "
                f"映射数={stats['mapping_stats'].get('total_mappings', 0)}")
        
        log_success("基本功能验证完成")
        
    except Exception as e:
        log_error("基本功能验证失败", error=e)
    finally:
        await close_pool()
        log_info("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(verify_basic_functionality())
