events {
    worker_connections 1024;
}

http {
    upstream product_similarity {
        # Consul服务发现会自动更新这里的服务器列表
        # 这里是静态配置，生产环境建议使用consul-template动态生成
        server product-similarity:8000;
    }

    server {
        listen 80;
        server_name localhost;

        # 健康检查端点
        location /health {
            proxy_pass http://product_similarity/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API路由
        location /api/product-similarity/ {
            rewrite ^/api/product-similarity/(.*) /$1 break;
            proxy_pass http://product_similarity;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # 根路径重定向到API文档
        location / {
            return 301 /api/product-similarity/docs;
        }

        # 错误页面
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
