#!/usr/bin/env python3
"""
测试short_id计算是否正确
验证 product_id // 100000 的计算结果
"""

def test_short_id_calculation():
    """测试short_id计算"""
    test_cases = [
        (449105617, 4491),  # 449105617 // 100000 = 4491
        (313067529, 3130),  # 313067529 // 100000 = 3130
        (376736954, 3767),  # 376736954 // 100000 = 3767
        (123456789, 1234),  # 123456789 // 100000 = 1234
        (99999, 0),         # 99999 // 100000 = 0
        (100000, 1),        # 100000 // 100000 = 1
        (199999, 1),        # 199999 // 100000 = 1
        (200000, 2),        # 200000 // 100000 = 2
    ]
    
    print("测试 short_id = product_id // 100000 计算:")
    print("=" * 50)
    
    for product_id, expected_short_id in test_cases:
        calculated_short_id = product_id // 100000
        status = "✅" if calculated_short_id == expected_short_id else "❌"
        
        print(f"{status} product_id: {product_id:>10} → short_id: {calculated_short_id:>4} (期望: {expected_short_id:>4})")
        
        if calculated_short_id != expected_short_id:
            print(f"   错误！计算结果 {calculated_short_id} != 期望结果 {expected_short_id}")
    
    print("\n验证整数除法 // 的行为:")
    print("=" * 30)
    
    # 验证整数除法确实是向下取整（截断小数部分）
    test_divisions = [
        (449105617, 100000),
        (313067529, 100000),
        (376736954, 100000),
    ]
    
    for dividend, divisor in test_divisions:
        float_result = dividend / divisor  # 浮点除法
        int_result = dividend // divisor   # 整数除法
        
        print(f"{dividend} / {divisor} = {float_result:.6f}")
        print(f"{dividend} // {divisor} = {int_result} (整数除法，截断小数)")
        print(f"截断验证: int({float_result}) = {int(float_result)}")
        print()

if __name__ == "__main__":
    test_short_id_calculation()
