#!/usr/bin/env python3
"""
使用test_keywords.py中的关键词进行持续并发测试
保持20个任务同时运行，任务结束后立即补充新任务
"""
import asyncio
import aiohttp
import time
import random
from typing import List, Dict, Any
import sys
sys.path.append('.')

# 导入关键词数据
try:
    from test_keywords import keyword_info
    print(f"✅ 成功加载 {len(keyword_info)} 个关键词")
except ImportError:
    print("❌ 无法导入 test_keywords.py")
    sys.exit(1)

class ContinuousConcurrentTester:
    def __init__(self, target_product_id: int = 253486273, max_concurrent: int = 20):
        self.target_product_id = target_product_id
        self.max_concurrent = max_concurrent
        self.base_url = "http://localhost:8001"
        self.gateway_url = "http://localhost/api/product-similarity"
        
        # 统计数据
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.start_time = time.time()
        
        # 关键词队列
        self.keyword_queue = list(keyword_info)
        random.shuffle(self.keyword_queue)  # 随机打乱顺序
        self.current_index = 0
        
        # 活跃任务跟踪
        self.active_tasks = set()
        
    def get_next_keyword(self) -> Dict[str, Any]:
        """获取下一个关键词，循环使用"""
        if self.current_index >= len(self.keyword_queue):
            # 重新打乱并重置索引
            random.shuffle(self.keyword_queue)
            self.current_index = 0
            print(f"🔄 关键词队列重新开始，已处理 {self.total_requests} 个请求")
        
        keyword_data = self.keyword_queue[self.current_index]
        self.current_index += 1
        return keyword_data
    
    async def test_keyword_matching(self, session: aiohttp.ClientSession, keyword_data: Dict[str, Any], task_id: int) -> Dict[str, Any]:
        """测试单个关键词匹配"""
        keyword = keyword_data["keyword"]
        count = keyword_data["count"]
        
        start_time = time.time()
        
        try:
            # 准备请求数据
            payload = {
                "keyword": keyword,
                "target_product_id": self.target_product_id,
                "limit": 50
            }
            
            # 随机选择访问方式（直接访问或网关访问）
            if random.choice([True, False]):
                url = f"{self.base_url}/keyword-matching"
                access_type = "直接"
            else:
                url = f"{self.gateway_url}/keyword-matching"
                access_type = "网关"
            
            # 发送请求
            async with session.post(url, json=payload, timeout=aiohttp.ClientTimeout(total=120)) as response:
                if response.status == 200:
                    result = await response.json()
                    duration = time.time() - start_time
                    
                    # 解析结果
                    avg_similarity = result.get("average_similarity", 0)
                    similar_count = result.get("similar_products_count", 0)
                    competitor_count = result.get("competitor_products_count", 0)
                    is_cached = result.get("cached", False)
                    
                    self.successful_requests += 1
                    
                    return {
                        "success": True,
                        "task_id": task_id,
                        "keyword": keyword,
                        "count": count,
                        "access_type": access_type,
                        "duration": duration,
                        "avg_similarity": avg_similarity,
                        "similar_count": similar_count,
                        "competitor_count": competitor_count,
                        "cached": is_cached,
                        "status": response.status
                    }
                else:
                    error_text = await response.text()
                    self.failed_requests += 1
                    
                    return {
                        "success": False,
                        "task_id": task_id,
                        "keyword": keyword,
                        "count": count,
                        "access_type": access_type,
                        "duration": time.time() - start_time,
                        "error": f"HTTP {response.status}: {error_text[:100]}",
                        "status": response.status
                    }
                    
        except Exception as e:
            self.failed_requests += 1
            return {
                "success": False,
                "task_id": task_id,
                "keyword": keyword,
                "count": count,
                "access_type": "未知",
                "duration": time.time() - start_time,
                "error": str(e),
                "status": 0
            }
    
    async def worker_task(self, session: aiohttp.ClientSession, task_id: int):
        """工作任务，持续处理关键词"""
        while True:
            try:
                # 获取下一个关键词
                keyword_data = self.get_next_keyword()
                
                # 执行测试
                result = await self.test_keyword_matching(session, keyword_data, task_id)
                self.total_requests += 1
                
                # 打印结果
                if result["success"]:
                    cache_indicator = "🟢缓存" if result["cached"] else "🔵新请求"
                    print(f"✅ 任务{task_id:2d} | {result['keyword'][:20]:20s} | "
                          f"{result['access_type']:2s} | {result['duration']:5.2f}s | "
                          f"相似度:{result['avg_similarity']:2.0f} | 相似:{result['similar_count']:2d} | "
                          f"竞品:{result['competitor_count']:2d} | {cache_indicator}")
                else:
                    print(f"❌ 任务{task_id:2d} | {result['keyword'][:20]:20s} | "
                          f"{result['access_type']:2s} | {result['duration']:5.2f}s | "
                          f"错误: {result['error'][:30]}")
                
                # 每10个请求打印统计信息
                if self.total_requests % 10 == 0:
                    elapsed = time.time() - self.start_time
                    success_rate = (self.successful_requests / self.total_requests) * 100
                    avg_rps = self.total_requests / elapsed
                    print(f"\n📊 统计 | 总请求:{self.total_requests} | 成功:{self.successful_requests} | "
                          f"失败:{self.failed_requests} | 成功率:{success_rate:.1f}% | "
                          f"平均RPS:{avg_rps:.2f} | 运行时间:{elapsed:.1f}s\n")
                
                # 短暂延迟避免过于频繁的请求
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"❌ 任务{task_id} 异常: {e}")
                await asyncio.sleep(1)  # 异常时等待更长时间
    
    async def run_continuous_test(self):
        """运行持续并发测试"""
        print(f"🚀 开始持续并发测试")
        print(f"📋 目标产品ID: {self.target_product_id}")
        print(f"🔢 并发任务数: {self.max_concurrent}")
        print(f"📚 关键词总数: {len(self.keyword_queue)}")
        print(f"🌐 测试地址: {self.base_url} 和 {self.gateway_url}")
        print("=" * 80)
        print(f"{'任务':4s} | {'关键词':20s} | {'方式':2s} | {'时间':5s} | {'相似度':4s} | {'相似':2s} | {'竞品':2s} | {'状态':6s}")
        print("-" * 80)
        
        # 创建HTTP会话
        connector = aiohttp.TCPConnector(limit=50, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=120)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # 创建并启动工作任务
            tasks = []
            for i in range(self.max_concurrent):
                task = asyncio.create_task(self.worker_task(session, i + 1))
                tasks.append(task)
            
            try:
                # 等待所有任务完成（实际上会一直运行）
                await asyncio.gather(*tasks)
            except KeyboardInterrupt:
                print(f"\n\n🛑 收到中断信号，正在停止测试...")
                
                # 取消所有任务
                for task in tasks:
                    task.cancel()
                
                # 等待任务清理
                await asyncio.gather(*tasks, return_exceptions=True)
                
                # 打印最终统计
                elapsed = time.time() - self.start_time
                success_rate = (self.successful_requests / self.total_requests) * 100 if self.total_requests > 0 else 0
                avg_rps = self.total_requests / elapsed if elapsed > 0 else 0
                
                print(f"\n📊 最终统计:")
                print(f"   总请求数: {self.total_requests}")
                print(f"   成功请求: {self.successful_requests}")
                print(f"   失败请求: {self.failed_requests}")
                print(f"   成功率: {success_rate:.2f}%")
                print(f"   平均RPS: {avg_rps:.2f}")
                print(f"   总运行时间: {elapsed:.2f}秒")
                print(f"\n✅ 测试完成！")

async def main():
    """主函数"""
    tester = ContinuousConcurrentTester(
        target_product_id=253486273,
        max_concurrent=20
    )
    
    await tester.run_continuous_test()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
