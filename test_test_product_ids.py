#!/usr/bin/env python3
"""
产品相似度微服务测试脚本
使用随机产品ID测试整个项目功能
"""
import asyncio
import random
import sys
import os
from pathlib import Path
import httpx
import json
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入产品ID列表
from test_product_ids import nm_ids

class ProductSimilarityTester:
    """产品相似度微服务测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip("/")
        self.client = None
        self.test_results = []
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.client = httpx.AsyncClient(timeout=60.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.client:
            await self.client.aclose()
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "data": data
        })
    
    def get_random_product_ids(self, count: int = 2) -> List[int]:
        """获取随机产品ID"""
        # 转换字符串ID为整数
        int_ids = [int(id_str) for id_str in nm_ids if id_str.strip()]
        return random.sample(int_ids, min(count, len(int_ids)))
    
    async def test_health_check(self):
        """测试健康检查端点"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") in ["healthy", "degraded"]:
                    self.log_test("健康检查", True, f"状态: {data.get('status')}")
                else:
                    self.log_test("健康检查", False, f"状态异常: {data.get('status')}")
            else:
                self.log_test("健康检查", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("健康检查", False, f"请求异常: {e}")
    
    async def test_service_info(self):
        """测试服务信息端点"""
        try:
            response = await self.client.get(f"{self.base_url}/info")
            
            if response.status_code == 200:
                data = response.json()
                service_name = data.get("service_name", "")
                if "product-similarity" in service_name:
                    self.log_test("服务信息", True, f"服务名: {service_name}")
                else:
                    self.log_test("服务信息", False, f"服务名异常: {service_name}")
            else:
                self.log_test("服务信息", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("服务信息", False, f"请求异常: {e}")
    
    async def test_get_product_info(self):
        """测试获取产品信息"""
        try:
            product_id = self.get_random_product_ids(1)[0]
            response = await self.client.get(f"{self.base_url}/product/{product_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success" and data.get("data"):
                    self.log_test("获取产品信息", True, f"产品ID: {product_id}")
                else:
                    self.log_test("获取产品信息", False, f"数据格式异常: {data}")
            else:
                self.log_test("获取产品信息", False, f"HTTP {response.status_code}, 产品ID: {product_id}")
                
        except Exception as e:
            self.log_test("获取产品信息", False, f"请求异常: {e}")
    
    async def test_batch_get_products(self):
        """测试批量获取产品信息"""
        try:
            product_ids = self.get_random_product_ids(3)
            request_data = {
                "product_ids": product_ids,
                "force_refresh": False
            }
            
            response = await self.client.post(
                f"{self.base_url}/products/batch",
                json=request_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    found_count = data.get("total_found", 0)
                    requested_count = data.get("total_requested", 0)
                    self.log_test("批量获取产品", True, f"请求: {requested_count}, 找到: {found_count}")
                else:
                    self.log_test("批量获取产品", False, f"状态异常: {data}")
            else:
                self.log_test("批量获取产品", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("批量获取产品", False, f"请求异常: {e}")
    
    async def test_compare_products(self):
        """测试产品相似度比较"""
        try:
            product_ids = self.get_random_product_ids(2)
            request_data = {
                "product_id1": product_ids[0],
                "product_id2": product_ids[1],
                "mode": "text",
                "convert": False
            }
            
            response = await self.client.post(
                f"{self.base_url}/compare",
                json=request_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    result = data.get("data", {})
                    score = result.get("similar_scores", 0)
                    reason = result.get("reson", "")
                    self.log_test(
                        "产品相似度比较", True, 
                        f"产品: {product_ids[0]} vs {product_ids[1]}, 分数: {score}"
                    )
                else:
                    self.log_test("产品相似度比较", False, f"状态异常: {data}")
            else:
                self.log_test("产品相似度比较", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("产品相似度比较", False, f"请求异常: {e}")
    
    async def test_batch_compare_products(self):
        """测试批量产品相似度比较"""
        try:
            # 生成3对产品进行比较
            all_ids = self.get_random_product_ids(6)
            product_pairs = [
                [all_ids[0], all_ids[1]],
                [all_ids[2], all_ids[3]],
                [all_ids[4], all_ids[5]]
            ]
            
            request_data = {
                "product_pairs": product_pairs,
                "mode": "text",
                "convert": False,
                "max_concurrent": 3
            }
            
            response = await self.client.post(
                f"{self.base_url}/compare/batch",
                json=request_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    success_count = data.get("success_count", 0)
                    total_pairs = data.get("total_pairs", 0)
                    self.log_test(
                        "批量产品比较", True, 
                        f"总对数: {total_pairs}, 成功: {success_count}"
                    )
                else:
                    self.log_test("批量产品比较", False, f"状态异常: {data}")
            else:
                self.log_test("批量产品比较", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("批量产品比较", False, f"请求异常: {e}")
    
    async def test_get_product_similarities(self):
        """测试获取产品相似度列表"""
        try:
            product_id = self.get_random_product_ids(1)[0]
            response = await self.client.get(
                f"{self.base_url}/product/{product_id}/similarities?limit=5"
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    similarities = data.get("data", [])
                    count = len(similarities)
                    self.log_test(
                        "获取产品相似度列表", True, 
                        f"产品ID: {product_id}, 相似产品数: {count}"
                    )
                else:
                    self.log_test("获取产品相似度列表", False, f"状态异常: {data}")
            else:
                self.log_test("获取产品相似度列表", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("获取产品相似度列表", False, f"请求异常: {e}")
    
    async def test_get_top_similarities(self):
        """测试获取高相似度产品对"""
        try:
            response = await self.client.get(f"{self.base_url}/similarities/top?limit=10")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    similarities = data.get("data", [])
                    count = len(similarities)
                    self.log_test(
                        "获取高相似度产品对", True, 
                        f"高相似度产品对数: {count}"
                    )
                else:
                    self.log_test("获取高相似度产品对", False, f"状态异常: {data}")
            else:
                self.log_test("获取高相似度产品对", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("获取高相似度产品对", False, f"请求异常: {e}")
    
    async def test_statistics(self):
        """测试获取统计信息"""
        try:
            response = await self.client.get(f"{self.base_url}/stats")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    stats = data.get("data", {})
                    self.log_test("获取统计信息", True, f"统计数据获取成功")
                else:
                    self.log_test("获取统计信息", False, f"状态异常: {data}")
            else:
                self.log_test("获取统计信息", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("获取统计信息", False, f"请求异常: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试产品相似度微服务")
        print("=" * 60)
        
        # 基础功能测试
        await self.test_health_check()
        await self.test_service_info()
        
        # 产品信息测试
        await self.test_get_product_info()
        await self.test_batch_get_products()
        
        # 相似度比较测试
        await self.test_compare_products()
        await self.test_batch_compare_products()
        
        # 相似度查询测试
        await self.test_get_product_similarities()
        await self.test_get_top_similarities()
        
        # 统计信息测试
        await self.test_statistics()
        
        # 输出测试结果摘要
        self.print_test_summary()
    
    def print_test_summary(self):
        """打印测试结果摘要"""
        print("\n" + "=" * 60)
        print("📊 测试结果摘要")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        print("\n" + "=" * 60)
        
        # 返回是否所有测试都通过
        return failed_tests == 0

async def main():
    """主函数"""
    # 检查服务是否可用
    base_url = os.getenv("TEST_BASE_URL", "http://localhost:8000")
    
    print(f"测试目标: {base_url}")
    print(f"可用产品ID数量: {len([id for id in nm_ids if id.strip()])}")
    
    async with ProductSimilarityTester(base_url) as tester:
        all_passed = await tester.run_all_tests()
        
        if all_passed:
            print("🎉 所有测试通过！")
            sys.exit(0)
        else:
            print("💥 部分测试失败！")
            sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
