# 动态生成的服务路由配置
# 由 consul-template 根据 Consul 服务发现自动生成

{{range services}}
{{$service := .Name}}
{{$tag := .Tags | join ","}}

# 服务: {{$service}}
# 标签: {{$tag}}
{{if .Tags | contains "api"}}

# 上游服务器配置 - {{$service}}
upstream {{$service}}_backend {
    {{range service $service}}
    server {{.Address}}:{{.Port}} max_fails=3 fail_timeout=30s;
    {{else}}
    # 没有可用的服务实例
    server 127.0.0.1:65535 down;
    {{end}}
    
    # 连接保持
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# API路由配置 - {{$service}}
location /api/{{$service}}/ {
    # 限流配置
    limit_req zone=api burst=20 nodelay;
    
    # 移除路径前缀
    rewrite ^/api/{{$service}}/(.*)$ /$1 break;
    
    # 代理配置
    proxy_pass http://{{$service}}_backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Service-Name {{$service}};
    
    # HTTP版本和连接配置
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    
    # 超时配置
    proxy_connect_timeout 10s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # 缓冲配置
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;
    
    # 错误处理
    proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    proxy_next_upstream_tries 2;
    proxy_next_upstream_timeout 10s;
    
    # 健康检查失败时的处理
    error_page 502 503 504 = @{{$service}}_error;
}

# 错误处理 - {{$service}}
location @{{$service}}_error {
    return 503 '{"error":"Service Unavailable","service":"{{$service}}","message":"服务暂时不可用，请稍后重试"}';
    add_header Content-Type application/json;
}

{{end}}
{{end}}

# 特殊路由配置

# 产品相似度服务的特殊路由（如果存在）
{{with service "product-similarity"}}
# 产品信息快捷访问
location /api/products/ {
    limit_req zone=api burst=20 nodelay;
    rewrite ^/api/products/(.*)$ /product/$1 break;
    proxy_pass http://product-similarity_backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# 产品比较快捷访问
location /api/compare/ {
    limit_req zone=api burst=10 nodelay;
    proxy_pass http://product-similarity_backend/compare/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
{{end}}

# 服务发现端点
location /api/services {
    access_log off;
    return 200 '{
        "services": [
            {{range services}}
            {{if .Tags | contains "api"}}
            {
                "name": "{{.Name}}",
                "tags": [{{range $i, $tag := .Tags}}{{if $i}},{{end}}"{{$tag}}"{{end}}],
                "instances": [
                    {{range service .Name}}
                    {
                        "address": "{{.Address}}",
                        "port": {{.Port}},
                        "status": "{{.Status}}"
                    }{{if not (last (service $.Name))}},{{end}}
                    {{end}}
                ]
            }{{if not (last (services))}},{{end}}
            {{end}}
            {{end}}
        ]
    }';
    add_header Content-Type application/json;
}

# API文档聚合（如果服务支持）
{{range services}}
{{if .Tags | contains "api"}}
location /docs/{{.Name}} {
    rewrite ^/docs/{{.Name}}/(.*)$ /docs/$1 break;
    proxy_pass http://{{.Name}}_backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
{{end}}
{{end}}

# 默认API根路径
location /api/ {
    return 200 '{
        "message": "微服务API网关",
        "version": "1.0.0",
        "services": "/api/services",
        "health": "/gateway/status",
        "consul": "/consul/"
    }';
    add_header Content-Type application/json;
}
