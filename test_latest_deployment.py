#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最新更新后的部署
验证文件更新和模型配置更改后的服务状态
更新时间: 2025-07-31
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# 测试配置
SERVICE_URL = "http://localhost:8001"
GATEWAY_URL = "http://localhost"

async def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{SERVICE_URL}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 健康检查通过:")
                    print(f"   • 服务状态: {data.get('status', 'N/A')}")
                    print(f"   • 服务名称: {data.get('service', 'N/A')}")
                    print(f"   • 版本: {data.get('version', 'N/A')}")
                    
                    checks = data.get('checks', {})
                    print(f"   • 数据库: {checks.get('database', 'N/A')}")
                    print(f"   • Consul: {checks.get('consul', 'N/A')}")
                    print(f"   • 缓存: {checks.get('cache', 'N/A')}")
                    print(f"   • AI服务: {checks.get('ai_service', 'N/A')}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

async def test_latest_functionality():
    """测试最新更新后的功能"""
    print("\n🔍 测试最新更新后的keywordMatching功能...")
    
    # 使用时间戳确保不会命中缓存
    timestamp = int(time.time())
    test_data = {
        "keyword": f"最新更新测试 светодиодная люстра {timestamp}",
        "target_product_id": 253486273
    }
    
    try:
        start_time = time.time()
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{SERVICE_URL}/keyword-matching",
                json=test_data,
                timeout=aiohttp.ClientTimeout(total=300)
            ) as response:
                end_time = time.time()
                processing_time = end_time - start_time
                
                if response.status == 200:
                    data = await response.json()
                    result = data.get('data', {})
                    print(f"✅ 最新功能测试成功:")
                    print(f"   • 关键词: {result.get('keyword', 'N/A')}")
                    print(f"   • 目标产品ID: {result.get('target_product_id', 'N/A')}")
                    print(f"   • 相似度: {result.get('avg_similarity', 'N/A')}")
                    print(f"   • 相似产品数: {result.get('similar_count', 'N/A')}")
                    print(f"   • 竞争产品数: {result.get('competitor_count', 'N/A')}")
                    print(f"   • 有效评分数: {result.get('valid_scores', 'N/A')}")
                    print(f"   • 处理时间: {processing_time:.2f}秒")
                    print(f"   • 缓存状态: {result.get('from_cache', 'N/A')}")
                    print(f"   • 创建时间: {result.get('created_at', 'N/A')}")
                    
                    # 判断更新是否生效
                    if not result.get('from_cache', True):
                        print(f"   🆕 使用了最新更新的代码进行分析")
                        if processing_time < 30:
                            print(f"   ⚡ 处理速度很快，可能使用了优化后的AI模型")
                        elif processing_time < 60:
                            print(f"   🚀 处理速度正常")
                        else:
                            print(f"   ⏱️ 处理时间较长，进行了深度分析")
                    else:
                        print(f"   💾 使用了缓存结果")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 最新功能测试失败: {response.status}")
                    print(f"   错误信息: {error_text[:200]}")
                    return False
    except Exception as e:
        print(f"❌ 最新功能测试异常: {e}")
        return False

async def test_performance():
    """测试性能表现"""
    print("\n🔍 测试性能表现...")
    
    test_cases = [
        {"keyword": f"быстрый тест {int(time.time())}", "product_id": 253486273},
        {"keyword": f"тест производительности {int(time.time())}", "product_id": 316527894}
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            start_time = time.time()
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{SERVICE_URL}/keyword-matching",
                    json={
                        "keyword": test_case["keyword"],
                        "target_product_id": test_case["product_id"]
                    },
                    timeout=aiohttp.ClientTimeout(total=180)
                ) as response:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        result = data.get('data', {})
                        results.append({
                            'success': True,
                            'time': processing_time,
                            'similarity': result.get('avg_similarity', 0),
                            'from_cache': result.get('from_cache', True)
                        })
                        print(f"   测试 {i}: ✅ 成功 ({processing_time:.2f}秒)")
                    else:
                        results.append({'success': False, 'time': processing_time})
                        print(f"   测试 {i}: ❌ 失败 ({response.status})")
        except Exception as e:
            results.append({'success': False, 'time': 0, 'error': str(e)})
            print(f"   测试 {i}: ❌ 异常 ({e})")
    
    # 性能统计
    successful_results = [r for r in results if r['success']]
    if successful_results:
        avg_time = sum(r['time'] for r in successful_results) / len(successful_results)
        print(f"✅ 性能测试结果:")
        print(f"   • 成功率: {len(successful_results)}/{len(results)}")
        print(f"   • 平均响应时间: {avg_time:.2f}秒")
        return len(successful_results) == len(results)
    else:
        print(f"❌ 所有性能测试失败")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始最新更新后部署验证测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    results = []
    test_names = [
        "健康检查",
        "最新功能测试",
        "性能测试"
    ]
    
    # 执行所有测试
    results.append(await test_health_check())
    results.append(await test_latest_functionality())
    results.append(await test_performance())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 最新更新后部署验证结果")
    print("=" * 60)
    
    success_count = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n📈 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！最新更新部署成功！")
        print("✅ 文件更新和模型配置更改已生效")
    elif success_count >= len(results) * 0.75:
        print("⚠️ 大部分测试通过，部署基本成功")
        print("建议检查失败的测试项目")
    else:
        print("🚨 多项测试失败，部署可能存在问题")
        print("请检查更新的文件和配置")

if __name__ == "__main__":
    asyncio.run(main())
