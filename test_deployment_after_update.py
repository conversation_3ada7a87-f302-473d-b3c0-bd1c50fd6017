#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署后验证测试脚本
验证更新后的keywordMatching服务是否正常工作
"""

import asyncio
import aiohttp
import json
import time

# 测试配置
SERVICE_URL = "http://localhost:8001"
GATEWAY_URL = "http://localhost"

async def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{SERVICE_URL}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 健康检查通过: {data}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

async def test_keyword_matching_direct():
    """测试直接访问keywordMatching功能"""
    print("\n🔍 测试直接访问keywordMatching...")
    test_data = {
        "keyword": "люстра на потолок",
        "target_product_id": 253486273
    }
    
    try:
        start_time = time.time()
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{SERVICE_URL}/keyword-matching",
                json=test_data,
                timeout=aiohttp.ClientTimeout(total=120)
            ) as response:
                end_time = time.time()
                processing_time = end_time - start_time
                
                if response.status == 200:
                    data = await response.json()
                    result = data.get('data', {})
                    print(f"✅ 直接访问成功:")
                    print(f"   • 关键词: {result.get('keyword', 'N/A')}")
                    print(f"   • 相似度: {result.get('avg_similarity', 'N/A')}")
                    print(f"   • 相似产品数: {result.get('similar_count', 'N/A')}")
                    print(f"   • 竞争产品数: {result.get('competitor_count', 'N/A')}")
                    print(f"   • 有效评分数: {result.get('valid_scores', 'N/A')}")
                    print(f"   • 处理时间: {processing_time:.2f}秒")
                    print(f"   • 缓存状态: {result.get('from_cache', 'N/A')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 直接访问失败: {response.status}")
                    print(f"   错误信息: {error_text[:200]}")
                    return False
    except Exception as e:
        print(f"❌ 直接访问异常: {e}")
        return False

async def test_keyword_matching_gateway():
    """测试网关访问keywordMatching功能"""
    print("\n🔍 测试网关访问keywordMatching...")
    test_data = {
        "keyword": "светильник потолочный",
        "target_product_id": 253486273
    }
    
    try:
        start_time = time.time()
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{GATEWAY_URL}/api/product-similarity/keyword-matching",
                json=test_data,
                timeout=aiohttp.ClientTimeout(total=120)
            ) as response:
                end_time = time.time()
                processing_time = end_time - start_time
                
                if response.status == 200:
                    data = await response.json()
                    result = data.get('data', {})
                    print(f"✅ 网关访问成功:")
                    print(f"   • 关键词: {result.get('keyword', 'N/A')}")
                    print(f"   • 相似度: {result.get('avg_similarity', 'N/A')}")
                    print(f"   • 相似产品数: {result.get('similar_count', 'N/A')}")
                    print(f"   • 竞争产品数: {result.get('competitor_count', 'N/A')}")
                    print(f"   • 有效评分数: {result.get('valid_scores', 'N/A')}")
                    print(f"   • 处理时间: {processing_time:.2f}秒")
                    print(f"   • 缓存状态: {result.get('from_cache', 'N/A')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 网关访问失败: {response.status}")
                    print(f"   错误信息: {error_text[:200]}")
                    return False
    except Exception as e:
        print(f"❌ 网关访问异常: {e}")
        return False

async def test_consul_registration():
    """测试Consul服务注册"""
    print("\n🔍 测试Consul服务注册...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8500/v1/health/service/product-similarity") as response:
                if response.status == 200:
                    data = await response.json()
                    if data:
                        service_info = data[0]
                        service = service_info.get('Service', {})
                        checks = service_info.get('Checks', [])
                        
                        print(f"✅ Consul注册成功:")
                        print(f"   • 服务名: {service.get('Service', 'N/A')}")
                        print(f"   • 服务地址: {service.get('Address', 'N/A')}:{service.get('Port', 'N/A')}")
                        print(f"   • 健康检查: {len([c for c in checks if c.get('Status') == 'passing'])} 通过")
                        return True
                    else:
                        print("❌ Consul中未找到服务注册")
                        return False
                else:
                    print(f"❌ Consul查询失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Consul查询异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始部署后验证测试")
    print("=" * 60)
    
    results = []
    
    # 测试健康检查
    results.append(await test_health_check())
    
    # 测试直接访问
    results.append(await test_keyword_matching_direct())
    
    # 测试网关访问
    results.append(await test_keyword_matching_gateway())
    
    # 测试Consul注册
    results.append(await test_consul_registration())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    test_names = [
        "健康检查",
        "直接访问keywordMatching",
        "网关访问keywordMatching", 
        "Consul服务注册"
    ]
    
    success_count = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n📈 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！服务部署成功！")
    elif success_count >= len(results) * 0.75:
        print("⚠️ 大部分测试通过，服务基本正常，但有部分问题需要关注")
    else:
        print("🚨 多项测试失败，服务可能存在问题，需要检查")

if __name__ == "__main__":
    asyncio.run(main())
