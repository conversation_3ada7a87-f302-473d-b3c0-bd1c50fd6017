#!/usr/bin/env python3
"""
测试新关键词，验证图片描述功能
"""

import asyncio
import aiohttp
import json

async def test_new_keyword():
    """测试新关键词，不会命中缓存"""
    
    # 使用一个不太常见的关键词，避免缓存
    test_keyword = "светодиодная лампа настольная"  # LED台灯
    target_product_id = 253486273
    
    print(f"🔍 测试关键词: {test_keyword}")
    print(f"🎯 目标产品ID: {target_product_id}")
    print("=" * 60)
    
    # API 端点
    api_url = "http://localhost:8001/keyword-matching"
    
    # 请求数据
    request_data = {
        "keyword": test_keyword,
        "target_product_id": target_product_id
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("📡 发送请求...")
            async with session.post(api_url, json=request_data) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    print("✅ 请求成功!")
                    print(f"状态: {result.get('status')}")
                    
                    data = result.get('data', {})
                    print(f"关键词: {data.get('keyword')}")
                    print(f"平均相似度: {data.get('avg_similarity')}")
                    print(f"相似产品数: {data.get('similar_count')}")
                    print(f"竞争产品数: {data.get('competitor_count')}")
                    print(f"有效评分数: {data.get('valid_scores')}")
                    print(f"缓存状态: {'命中缓存' if data.get('from_cache') else '新请求'}")
                    
                    # 检查详细结果
                    detailed_results = data.get('detailed_results', [])
                    print(f"\n📊 详细结果数量: {len(detailed_results)}")
                    
                    if detailed_results:
                        print("\n🔍 前3个产品详情:")
                        for i, product in enumerate(detailed_results[:3]):
                            print(f"\n--- 产品 {i+1} ---")
                            print(f"产品ID: {product.get('product_id')}")
                            print(f"相似度: {product.get('similarity_score')}")
                            print(f"是否相似: {product.get('is_similar')}")
                            print(f"是否竞争: {product.get('is_competitor')}")
                            
                            # 检查产品信息
                            product_info = product.get('product_info', {})
                            if product_info:
                                print(f"产品名称: {product_info.get('name', 'N/A')}")
                                print(f"品牌: {product_info.get('brand', 'N/A')}")
                                print(f"价格: {product_info.get('price', 'N/A')}")
                                
                                # 🔥 重点检查图片描述功能
                                image_descriptions = product_info.get('image_descriptions', [])
                                images_description_text = product_info.get('images_description_text', '')
                                
                                if image_descriptions:
                                    print(f"✅ 图片描述数量: {len(image_descriptions)}")
                                    print(f"✅ 图片描述文本长度: {len(images_description_text)} 字符")
                                    if images_description_text:
                                        print(f"✅ 图片描述预览: {images_description_text[:100]}...")
                                        return True  # 成功找到图片描述
                                else:
                                    print("❌ 未找到图片描述")
                    else:
                        print("❌ 没有详细结果")
                    
                    return len(detailed_results) > 0
                    
                else:
                    print(f"❌ 请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"错误信息: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🚀 测试新关键词，验证图片描述功能")
    print("=" * 60)
    
    success = await test_new_keyword()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试成功！图片描述功能正常工作")
    else:
        print("❌ 测试失败！图片描述功能可能有问题")

if __name__ == "__main__":
    asyncio.run(main())
