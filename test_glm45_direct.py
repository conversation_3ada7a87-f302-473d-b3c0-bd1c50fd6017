#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试 glm-4.5 模型
不通过网关或网络请求，直接调用AI模型
"""

import os
import sys
import json
import time
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.config import settings

def test_glm45_direct():
    """直接测试glm-4.5模型"""
    print("🤖 直接测试 glm-4.5 模型")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # 获取AI端点配置
    endpoints = settings.get_openai_endpoints()
    print(f"📡 可用端点数量: {len(endpoints)}")

    # 找到glm-4.5端点
    glm45_endpoint = None
    for endpoint in endpoints:
        if endpoint.get('model') == 'glm-4.5':
            glm45_endpoint = endpoint
            break

    if not glm45_endpoint:
        print("❌ 未找到 glm-4.5 端点配置")
        return

    print(f"🎯 找到 glm-4.5 端点:")
    print(f"   URL: {glm45_endpoint['url']}")
    print(f"   模型: {glm45_endpoint['model']}")
    print(f"   API Key: {glm45_endpoint['api_key'][:20]}...")
    print()

    # 测试用的简单prompt
    test_prompt = """请用中文回答：你是什么AI模型？请简单介绍一下自己。"""

    print("🧪 开始直接调用测试...")
    print(f"📝 测试提示: {test_prompt}")
    print()

    try:
        start_time = time.time()

        # 构建请求
        url = f"{glm45_endpoint['url']}/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {glm45_endpoint['api_key']}"
        }

        payload = {
            "model": glm45_endpoint['model'],
            "messages": [{"role": "user", "content": test_prompt}],
            "temperature": 0.1,
            "max_tokens": 1000
        }

        print(f"🌐 请求URL: {url}")
        print(f"📦 请求模型: {glm45_endpoint['model']}")

        # 发送请求
        response = requests.post(url, headers=headers, json=payload, timeout=60)

        end_time = time.time()
        duration = end_time - start_time

        print(f"📊 HTTP状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            ai_response = result['choices'][0]['message']['content']

            print("✅ 测试成功!")
            print(f"⏱️  响应时间: {duration:.2f}秒")
            print(f"📄 AI回复:")
            print("-" * 40)
            print(ai_response)
            print("-" * 40)

            # 验证回复是否包含glm相关信息
            if 'glm' in ai_response.lower() or 'chatglm' in ai_response.lower() or '智谱' in ai_response:
                print("🎯 模型识别正确: 确认是GLM模型")
            else:
                print("⚠️  模型识别不确定: 回复中未明确提及GLM")

        else:
            print(f"❌ 测试失败: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误内容: {response.text}")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print(f"错误类型: {type(e).__name__}")

def main():
    """主函数"""
    print("🚀 开始直接测试 glm-4.5 模型")
    print()

    # 测试基本模型识别
    test_glm45_direct()

    print("\n" + "=" * 60)
    print("🎉 测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
