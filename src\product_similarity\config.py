import json
import os
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def _json_env(key: str, default: str = "[]") -> List[Dict[str, Any]]:
    """解析JSON格式的环境变量"""
    raw = os.getenv(key, default)
    if not raw or raw.strip() == "":
        return []
    try:
        return json.loads(raw)
    except json.JSONDecodeError as e:
        raise RuntimeError(f"环境变量 {key} 不是合法 JSON: {e}")

def _get_redis_config() -> Dict[str, Any]:
    """获取Redis配置，支持两种配置方式"""
    redis_url = os.getenv("REDIS_URL")
    if redis_url:
        # 方式2: 使用完整的 Redis URL
        return {"url": redis_url}
    else:
        # 方式1: 分别配置各项参数
        return {
            "host": os.getenv("REDIS_HOST", "localhost"),
            "port": int(os.getenv("REDIS_PORT", "6379")),
            "password": os.getenv("REDIS_PASSWORD"),
            "db": int(os.getenv("REDIS_DB", "0"))
        }

class Settings:
    """应用配置类"""
    
    # Consul 配置
    CONSUL_HOST: str = os.getenv("CONSUL_HOST", "consul")
    CONSUL_PORT: int = int(os.getenv("CONSUL_PORT", "8500"))
    
    # 服务配置
    SERVICE_NAME: str = os.getenv("SERVICE_NAME", "product-similarity")
    SERVICE_PORT: int = int(os.getenv("SERVICE_PORT", "8000"))
    SERVICE_TAGS: str = os.getenv("SERVICE_TAGS", "api,product,similarity")
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # PostgreSQL 配置
    PG_USER: str = os.getenv("PG_USER", "postgres")
    PG_PASSWORD: str = os.getenv("PG_PASSWORD", "postgres")
    PG_DB: str = os.getenv("PG_DB", "postgres")
    PG_HOST: str = os.getenv("PG_HOST", "localhost")
    PG_PORT: int = int(os.getenv("PG_PORT", "5432"))
    
    @property
    def PG_DSN(self) -> str:
        """构建PostgreSQL连接字符串"""
        return f"postgresql://{self.PG_USER}:{self.PG_PASSWORD}@{self.PG_HOST}:{self.PG_PORT}/{self.PG_DB}"
    
    # Redis 配置
    REDIS_CONFIG: Dict[str, Any] = _get_redis_config()
    
    # OpenAI 配置 (支持新旧两种格式)
    OPENAI_CREDENTIALS: List[Dict[str, Any]] = _json_env("OPENAI_CREDENTIALS")
    TEXT_ENDPOINTS: List[Dict[str, Any]] = _json_env("TEXT_ENDPOINTS")
    MM_ENDPOINTS: List[Dict[str, Any]] = _json_env("MM_ENDPOINTS")
    
    # 业务配置
    MAX_CONCURRENT_REQUESTS: int = int(os.getenv("MAX_CONCURRENT_REQUESTS", "10"))
    REQUEST_TIMEOUT: int = int(os.getenv("REQUEST_TIMEOUT", "30"))
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "3600"))
    
    # AI 模型配置
    AI_TIMEOUT: int = int(os.getenv("AI_TIMEOUT", "120"))
    AI_TEMPERATURE: float = float(os.getenv("AI_TEMPERATURE", "0.1"))
    
    def get_service_tags_list(self) -> List[str]:
        """获取服务标签列表"""
        return [tag.strip() for tag in self.SERVICE_TAGS.split(",")]
    
    def get_openai_endpoints(self) -> List[Dict[str, Any]]:
        """获取OpenAI端点配置，优先使用TEXT_ENDPOINTS"""
        if self.TEXT_ENDPOINTS:
            # 优先使用：TEXT_ENDPOINTS（支持多个模型端点）
            return self.TEXT_ENDPOINTS
        elif self.OPENAI_CREDENTIALS:
            # 备用格式：OPENAI_CREDENTIALS
            return self.OPENAI_CREDENTIALS
        else:
            return []
    
    def get_multimodal_endpoints(self) -> List[Dict[str, Any]]:
        """获取多模态端点配置"""
        return self.MM_ENDPOINTS

# 创建全局配置实例
settings = Settings()
