#!/usr/bin/env python3
"""
测试新样本记录 - 使用未测试过的产品ID
"""

import asyncio
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail
from product_similarity.crud import get_basket_stats, get_basket_by_short_id
from product_similarity.logging import log_info, log_error, log_success, log_warning
from test_product_ids import nm_ids

# 转换为整数
product_ids = [int(id_str) for id_str in nm_ids]

async def test_new_samples():
    """测试新样本记录"""
    log_info("=== 测试新样本记录 ===")
    
    # 获取初始统计
    initial_stats = await get_basket_stats()
    initial_samples = initial_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"初始样本数: {initial_samples}")
    
    # 选择一些后面的产品ID（可能还没有被测试过）
    test_products = product_ids[300:310]  # 选择第300-310个产品
    
    log_info(f"测试产品ID: {test_products}")
    
    success_count = 0
    new_samples_count = 0
    
    for i, product_id in enumerate(test_products):
        short_id = product_id // 100000
        log_info(f"\n--- 测试产品 {i+1}/10: {product_id} (short_id: {short_id}) ---")
        
        # 查看处理前的数据库状态
        before_info = await get_basket_by_short_id(short_id)
        before_stats = await get_basket_stats()
        before_samples = before_stats.get('sample_stats', {}).get('total_samples', 0)
        
        log_info(f"处理前: 数据库状态={before_info}")
        log_info(f"处理前样本数: {before_samples}")
        
        try:
            # 获取产品信息
            product_info = await get_product_detail(product_id)
            if product_info:
                success_count += 1
                log_success(f"成功获取产品: {product_id}")
                
                # 查看处理后的数据库状态
                after_info = await get_basket_by_short_id(short_id)
                after_stats = await get_basket_stats()
                after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
                
                log_info(f"处理后: 数据库状态={after_info}")
                log_info(f"处理后样本数: {after_samples}")
                
                # 检查样本是否增加
                if after_samples > before_samples:
                    new_samples_count += 1
                    log_success(f"✅ 新样本已记录: {before_samples} → {after_samples}")
                else:
                    log_info(f"ℹ️ 样本数未变化: {before_samples} → {after_samples} (可能是重复样本)")
                    
        except Exception as e:
            log_error(f"获取产品失败: {product_id}", error=e)
    
    # 最终统计
    final_stats = await get_basket_stats()
    final_samples = final_stats.get('sample_stats', {}).get('total_samples', 0)
    
    log_info(f"\n=== 测试结果 ===")
    log_info(f"成功获取产品数: {success_count}/10")
    log_info(f"新样本记录数: {new_samples_count}")
    log_info(f"样本数变化: {initial_samples} → {final_samples} (+{final_samples - initial_samples})")
    log_info(f"最终统计: {final_stats}")

async def test_specific_new_products():
    """测试特定的新产品"""
    log_info("\n=== 测试特定新产品 ===")
    
    # 选择一些特定的产品ID，这些可能还没有被测试过
    specific_products = [
        int(nm_ids[350]),  # 倒数第几个
        int(nm_ids[340]),
        int(nm_ids[330]),
    ]
    
    log_info(f"测试特定产品: {specific_products}")
    
    for product_id in specific_products:
        short_id = product_id // 100000
        log_info(f"\n--- 测试产品: {product_id} (short_id: {short_id}) ---")
        
        # 查看数据库状态
        before_info = await get_basket_by_short_id(short_id)
        before_stats = await get_basket_stats()
        before_samples = before_stats.get('sample_stats', {}).get('total_samples', 0)
        
        log_info(f"处理前状态: {before_info}")
        log_info(f"处理前样本数: {before_samples}")
        
        try:
            product_info = await get_product_detail(product_id)
            if product_info:
                after_info = await get_basket_by_short_id(short_id)
                after_stats = await get_basket_stats()
                after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
                
                log_info(f"处理后状态: {after_info}")
                log_info(f"处理后样本数: {after_samples}")
                
                if after_samples > before_samples:
                    log_success(f"✅ 新样本记录成功!")
                else:
                    log_info(f"ℹ️ 样本已存在或其他原因")
                    
        except Exception as e:
            log_error(f"获取失败: {product_id}", error=e)

async def main():
    """主测试函数"""
    log_info("开始测试新样本记录")
    
    await test_new_samples()
    await test_specific_new_products()
    
    log_success("新样本记录测试完成")

if __name__ == "__main__":
    asyncio.run(main())
