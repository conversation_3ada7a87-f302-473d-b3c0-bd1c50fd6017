# 产品相似度微服务部署指南

本指南详细说明如何将产品相似度服务接入到现有的微服务集群中。

## 📋 目录

- [服务概述](#服务概述)
- [前置条件](#前置条件)
- [快速部署](#快速部署)
- [详细配置](#详细配置)
- [测试验证](#测试验证)
- [故障排除](#故障排除)
- [运维管理](#运维管理)

## 🏗️ 服务概述

### 产品相似度微服务功能

本服务提供以下核心功能：

1. **产品信息获取接口**：
   - `GET /product/{product_id}` - 获取单个产品信息
   - `POST /products/batch` - 批量获取产品信息

2. **产品分析接口**：
   - `POST /compare` - 比较两个产品的相似度
   - `POST /compare/batch` - 批量比较产品相似度
   - `GET /product/{product_id}/similarities` - 获取产品相似度列表
   - `GET /similarities/top` - 获取最高相似度产品对

3. **服务管理接口**：
   - `GET /health` - 健康检查
   - `GET /info` - 服务信息
   - `GET /stats` - 服务统计

### 架构集成

```
┌─────────────────────────────────────────────────────────────┐
│                        主服务（基础设施）                      │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────────────┐  │
│  │ Consul  │  │  Nginx  │  │  Redis  │  │   PostgreSQL    │  │
│  │ (服务发现)│  │ (网关)  │  │ (缓存)  │  │    (数据库)     │  │
│  └─────────┘  └─────────┘  └─────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ 服务注册 & 网络连接
                              │
┌─────────────────────────────────────────────────────────────┐
│                    产品相似度业务服务                        │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │              product-similarity                         │  │
│  │  • 产品信息获取  • 相似度分析  • 批量处理                │  │
│  └─────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## ✅ 前置条件

### 1. 主服务运行状态

确保基础设施服务正在运行：

```bash
# 检查Consul服务发现
curl http://localhost:8500/v1/status/leader

# 检查网关状态
curl http://localhost/gateway/status

# 检查微服务网络
docker network ls | grep microservices
```

### 2. 环境要求

- Docker 和 Docker Compose
- Python 3.11+（用于测试脚本）
- 网络连通性到主服务

### 3. 配置文件

确保以下文件存在：
- `Dockerfile` - 服务镜像构建文件
- `requirements.txt` - Python依赖
- `src/` - 服务源代码目录

## 🚀 快速部署

### 方法一：使用部署脚本（推荐）

#### Windows PowerShell:
```powershell
# 部署服务
.\deploy-business-service.ps1 deploy

# 查看状态
.\deploy-business-service.ps1 status

# 查看日志
.\deploy-business-service.ps1 logs
```

#### Linux/macOS:
```bash
# 部署服务
./deploy-business-service.sh deploy

# 查看状态
./deploy-business-service.sh status

# 查看日志
./deploy-business-service.sh logs
```

### 方法二：手动部署

```bash
# 1. 配置环境变量
cp .env.business.example .env
# 编辑 .env 文件

# 2. 确保网络存在
docker network create microservices

# 3. 构建并启动服务
docker-compose -f docker-compose.business.yml up --build -d

# 4. 检查服务状态
docker-compose -f docker-compose.business.yml ps
```

## ⚙️ 详细配置

### 环境变量配置

编辑 `.env` 文件，配置以下关键参数：

```bash
# 基础设施连接
CONSUL_HOST=consul                    # 或主服务IP
PG_HOST=postgres                      # 或外部数据库IP
REDIS_HOST=redis                      # 或外部Redis IP

# 服务配置
SERVICE_NAME=product-similarity
SERVICE_PORT=8000
SERVICE_TAGS=api,product,similarity,business

# AI模型配置
TEXT_ENDPOINTS=[{"url":"http://your-ai-endpoint:3000","api_key":"your-api-key","model":"your-model","is_multimodal":false}]
```

### 跨主机部署配置

如果业务服务与主服务不在同一主机：

```bash
# 修改 .env 文件
CONSUL_HOST=*************            # 主服务IP
PG_HOST=*************                # 主服务IP
REDIS_HOST=*************             # 主服务IP

# 创建外部网络
docker network create microservices
```

### 独立数据库配置

如果需要使用独立数据库，在 `docker-compose.business.yml` 中取消注释数据库服务：

```yaml
# 取消注释以下部分
product-similarity-postgres:
  image: postgres:16-alpine
  # ... 其他配置
```

## 🧪 测试验证

### 自动化测试

运行集成测试脚本：

```bash
python test_microservice_integration.py
```

测试内容包括：
- ✅ Consul连接测试
- ✅ 服务注册验证
- ✅ 健康检查测试
- ✅ 直接服务访问
- ✅ 网关路由测试
- ✅ API端点测试
- ✅ 负载均衡测试
- ✅ 业务功能测试

### 手动验证

```bash
# 1. 检查服务注册
curl http://localhost:8500/v1/catalog/service/product-similarity

# 2. 健康检查
curl http://localhost:8000/health

# 3. 通过网关访问
curl http://localhost/api/product-similarity/health

# 4. 测试业务功能
curl http://localhost:8000/info
curl -X POST http://localhost:8000/compare \
  -H "Content-Type: application/json" \
  -d '{"product_id1": 233681605, "product_id2": 233681606, "mode": "text"}'
```

## 🔧 故障排除

### 常见问题

#### 1. 服务无法注册到Consul

**症状**: 服务启动但Consul中看不到

**解决方案**:
```bash
# 检查网络连接
docker exec product-similarity-server ping consul

# 检查环境变量
docker exec product-similarity-server env | grep CONSUL

# 查看服务日志
docker-compose -f docker-compose.business.yml logs product-similarity
```

#### 2. 网关无法路由到服务

**症状**: 通过网关访问返回502/404

**解决方案**:
```bash
# 检查consul-template是否更新了Nginx配置
# 等待几分钟让consul-template生成配置

# 或者重启网关服务（在主服务中）
docker-compose -f docker-compose.infrastructure.yml restart nginx-gateway
```

#### 3. 数据库连接失败

**症状**: 服务启动失败，数据库连接错误

**解决方案**:
```bash
# 检查数据库连接
docker exec product-similarity-server pg_isready -h postgres -p 5432

# 检查环境变量
docker exec product-similarity-server env | grep PG_

# 测试数据库连接
docker exec postgres-db psql -U admin -d microservices -c "SELECT 1;"
```

## 🛠️ 运维管理

### 服务管理命令

```bash
# 查看服务状态
docker-compose -f docker-compose.business.yml ps

# 查看服务日志
docker-compose -f docker-compose.business.yml logs -f product-similarity

# 重启服务
docker-compose -f docker-compose.business.yml restart product-similarity

# 停止服务
docker-compose -f docker-compose.business.yml down

# 更新服务
docker-compose -f docker-compose.business.yml up --build -d
```

### 扩展服务实例

```bash
# 扩展到3个实例
docker-compose -f docker-compose.business.yml up -d --scale product-similarity=3

# 验证负载均衡
for i in {1..10}; do
  curl http://localhost/api/product-similarity/info
done
```

### 监控和日志

```bash
# 实时监控服务状态
watch docker-compose -f docker-compose.business.yml ps

# 查看资源使用情况
docker stats product-similarity-server

# 导出日志
docker-compose -f docker-compose.business.yml logs --no-color > service.log
```

## 📊 性能优化

### 资源限制

在 `docker-compose.business.yml` 中添加资源限制：

```yaml
services:
  product-similarity:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

### 缓存优化

调整环境变量中的缓存配置：

```bash
CACHE_TTL=7200                       # 增加缓存时间
REDIS_POOL_MAX_CONNECTIONS=50        # 增加连接池大小
```

## 🔐 安全配置

### 网络安全

```yaml
# 限制容器权限
security_opt:
  - no-new-privileges:true
user: "1000:1000"
read_only: true
```

### API安全

在 `.env` 中配置API认证：

```bash
API_KEY=your-secure-api-key
JWT_SECRET=your-jwt-secret
```

## 📞 支持和帮助

### 快速诊断

```bash
# 运行诊断脚本
python test_microservice_integration.py

# 检查所有服务状态
curl http://localhost:8500/v1/agent/services
```

### 获取帮助

1. 查看服务日志获取详细错误信息
2. 检查网络连通性和防火墙设置
3. 验证环境变量配置
4. 确认主服务正常运行

---

**部署完成后，您的产品相似度服务将完全集成到微服务集群中，支持服务发现、负载均衡和统一网关访问。**
