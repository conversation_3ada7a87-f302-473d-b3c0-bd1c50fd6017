#!/usr/bin/env python3
"""
优化版大规模压力测试
降低并发数，增加稳定性，专注于测试服务的处理能力
"""

import asyncio
import aiohttp
import json
import time
import random
from test_keywords import keyword_info

async def optimized_pressure_test():
    """优化版压力测试"""
    
    print("🎯 优化版大规模压力测试")
    print("=" * 60)
    
    # 筛选照明相关关键词
    lighting_keywords = []
    lighting_terms = ['люстра', 'светильник', 'лампа', 'светодиод', 'освещение', 'плафон', 'бра', 'торшер', 'подсветка']
    
    for item in keyword_info:
        keyword = item['keyword'].lower()
        if any(term in keyword for term in lighting_terms):
            lighting_keywords.append(item['keyword'])
    
    print(f"📊 找到 {len(lighting_keywords)} 个照明相关关键词")
    
    # 测试配置 - 降低并发数提高稳定性
    test_keywords_count = 200  # 测试200个关键词
    max_concurrent = 10        # 降低到10个并发
    target_product_id = 253486273
    
    # 随机选择关键词
    test_keywords = random.sample(lighting_keywords, min(test_keywords_count, len(lighting_keywords)))
    
    print(f"📊 测试参数:")
    print(f"   关键词数量: {test_keywords_count}")
    print(f"   最大并发数: {max_concurrent}")
    print(f"   目标产品ID: {target_product_id}")
    print(f"   关键词类型: 照明相关")
    print("=" * 60)
    
    results = []
    start_time = time.time()
    
    async def test_single_keyword(session, keyword, test_id, semaphore):
        """测试单个关键词"""
        async with semaphore:
            request_data = {
                "keyword": keyword,
                "target_product_id": target_product_id
            }
            
            request_start = time.time()
            
            try:
                async with session.post(
                    "http://localhost:8001/keyword-matching",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时
                ) as response:
                    request_end = time.time()
                    processing_time = request_end - request_start
                    
                    if response.status == 200:
                        result = await response.json()
                        data = result.get('data', {})
                        
                        avg_similarity = data.get('avg_similarity', 0)
                        valid_scores = data.get('valid_scores', 0)
                        from_cache = data.get('from_cache', False)
                        data_valid = avg_similarity > 0 and valid_scores > 0
                        
                        # 实时显示结果
                        status_icon = "✅" if data_valid else "⚠️"
                        cache_icon = "💾" if from_cache else "🆕"
                        
                        print(f"{status_icon} [{test_id:3d}] {keyword[:40]:<40} | 相似度: {avg_similarity:2d} | 有效评分: {valid_scores:2d} | 耗时: {processing_time:6.1f}s | {cache_icon}")
                        
                        return {
                            'test_id': test_id,
                            'keyword': keyword,
                            'success': True,
                            'processing_time': processing_time,
                            'avg_similarity': avg_similarity,
                            'valid_scores': valid_scores,
                            'similar_count': data.get('similar_count', 0),
                            'competitor_count': data.get('competitor_count', 0),
                            'from_cache': from_cache,
                            'data_valid': data_valid
                        }
                    else:
                        error_text = await response.text()
                        print(f"❌ [{test_id:3d}] {keyword[:40]:<40} | 错误: HTTP {response.status}")
                        return {
                            'test_id': test_id,
                            'keyword': keyword,
                            'success': False,
                            'processing_time': processing_time,
                            'error': f"HTTP {response.status}: {error_text[:50]}",
                            'data_valid': False
                        }
                        
            except asyncio.TimeoutError:
                request_end = time.time()
                processing_time = request_end - request_start
                print(f"⏰ [{test_id:3d}] {keyword[:40]:<40} | 超时: {processing_time:.1f}s")
                return {
                    'test_id': test_id,
                    'keyword': keyword,
                    'success': False,
                    'processing_time': processing_time,
                    'error': "Timeout",
                    'data_valid': False
                }
            except Exception as e:
                request_end = time.time()
                processing_time = request_end - request_start
                print(f"❌ [{test_id:3d}] {keyword[:40]:<40} | 异常: {str(e)[:30]}")
                return {
                    'test_id': test_id,
                    'keyword': keyword,
                    'success': False,
                    'processing_time': processing_time,
                    'error': str(e)[:100],
                    'data_valid': False
                }
    
    # 创建连接器和会话
    connector = aiohttp.TCPConnector(
        limit=max_concurrent * 2,
        limit_per_host=max_concurrent,
        ttl_dns_cache=300,
        use_dns_cache=True
    )
    
    async with aiohttp.ClientSession(connector=connector) as session:
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        print("🚀 开始执行测试...")
        print(f"{'状态':<2} {'序号':<5} {'关键词':<40} | {'相似度':<6} | {'有效评分':<8} | {'耗时':<8} | {'类型'}")
        print("-" * 100)
        
        # 分批执行任务，每批20个
        batch_size = 20
        batch_count = 0
        
        for i in range(0, len(test_keywords), batch_size):
            batch_count += 1
            batch_keywords = test_keywords[i:i+batch_size]
            
            print(f"\n📦 批次 {batch_count}: 处理关键词 {i+1}-{min(i+batch_size, len(test_keywords))}")
            
            # 创建批次任务
            batch_tasks = [
                test_single_keyword(session, keyword, i + j + 1, semaphore)
                for j, keyword in enumerate(batch_keywords)
            ]
            
            # 执行批次任务
            batch_start = time.time()
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            batch_end = time.time()
            
            # 处理批次结果
            batch_success = 0
            batch_valid = 0
            batch_cached = 0
            
            for result in batch_results:
                if isinstance(result, Exception):
                    print(f"❌ 批次异常: {result}")
                else:
                    results.append(result)
                    if result['success']:
                        batch_success += 1
                        if result.get('data_valid', False):
                            batch_valid += 1
                        if result.get('from_cache', False):
                            batch_cached += 1
            
            # 批次统计
            batch_time = batch_end - batch_start
            print(f"📊 批次 {batch_count} 完成: 耗时 {batch_time:.1f}s | 成功: {batch_success}/{len(batch_keywords)} | 有效: {batch_valid} | 缓存: {batch_cached}")
            
            # 批次间隔
            if i + batch_size < len(test_keywords):
                print("⏳ 等待3秒后继续下一批次...")
                await asyncio.sleep(3)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 分析结果
    print("\n" + "=" * 60)
    print("📊 优化版大规模压力测试结果分析")
    print("=" * 60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    valid_data_tests = sum(1 for r in results if r.get('data_valid', False))
    cached_results = sum(1 for r in results if r.get('from_cache', False))
    new_requests = total_tests - cached_results
    
    processing_times = [r['processing_time'] for r in results if r['success']]
    avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
    
    new_processing_times = [r['processing_time'] for r in results if r['success'] and not r.get('from_cache', True)]
    avg_new_processing_time = sum(new_processing_times) / len(new_processing_times) if new_processing_times else 0
    
    cached_processing_times = [r['processing_time'] for r in results if r['success'] and r.get('from_cache', False)]
    avg_cached_processing_time = sum(cached_processing_times) / len(cached_processing_times) if cached_processing_times else 0
    
    similarities = [r.get('avg_similarity', 0) for r in results if r.get('data_valid', False)]
    avg_similarity = sum(similarities) / len(similarities) if similarities else 0
    
    valid_scores_list = [r.get('valid_scores', 0) for r in results if r.get('data_valid', False)]
    avg_valid_scores = sum(valid_scores_list) / len(valid_scores_list) if valid_scores_list else 0
    
    print(f"📈 基础统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   成功请求: {successful_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"   有效数据: {valid_data_tests} ({valid_data_tests/total_tests*100:.1f}%)")
    print(f"   新请求数: {new_requests}")
    print(f"   缓存命中: {cached_results} ({cached_results/total_tests*100:.1f}%)")
    
    print(f"\n⏱️  性能统计:")
    print(f"   总耗时: {total_time:.1f} 秒")
    print(f"   整体吞吐量: {total_tests/total_time:.2f} 请求/秒")
    print(f"   平均处理时间: {avg_processing_time:.1f} 秒")
    print(f"   新请求平均时间: {avg_new_processing_time:.1f} 秒")
    print(f"   缓存请求平均时间: {avg_cached_processing_time:.3f} 秒")
    
    print(f"\n📊 数据质量:")
    print(f"   平均相似度: {avg_similarity:.1f}")
    print(f"   平均有效评分数: {avg_valid_scores:.1f}")
    
    # 判断测试结果
    success_rate = successful_tests/total_tests*100 if total_tests > 0 else 0
    validity_rate = valid_data_tests/total_tests*100 if total_tests > 0 else 0
    
    if success_rate >= 95 and validity_rate >= 90:
        print(f"\n🎉 优化版大规模压力测试通过！")
        print(f"✅ 优秀成功率: {success_rate:.1f}%")
        print(f"✅ 优秀数据有效性: {validity_rate:.1f}%")
        print(f"✅ 高并发处理能力强")
        print(f"✅ 服务稳定性优秀")
        
        print(f"\n🚀 可以尝试更高并发或更大规模测试")
        
    elif success_rate >= 85 and validity_rate >= 80:
        print(f"\n✅ 优化版大规模压力测试基本通过")
        print(f"✅ 良好成功率: {success_rate:.1f}%")
        print(f"✅ 良好数据有效性: {validity_rate:.1f}%")
        print(f"⚠️  建议保持当前并发水平")
    else:
        print(f"\n⚠️  优化版大规模压力测试需要优化")
        if success_rate < 85:
            print(f"   - 成功率偏低: {success_rate:.1f}%")
        if validity_rate < 80:
            print(f"   - 数据有效性偏低: {validity_rate:.1f}%")
    
    # 显示失败的测试
    failed_tests = [r for r in results if not r['success']]
    if failed_tests:
        print(f"\n❌ 失败的测试 ({len(failed_tests)}个):")
        for test in failed_tests[:10]:  # 只显示前10个
            print(f"   - {test['keyword']}: {test.get('error', 'Unknown error')}")
    
    # 保存结果
    timestamp = int(time.time())
    result_file = f"optimized_pressure_test_results_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_config': {
                'keywords_count': test_keywords_count,
                'max_concurrent': max_concurrent,
                'target_product_id': target_product_id
            },
            'analysis': {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'valid_data_tests': valid_data_tests,
                'success_rate': success_rate,
                'validity_rate': validity_rate,
                'total_time': total_time,
                'avg_processing_time': avg_processing_time,
                'avg_new_processing_time': avg_new_processing_time,
                'avg_similarity': avg_similarity
            },
            'detailed_results': results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: {result_file}")

async def main():
    """主函数"""
    print("🎯 keywordMatching 优化版大规模压力测试")
    print("📝 降低并发数，提高稳定性，专注测试处理能力")
    
    await optimized_pressure_test()

if __name__ == "__main__":
    asyncio.run(main())
