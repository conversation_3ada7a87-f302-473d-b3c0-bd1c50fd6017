#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试网关路由问题
"""

import requests
import json

def test_gateway_routes():
    """测试网关路由"""
    print("🔍 调试网关路由问题")
    
    # 测试1: 根路径
    print("\n1. 测试根路径 /")
    try:
        response = requests.get("http://localhost/", timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试2: 健康检查路径
    print("\n2. 测试健康检查路径 /api/product-similarity/health")
    try:
        response = requests.get("http://localhost/api/product-similarity/health", timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试3: 服务信息路径
    print("\n3. 测试服务信息路径 /api/product-similarity/info")
    try:
        response = requests.get("http://localhost/api/product-similarity/info", timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试4: keyword-matching路径
    print("\n4. 测试keyword-matching路径 /api/product-similarity/keyword-matching")
    try:
        response = requests.post(
            "http://localhost/api/product-similarity/keyword-matching",
            json={"keyword": "test", "target_product_id": 123},
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试5: 直接访问product-similarity服务
    print("\n5. 直接访问product-similarity服务")
    try:
        response = requests.get("http://localhost:8001/health", timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
    except Exception as e:
        print(f"   错误: {e}")

def test_upstream_connectivity():
    """测试上游服务连接"""
    print("\n🔗 测试上游服务连接")
    
    # 从nginx容器内部测试连接
    import subprocess
    
    try:
        # 测试product-similarity服务连接
        print("\n1. 从nginx容器测试product-similarity连接")
        result = subprocess.run([
            "docker", "exec", "nginx-gateway", 
            "wget", "-q", "-O", "-", "http://product-similarity:8001/health"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"   ✅ 连接成功: {result.stdout}")
        else:
            print(f"   ❌ 连接失败: {result.stderr}")
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")

if __name__ == "__main__":
    test_gateway_routes()
    test_upstream_connectivity()
