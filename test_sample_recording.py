#!/usr/bin/env python3
"""
测试样本记录逻辑 - 验证所有成功请求都被记录
"""

import asyncio
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail
from product_similarity.crud import get_basket_stats, get_basket_by_short_id
from product_similarity.logging import log_info, log_error, log_success, log_warning
from test_product_ids import nm_ids

# 转换为整数
product_ids = [int(id_str) for id_str in nm_ids]

async def test_sample_recording():
    """测试样本记录逻辑"""
    log_info("=== 测试样本记录逻辑 ===")
    
    # 获取初始统计
    initial_stats = await get_basket_stats()
    initial_samples = initial_stats.get('sample_stats', {}).get('total_samples', 0)
    log_info(f"初始样本数: {initial_samples}")
    
    # 选择一些真实的测试产品ID
    test_products = [
        449105617,  # 来自test_product_ids.py
        313067529,  # 来自test_product_ids.py
        376736954,  # 来自test_product_ids.py
        387577046,  # 来自test_product_ids.py
        148846766,  # 来自test_product_ids.py
        243143892,  # 来自test_product_ids.py
        314009537,  # 来自test_product_ids.py
        187549117,  # 来自test_product_ids.py
        150705176,  # 来自test_product_ids.py
        376742484,  # 来自test_product_ids.py
    ]
    
    success_count = 0
    for i, product_id in enumerate(test_products):
        short_id = product_id // 100000
        log_info(f"\n--- 测试产品 {i+1}/10: {product_id} (short_id: {short_id}) ---")
        
        # 查看处理前的数据库状态
        before_info = await get_basket_by_short_id(short_id)
        before_stats = await get_basket_stats()
        before_samples = before_stats.get('sample_stats', {}).get('total_samples', 0)
        
        log_info(f"处理前: 数据库状态={before_info}, 总样本数={before_samples}")
        
        try:
            # 获取产品信息
            product_info = await get_product_detail(product_id)
            if product_info:
                success_count += 1
                log_success(f"成功获取产品: {product_id}")
                
                # 查看处理后的数据库状态
                after_info = await get_basket_by_short_id(short_id)
                after_stats = await get_basket_stats()
                after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
                
                log_info(f"处理后: 数据库状态={after_info}, 总样本数={after_samples}")
                
                # 检查样本是否增加
                if after_samples > before_samples:
                    log_success(f"✅ 样本已记录: {before_samples} → {after_samples}")
                elif before_info and before_info.get('in_range'):
                    log_info(f"ℹ️ 使用数据库范围，样本数未变化（这是正常的）")
                else:
                    log_warning(f"⚠️ 样本未记录: {before_samples} → {after_samples}")
                    
        except Exception as e:
            log_error(f"获取产品失败: {product_id}", error=e)
    
    # 最终统计
    final_stats = await get_basket_stats()
    final_samples = final_stats.get('sample_stats', {}).get('total_samples', 0)
    
    log_info(f"\n=== 测试结果 ===")
    log_info(f"成功获取产品数: {success_count}/10")
    log_info(f"样本数变化: {initial_samples} → {final_samples} (+{final_samples - initial_samples})")
    log_info(f"最终统计: {final_stats}")

async def test_database_vs_traditional():
    """测试数据库命中 vs 传统算法的样本记录"""
    log_info("\n=== 测试数据库命中 vs 传统算法样本记录 ===")
    
    # 选择真实产品进行测试
    test_cases = [
        {"product_id": 260270720, "short_id": 2602, "description": "真实产品ID - 可能在数据库范围内"},
        {"product_id": 340516261, "short_id": 3405, "description": "真实产品ID - 可能需要传统算法"},
    ]
    
    for case in test_cases:
        product_id = case["product_id"]
        short_id = case["short_id"]
        description = case["description"]
        
        log_info(f"\n--- 测试: {product_id} ({description}) ---")
        
        # 查看数据库状态
        db_info = await get_basket_by_short_id(short_id)
        log_info(f"数据库查询结果: {db_info}")
        
        # 获取处理前样本数
        before_stats = await get_basket_stats()
        before_samples = before_stats.get('sample_stats', {}).get('total_samples', 0)
        
        try:
            # 获取产品信息
            product_info = await get_product_detail(product_id)
            if product_info:
                # 获取处理后样本数
                after_stats = await get_basket_stats()
                after_samples = after_stats.get('sample_stats', {}).get('total_samples', 0)
                
                log_info(f"样本变化: {before_samples} → {after_samples}")
                
                if db_info and db_info.get('in_range'):
                    log_info("✅ 使用数据库范围，样本应该被记录")
                else:
                    log_info("✅ 使用传统算法，样本应该被记录")
                    
                if after_samples > before_samples:
                    log_success("✅ 样本已正确记录")
                else:
                    log_warning("⚠️ 样本未记录 - 可能是重复样本")
                    
        except Exception as e:
            log_error(f"获取产品失败: {product_id}", error=e)

async def main():
    """主测试函数"""
    log_info("开始测试样本记录逻辑")
    
    await test_sample_recording()
    await test_database_vs_traditional()
    
    log_success("样本记录测试完成")

if __name__ == "__main__":
    asyncio.run(main())
