#!/usr/bin/env python3
"""
数据库迁移脚本：删除 hot_count 字段
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.product_similarity.db import init_pool, close_pool, get_pool


async def migrate_remove_hot_count():
    """删除 hot_count 字段的迁移脚本"""
    print("🔄 开始数据库迁移：删除 hot_count 字段")
    print("=" * 60)
    
    try:
        # 初始化数据库连接池
        await init_pool()
        print("✅ 数据库连接池初始化完成")
        
        pool = await get_pool()
        async with pool.acquire() as conn:
            
            # 1. 检查字段是否存在
            print("\n1️⃣ 检查 hot_count 字段是否存在...")
            column_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT 1 
                    FROM information_schema.columns 
                    WHERE table_schema = 'pj_similar' 
                    AND table_name = 'product_analyze_similar_result' 
                    AND column_name = 'hot_count'
                )
            """)
            
            if column_exists:
                print("  ✅ 找到 hot_count 字段，准备删除")
                
                # 2. 删除字段
                print("\n2️⃣ 删除 hot_count 字段...")
                await conn.execute("""
                    ALTER TABLE pj_similar.product_analyze_similar_result 
                    DROP COLUMN IF EXISTS hot_count
                """)
                print("  ✅ hot_count 字段删除成功")
                
                # 3. 验证删除结果
                print("\n3️⃣ 验证删除结果...")
                column_exists_after = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 
                        FROM information_schema.columns 
                        WHERE table_schema = 'pj_similar' 
                        AND table_name = 'product_analyze_similar_result' 
                        AND column_name = 'hot_count'
                    )
                """)
                
                if not column_exists_after:
                    print("  ✅ 验证成功，hot_count 字段已被删除")
                else:
                    print("  ❌ 验证失败，hot_count 字段仍然存在")
                
            else:
                print("  ⚪ hot_count 字段不存在，无需删除")
            
            # 4. 显示当前表结构
            print("\n4️⃣ 当前表结构:")
            columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'pj_similar' 
                AND table_name = 'product_analyze_similar_result'
                ORDER BY ordinal_position
            """)
            
            for col in columns:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                print(f"  - {col['column_name']}: {col['data_type']} {nullable}{default}")
        
        print(f"\n" + "=" * 60)
        print("✅ 数据库迁移完成！")
        
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("🔒 数据库连接池已关闭")


if __name__ == "__main__":
    asyncio.run(migrate_remove_hot_count())
