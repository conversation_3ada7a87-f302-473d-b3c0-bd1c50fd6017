import json
import hashlib
import time
from typing import Dict, Any, Optional, List, Union
import markdownify
import asyncio
from functools import wraps
from decimal import Decimal
from datetime import datetime, date

def change_md(data: Dict[str, Any]) -> str:
    """
    将字典数据转换为markdown格式字符串
    
    Args:
        data: 产品信息的字典形式
        
    Returns:
        转换后的markdown字符串
    """
    if not data:
        return ""
    
    try:
        # 方式1: 使用markdownify转换
        compressed = json.dumps(data, ensure_ascii=False, separators=(",", ":"))
        markdown_text = markdownify.markdownify(f"<pre>{compressed}</pre>")
        return markdown_text
        
    except Exception:
        # 方式2: 简单的markdown格式化
        return format_dict_to_markdown(data)

def format_dict_to_markdown(data: Dict[str, Any], level: int = 1) -> str:
    """
    将字典格式化为markdown格式
    
    Args:
        data: 要格式化的字典
        level: 标题级别
        
    Returns:
        markdown格式的字符串
    """
    if not isinstance(data, dict):
        return str(data)
    
    lines = []
    
    for key, value in data.items():
        if isinstance(value, dict):
            # 嵌套字典作为子标题
            lines.append(f"{'#' * min(level, 6)} {key}")
            lines.append(format_dict_to_markdown(value, level + 1))
        elif isinstance(value, list):
            # 列表格式
            lines.append(f"{'#' * min(level, 6)} {key}")
            for i, item in enumerate(value):
                if isinstance(item, dict):
                    lines.append(f"{i + 1}. {format_dict_to_markdown(item, level + 1)}")
                else:
                    lines.append(f"{i + 1}. {item}")
        else:
            # 简单键值对
            lines.append(f"**{key}**: {value}")
    
    return "\n".join(lines)

def generate_cache_key(*args, **kwargs) -> str:
    """
    生成缓存键
    
    Args:
        *args: 位置参数
        **kwargs: 关键字参数
        
    Returns:
        生成的缓存键
    """
    # 将所有参数转换为字符串并排序
    key_parts = []
    
    # 添加位置参数
    for arg in args:
        key_parts.append(str(arg))
    
    # 添加关键字参数（按键排序确保一致性）
    for key in sorted(kwargs.keys()):
        key_parts.append(f"{key}:{kwargs[key]}")
    
    # 生成MD5哈希
    key_string = "|".join(key_parts)
    return hashlib.md5(key_string.encode()).hexdigest()

def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """
    安全的JSON解析
    
    Args:
        json_str: JSON字符串
        default: 解析失败时的默认值
        
    Returns:
        解析结果或默认值
    """
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default

def decimal_serializer(obj):
    """JSON序列化时处理特殊类型"""
    if isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, (datetime, date)):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """
    安全的JSON序列化

    Args:
        obj: 要序列化的对象
        default: 序列化失败时的默认值

    Returns:
        JSON字符串或默认值
    """
    try:
        return json.dumps(obj, ensure_ascii=False, separators=(",", ":"), default=decimal_serializer)
    except (TypeError, ValueError):
        return default

def normalize_product_id(product_id: Union[str, int]) -> int:
    """
    标准化产品ID
    
    Args:
        product_id: 产品ID（字符串或整数）
        
    Returns:
        标准化后的整数产品ID
        
    Raises:
        ValueError: 当产品ID无效时
    """
    try:
        return int(product_id)
    except (ValueError, TypeError):
        raise ValueError(f"无效的产品ID: {product_id}")

def validate_similarity_score(score: Union[int, float]) -> int:
    """
    验证并标准化相似度分数
    
    Args:
        score: 相似度分数
        
    Returns:
        标准化后的分数（1-100之间的整数）
        
    Raises:
        ValueError: 当分数无效时
    """
    try:
        score = int(score)
        if not (1 <= score <= 100):
            raise ValueError(f"相似度分数必须在1-100之间: {score}")
        return score
    except (ValueError, TypeError):
        raise ValueError(f"无效的相似度分数: {score}")

def retry_async(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    异步重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避倍数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        raise last_exception
            
            raise last_exception
        return wrapper
    return decorator

def timing_decorator(func):
    """
    计时装饰器，记录函数执行时间
    """
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            print(f"[TIMING] {func.__name__} 执行时间: {execution_time:.2f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"[TIMING] {func.__name__} 执行时间: {execution_time:.2f}秒 (异常: {e})")
            raise
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            print(f"[TIMING] {func.__name__} 执行时间: {execution_time:.2f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"[TIMING] {func.__name__} 执行时间: {execution_time:.2f}秒 (异常: {e})")
            raise
    
    # 判断是否为异步函数
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper

def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    将列表分块
    
    Args:
        lst: 要分块的列表
        chunk_size: 每块的大小
        
    Returns:
        分块后的列表
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

def get_current_timestamp() -> int:
    """获取当前时间戳（秒）"""
    return int(time.time())

def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        格式化后的大小字符串
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"
