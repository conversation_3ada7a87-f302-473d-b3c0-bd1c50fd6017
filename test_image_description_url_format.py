#!/usr/bin/env python3
"""
测试图片描述接口URL格式修改
验证是否使用了新的URL格式: http://localhost/api/say-img-description/describe?url=xxxxxx
"""

import asyncio
import json
import time
import httpx
from test_keywords import keyword_info

async def test_keyword_matching_with_image_description():
    """测试keywordMatching功能，验证图片描述接口调用"""
    
    # 使用一个简单的关键词进行测试
    test_keyword = "светильник потолочный тест url"
    target_product_id = 253486273
    
    print(f"🔍 测试关键词: {test_keyword}")
    print(f"🎯 目标产品ID: {target_product_id}")
    print("=" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 调用keywordMatching API
        async with httpx.AsyncClient(timeout=300) as client:
            response = await client.post(
                "http://localhost:8001/keyword-matching",
                json={
                    "keyword": test_keyword,
                    "target_product_id": target_product_id
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 计算处理时间
                processing_time = time.time() - start_time
                
                print("✅ API调用成功!")
                print(f"⏱️  处理时间: {processing_time:.2f}秒")
                print()
                
                # 显示结果
                if result.get("status") == "success":
                    data = result.get("data", {})
                    print("📊 处理结果:")
                    print(f"   关键词: {data.get('keyword', 'N/A')}")
                    print(f"   平均相似度: {data.get('avg_similarity', 0)}")
                    print(f"   相似产品数: {data.get('similar_count', 0)}")
                    print(f"   竞争产品数: {data.get('competitor_count', 0)}")
                    print(f"   有效评分数: {data.get('valid_scores', 0)}")
                    print(f"   缓存状态: {'命中缓存' if data.get('from_cache') else '新计算'}")
                    print()
                    
                    # 验证功能
                    if data.get('avg_similarity', 0) > 0:
                        print("✅ 相似度计算正常")
                    else:
                        print("❌ 相似度计算异常")
                        
                    if data.get('valid_scores', 0) > 0:
                        print("✅ 有效评分数正常")
                    else:
                        print("❌ 有效评分数异常")
                        
                    # 如果是新计算，说明图片描述接口被调用了
                    if not data.get('from_cache'):
                        print("✅ 新计算请求，图片描述接口应该被调用")
                        print("📋 请检查Docker日志确认URL格式:")
                        print("   docker logs product-similarity-server --tail 50")
                    else:
                        print("ℹ️  命中缓存，未触发新的图片描述调用")
                        
                else:
                    print(f"❌ API返回错误: {result}")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        
    print("=" * 60)
    print("🔍 验证要点:")
    print("1. 检查Docker日志中是否出现新的URL格式")
    print("2. 图片描述API调用应该使用: http://localhost/api/say-img-description/describe?url=xxxxx")
    print("3. 不再使用params参数，URL直接包含图片地址")

if __name__ == "__main__":
    asyncio.run(test_keyword_matching_with_image_description())
