#!/usr/bin/env python3
"""
使用真实产品ID测试数据库存储修复
"""
import asyncio
import sys
from pathlib import Path
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from test_product_ids import nm_ids

async def test_real_product_storage():
    """使用真实产品ID测试存储功能"""
    print("🛍️ 使用真实产品ID测试数据库存储修复")
    print("=" * 50)
    
    try:
        from src.product_similarity.services.product import get_product_detail
        from src.product_similarity.services.similarity import compare_products_by_ids
        from src.product_similarity.crud import get_product_info
        
        # 选择真实的产品ID
        int_ids = [int(id_str) for id_str in nm_ids if id_str.strip()]
        test_id = random.choice(int_ids)
        
        print(f"🎯 选择真实产品ID: {test_id}")
        
        # 1. 测试产品信息存储和读取
        print("\n1️⃣ 测试产品信息存储...")
        
        # 强制从远程获取产品信息（这会触发存储）
        print("   📥 从远程获取产品信息...")
        product_info = await get_product_detail(test_id, force_refresh=True)
        
        if product_info:
            print(f"   ✅ 产品信息获取成功")
            print(f"   📊 数据大小: {len(str(product_info))} 字符")
            
            # 显示产品基本信息
            if isinstance(product_info, dict):
                name = product_info.get('name', 'N/A')
                print(f"   🏷️ 产品名称: {name[:50]}...")
                
                # 直接从数据库读取
                print("   📤 直接从数据库读取...")
                db_info = await get_product_info(test_id)
                
                if db_info:
                    print("   ✅ 数据库读取成功")
                    
                    # 检查数据类型
                    print(f"   🔍 原始数据类型: {type(product_info)}")
                    print(f"   🔍 数据库数据类型: {type(db_info)}")
                    
                    # 检查关键字段
                    if isinstance(db_info, dict):
                        db_name = db_info.get('name', 'N/A')
                        print(f"   🏷️ 数据库中的产品名称: {db_name[:50]}...")
                        
                        # 比较关键字段
                        if name == db_name:
                            print("   ✅ 产品信息存储和读取正常")
                            storage_test_passed = True
                        else:
                            print("   ❌ 产品信息不一致")
                            storage_test_passed = False
                    else:
                        print(f"   ❌ 数据库返回的数据类型错误: {type(db_info)}")
                        storage_test_passed = False
                else:
                    print("   ❌ 数据库读取失败")
                    storage_test_passed = False
            else:
                print(f"   ❌ 产品信息类型错误: {type(product_info)}")
                storage_test_passed = False
        else:
            print("   ❌ 产品信息获取失败")
            storage_test_passed = False
        
        # 2. 测试相似度结果存储
        print("\n2️⃣ 测试相似度结果存储...")
        
        # 选择两个真实产品ID进行比较
        product_id1, product_id2 = random.sample(int_ids, 2)
        print(f"   🎯 比较产品: {product_id1} vs {product_id2}")
        
        # 第一次比较
        print("   🔄 第一次比较...")
        result1 = await compare_products_by_ids(product_id1, product_id2, mode="text")
        
        if result1 and 'similar_scores' in result1:
            score1 = result1['similar_scores']
            cached1 = result1.get('cached', False)
            print(f"   ✅ 第一次比较成功，分数: {score1}")
            print(f"   💾 来自缓存: {'是' if cached1 else '否'}")
            
            # 第二次比较（应该从缓存获取）
            print("   🔄 第二次比较（测试缓存）...")
            result2 = await compare_products_by_ids(product_id1, product_id2, mode="text")
            
            if result2 and 'similar_scores' in result2:
                score2 = result2['similar_scores']
                cached2 = result2.get('cached', False)
                print(f"   ✅ 第二次比较成功，分数: {score2}")
                print(f"   💾 来自缓存: {'是' if cached2 else '否'}")
                
                # 验证缓存功能
                if score1 == score2 and cached2:
                    print("   ✅ 相似度结果存储和缓存正常")
                    similarity_test_passed = True
                elif score1 == score2:
                    print("   ⚠️ 分数一致但缓存标记异常（功能正常）")
                    similarity_test_passed = True
                else:
                    print("   ❌ 相似度分数不一致")
                    similarity_test_passed = False
            else:
                print("   ❌ 第二次比较失败")
                similarity_test_passed = False
        else:
            print("   ❌ 第一次比较失败")
            similarity_test_passed = False
        
        # 3. 测试多个真实产品的批量操作
        print("\n3️⃣ 测试批量操作...")
        
        success_count = 0
        test_pairs = []
        
        # 生成3对真实产品进行测试
        for i in range(3):
            pair = random.sample(int_ids, 2)
            test_pairs.append(pair)
        
        for i, (id1, id2) in enumerate(test_pairs, 1):
            try:
                print(f"   🔄 批量测试 {i}/3: {id1} vs {id2}")
                result = await compare_products_by_ids(id1, id2, mode="text")
                
                if result and 'similar_scores' in result:
                    score = result['similar_scores']
                    print(f"   ✅ 比较成功，分数: {score}")
                    success_count += 1
                else:
                    print("   ❌ 比较失败")
            except Exception as e:
                print(f"   ❌ 比较异常: {str(e)[:50]}...")
        
        batch_test_passed = success_count >= 2
        print(f"   📊 批量测试结果: {success_count}/3 成功")
        
        # 总结
        print("\n" + "=" * 50)
        print("📊 真实产品数据测试结果:")
        print(f"   📦 产品信息存储: {'✅ 正常' if storage_test_passed else '❌ 异常'}")
        print(f"   🤖 相似度结果存储: {'✅ 正常' if similarity_test_passed else '❌ 异常'}")
        print(f"   🔄 批量操作: {'✅ 正常' if batch_test_passed else '❌ 异常'}")
        
        all_passed = storage_test_passed and similarity_test_passed and batch_test_passed
        
        if all_passed:
            print("\n🎉 所有数据库存储问题已修复！")
            print("\n✅ 修复确认:")
            print("   • 真实产品信息JSONB存储正常")
            print("   • 产品信息读取格式正确")
            print("   • 相似度结果存储和缓存正常")
            print("   • 批量操作稳定可靠")
            print("   • 数据类型处理正确")
            return True
        else:
            print("\n⚠️ 部分功能需要进一步检查")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await test_real_product_storage()
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        sys.exit(1)
