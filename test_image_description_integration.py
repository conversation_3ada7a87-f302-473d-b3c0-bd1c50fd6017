#!/usr/bin/env python3
"""
测试图片描述接口集成
验证 keywordMatching 是否调用了图片描述接口
"""

import asyncio
import aiohttp
import json
import time

async def test_keyword_matching_with_monitoring():
    """测试 keywordMatching 并监控图片描述接口调用"""
    
    # 使用一个新的关键词，确保不会命中缓存
    test_keyword = f"настольная лампа светодиодная {int(time.time())}"  # 添加时间戳避免缓存
    target_product_id = 253486273
    
    print(f"🔍 测试关键词: {test_keyword}")
    print(f"🎯 目标产品ID: {target_product_id}")
    print("=" * 60)
    
    # 1. 首先检查图片描述服务是否可用
    print("📡 检查图片描述服务状态...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/health") as response:
                if response.status == 200:
                    print("✅ 图片描述服务运行正常")
                else:
                    print(f"❌ 图片描述服务状态异常: {response.status}")
    except Exception as e:
        print(f"❌ 图片描述服务连接失败: {e}")
    
    # 2. 测试 keywordMatching
    api_url = "http://localhost:8001/keyword-matching"
    request_data = {
        "keyword": test_keyword,
        "target_product_id": target_product_id
    }
    
    print("\n📡 发送 keywordMatching 请求...")
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, json=request_data) as response:
                end_time = time.time()
                processing_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    
                    print("✅ keywordMatching 请求成功!")
                    print(f"⏱️  处理时间: {processing_time:.2f} 秒")
                    
                    data = result.get('data', {})
                    print(f"关键词: {data.get('keyword')}")
                    print(f"平均相似度: {data.get('avg_similarity')}")
                    print(f"相似产品数: {data.get('similar_count')}")
                    print(f"竞争产品数: {data.get('competitor_count')}")
                    print(f"有效评分数: {data.get('valid_scores')}")
                    print(f"缓存状态: {'命中缓存' if data.get('from_cache') else '新请求'}")
                    print(f"搜索产品数: {data.get('search_products_count', 'N/A')}")
                    print(f"比较产品数: {data.get('compared_products_count', 'N/A')}")
                    
                    # 3. 检查是否调用了图片描述接口
                    print("\n🔍 检查图片描述接口调用情况...")
                    
                    # 如果处理时间较长且不是缓存，说明可能调用了图片描述接口
                    if not data.get('from_cache') and processing_time > 10:
                        print("✅ 处理时间较长，可能已调用图片描述接口")
                    elif data.get('from_cache'):
                        print("ℹ️  命中缓存，未调用图片描述接口")
                    else:
                        print("⚠️  处理时间较短，可能未调用图片描述接口")
                    
                    return True
                    
                else:
                    print(f"❌ keywordMatching 请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"错误信息: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

async def test_direct_image_description():
    """直接测试图片描述接口"""
    print("\n" + "=" * 60)
    print("🖼️  直接测试图片描述接口")
    print("=" * 60)
    
    # 使用一个测试图片URL
    test_image_url = "https://basket-01.wbbasket.ru/vol3804/part380496/380496894/images/big/1.webp"
    
    api_url = "http://localhost:8000/api/say-img-description/describe"
    params = {"url": test_image_url}
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ 图片描述接口调用成功!")
                    print(f"图片URL: {test_image_url}")
                    print(f"描述内容: {result.get('description', 'N/A')}")
                    return True
                else:
                    print(f"❌ 图片描述接口调用失败: {response.status}")
                    error_text = await response.text()
                    print(f"错误信息: {error_text}")
                    return False
    except Exception as e:
        print(f"❌ 图片描述接口异常: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🚀 测试图片描述接口集成")
    print("🎯 验证 keywordMatching 是否使用图片描述功能")
    print("=" * 60)
    
    # 1. 测试直接图片描述接口
    img_desc_success = await test_direct_image_description()
    
    # 2. 测试 keywordMatching 集成
    keyword_success = await test_keyword_matching_with_monitoring()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"图片描述接口: {'✅ 正常' if img_desc_success else '❌ 异常'}")
    print(f"keywordMatching: {'✅ 正常' if keyword_success else '❌ 异常'}")
    
    if img_desc_success and keyword_success:
        print("✅ 图片描述功能已成功集成到 keywordMatching 中")
    else:
        print("❌ 图片描述功能集成可能存在问题")

if __name__ == "__main__":
    asyncio.run(main())
