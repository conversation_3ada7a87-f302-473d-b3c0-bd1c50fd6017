#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简单关键词匹配（不依赖图片描述）
"""

import requests
import json
import time

def test_simple_keyword_matching():
    """测试简单关键词匹配"""
    print("🔍 测试简单关键词匹配")
    print("=" * 50)
    
    # 使用一个简单的关键词进行测试
    test_data = {
        "keyword": "лампа",  # 简单的俄语关键词"灯"
        "target_product_id": 253486273
    }
    
    # 测试1: 直接访问
    print("1. 直接访问测试")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8001/keyword-matching",
            json=test_data,
            timeout=120
        )
        end_time = time.time()
        
        print(f"   状态码: {response.status_code}")
        print(f"   处理时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 直接访问成功")
            print(f"   📊 相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            print(f"   📊 相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
            print(f"   📊 缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
        else:
            print(f"   ❌ 直接访问失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 直接访问异常: {e}")
    
    # 测试2: 网关访问
    print("\n2. 网关访问测试")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost/api/product-similarity/keyword-matching",
            json=test_data,
            timeout=120
        )
        end_time = time.time()
        
        print(f"   状态码: {response.status_code}")
        print(f"   处理时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 网关访问成功")
            print(f"   📊 相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            print(f"   📊 相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
            print(f"   📊 缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
        else:
            print(f"   ❌ 网关访问失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 网关访问异常: {e}")

if __name__ == "__main__":
    test_simple_keyword_matching()
