"""
最简单的 keywordMatching 测试
"""
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.keyword_matching import keywordMatching
from product_similarity.db import init_pool, close_pool

async def main():
    print("🧪 简单测试 keywordMatching 函数")
    
    try:
        # 初始化数据库
        print("正在连接数据库...")
        await init_pool()
        print("✅ 数据库连接成功")
        
        # 测试单个调用
        print("正在测试 keywordMatching 函数...")
        keyword = "手机"
        target_product_id = 123456789
        
        result = await keywordMatching(keyword, target_product_id)
        
        print("✅ 函数调用成功")
        print(f"结果状态: {result.get('status')}")
        print(f"结果类型: {type(result)}")
        print(f"结果键: {list(result.keys())}")
        
        # 检查目标产品
        target_product = result.get("target_product")
        if target_product:
            product_info = target_product.get("product_info", {})
            desc_text = product_info.get("images_description_text", "")
            print(f"目标产品图片描述: {'有' if desc_text else '无'} ({len(desc_text)} 字符)")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await close_pool()
            print("✅ 数据库连接已关闭")
        except:
            pass

if __name__ == "__main__":
    asyncio.run(main())
