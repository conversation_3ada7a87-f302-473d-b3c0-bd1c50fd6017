#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终网关访问测试
"""

import requests
import json
import time

def test_gateway_access():
    """测试网关访问"""
    print("🌐 测试网关访问")
    print("=" * 50)
    
    # 测试1: 网关健康检查
    print("1. 网关健康检查")
    try:
        response = requests.get("http://localhost/api/product-similarity/health", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if "status" in data and data["status"] == "healthy":
                print("   ✅ 网关健康检查通过")
            else:
                print(f"   ⚠️ 网关返回非预期响应: {response.text}")
        else:
            print(f"   ❌ 网关健康检查失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 网关健康检查异常: {e}")
    
    # 测试2: 网关keywordMatching功能
    print("\n2. 网关keywordMatching功能")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost/api/product-similarity/keyword-matching",
            json={
                "keyword": "люстра потолочная",
                "target_product_id": 253486273
            },
            timeout=60
        )
        end_time = time.time()
        
        print(f"   状态码: {response.status_code}")
        print(f"   处理时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            if "data" in data:
                similarity = data["data"].get("avg_similarity", "N/A")
                similar_count = data["data"].get("similar_count", "N/A")
                from_cache = data["data"].get("from_cache", "N/A")
                print(f"   ✅ 网关keywordMatching正常")
                print(f"   📊 相似度: {similarity}")
                print(f"   📊 相似产品数: {similar_count}")
                print(f"   📊 缓存状态: {from_cache}")
            else:
                print(f"   ⚠️ 网关返回非预期格式: {response.text}")
        else:
            print(f"   ❌ 网关keywordMatching失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 网关keywordMatching异常: {e}")
    
    # 测试3: 直接访问对比
    print("\n3. 直接访问对比测试")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8001/keyword-matching",
            json={
                "keyword": "люстра потолочная",
                "target_product_id": 253486273
            },
            timeout=60
        )
        end_time = time.time()
        
        print(f"   状态码: {response.status_code}")
        print(f"   处理时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            similarity = data["data"].get("avg_similarity", "N/A")
            similar_count = data["data"].get("similar_count", "N/A")
            from_cache = data["data"].get("from_cache", "N/A")
            print(f"   ✅ 直接访问正常")
            print(f"   📊 相似度: {similarity}")
            print(f"   📊 相似产品数: {similar_count}")
            print(f"   📊 缓存状态: {from_cache}")
        else:
            print(f"   ❌ 直接访问失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 直接访问异常: {e}")

if __name__ == "__main__":
    test_gateway_access()
