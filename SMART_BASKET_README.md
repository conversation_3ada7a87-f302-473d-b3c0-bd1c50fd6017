# Basket动态探测与智能缓存系统

## 📋 系统概述

智能Basket系统是一个用于解决Wildberries产品URL中basket计算不准确问题的解决方案。通过动态探测、数据库缓存和置信度机制，显著提高了产品信息获取的准确性和效率。

## 🎯 核心特性

### 1. 智能Basket计算
- **传统算法兜底**: 使用原有的basket计算逻辑作为基础
- **数据库缓存优先**: 优先使用已验证的basket映射数据
- **置信度评估**: 对每个basket选择进行置信度评估

### 2. 动态探测机制
- **404错误处理**: 当遇到404错误时自动触发动态探测
- **范围探测**: 在估算值附近±3范围内进行HEAD请求探测
- **自动修正**: 找到正确basket后自动重新请求

### 3. 自学习系统
- **样本记录**: 记录成功和失败的basket样本
- **置信度更新**: 根据请求结果动态调整置信度
- **范围重计算**: 自动重新计算basket范围映射

## 🗄️ 数据库设计

### basket_samples表（样本数据）
```sql
CREATE TABLE pj_similar.basket_samples (
    id BIGSERIAL PRIMARY KEY,
    basket VARCHAR(2) NOT NULL,
    short_id INTEGER NOT NULL,
    confidence FLOAT DEFAULT 1.0,  -- 置信度：1.0=确定正确，0.8=可能正确，0.1=确定错误
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(basket, short_id)
);
```

### basket_mapping表（范围映射）
```sql
CREATE TABLE pj_similar.basket_mapping (
    id SERIAL PRIMARY KEY,
    basket VARCHAR(2) NOT NULL UNIQUE,
    min_short_id INTEGER NOT NULL,
    max_short_id INTEGER NOT NULL,
    sample_count INTEGER DEFAULT 0,
    confidence_avg FLOAT DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## 📊 置信度机制

| 置信度 | 含义 | 使用场景 |
|--------|------|----------|
| 1.0 | 确定正确 | 动态探测成功、数据库范围内确认 |
| 0.8 | 可能正确 | 数据库范围外但请求成功 |
| 0.1 | 确定错误 | 请求返回404错误 |

## 🚀 使用方法

### 基本使用
```python
from product_similarity.services.product import get_product_detail

# 获取单个产品信息（自动使用智能basket）
product_info = await get_product_detail(449105617)

# 批量获取产品信息
product_ids = [449105617, 313067529, 376736954]
products = await get_products_batch(product_ids)
```

### 高级功能
```python
from product_similarity.crud import get_basket_stats, add_basket_sample

# 获取basket系统统计信息
stats = await get_basket_stats()

# 手动添加basket样本
await add_basket_sample("25", 4491, confidence=1.0)
```

## 🔧 维护工具

### 维护脚本
```bash
# 生成系统报告
python basket_maintenance.py --mode report

# 快速维护（清理+重计算）
python basket_maintenance.py --mode quick

# 完整维护（优化+清理+重计算）
python basket_maintenance.py --mode full
```

### 测试脚本
```bash
# 运行智能basket系统测试
python test_smart_basket.py

# 运行使用示例
python smart_basket_example.py
```

## 📈 性能优化

### 1. 缓存机制
- **数据库缓存**: 产品信息缓存到数据库
- **范围映射**: basket范围映射减少计算开销
- **批量处理**: 支持批量获取产品信息

### 2. 网络优化
- **HEAD请求**: 动态探测使用HEAD请求减少带宽
- **并发控制**: 合理控制并发请求数量
- **超时设置**: 设置合理的请求超时时间

## 🔄 自动维护

### 1. 范围重新计算
- 每次添加样本后自动重新计算范围
- 只使用confidence >= 0.8的样本
- 支持范围扩大和缩小

### 2. 数据清理
- 定期清理30天前confidence < 0.5的样本
- 删除重复的低置信度样本
- 防止错误数据长期污染

### 3. 质量监控
- 监控高置信度样本覆盖率
- 跟踪动态探测频率
- 统计系统整体性能

## ✅ 预期效果

### 准确性提升
- **动态纠错**: 自动发现和纠正basket计算错误
- **学习能力**: 系统使用越多，准确性越高
- **自动优化**: 持续优化basket映射关系

### 性能优化
- **缓存命中**: 大部分请求直接使用缓存
- **减少探测**: 随着样本增加，探测频率降低
- **批量处理**: 支持高效的批量操作

### 可维护性
- **自动维护**: 自动清理和优化数据
- **监控报告**: 详细的系统状态报告
- **灵活配置**: 支持不同的维护策略

## 📝 注意事项

1. **网络开销**: 动态探测会增加网络请求，但只在404时触发
2. **数据质量**: 需要定期运行清理任务维护数据质量
3. **初期探测**: 初期可能需要较多探测，后期会越来越准确
4. **监控建议**: 建议监控探测频率，优化探测范围

## 🔍 故障排除

### 常见问题
1. **探测失败**: 检查网络连接和请求头设置
2. **数据库错误**: 确认数据库连接和表结构
3. **性能问题**: 检查缓存命中率和并发设置

### 调试工具
- 查看日志输出了解系统运行状态
- 使用维护脚本生成详细报告
- 运行测试脚本验证系统功能

## 📊 监控指标

### 关键指标
- **成功率**: 产品信息获取成功率
- **探测频率**: 动态探测触发频率
- **缓存命中率**: 数据库缓存命中率
- **置信度分布**: 样本置信度分布情况

### 性能指标
- **平均响应时间**: 产品信息获取平均耗时
- **探测耗时**: 动态探测平均耗时
- **数据库性能**: 数据库查询性能

通过这个智能basket系统，可以显著提高Wildberries产品信息获取的准确性和效率，同时具备自学习和自维护能力。
