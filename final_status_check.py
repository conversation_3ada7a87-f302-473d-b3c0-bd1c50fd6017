#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终状态检查脚本
"""

import requests
import json
import time
from datetime import datetime

def check_docker_containers():
    """检查Docker容器状态"""
    print("🐳 Docker容器状态:")
    print("   请手动运行: docker ps")
    print("   确认 product-similarity-server 状态为 healthy")

def check_direct_api():
    """检查直接API访问"""
    print("\n🔍 直接API访问测试:")
    try:
        # 健康检查
        response = requests.get("http://localhost:8001/health", timeout=10)
        if response.status_code == 200:
            print("   ✅ 健康检查通过")
        else:
            print(f"   ❌ 健康检查失败: {response.status_code}")
            return False
        
        # API功能测试
        response = requests.post(
            "http://localhost:8001/keyword-matching",
            json={
                "keyword": "люстра",
                "target_product_id": 253486273
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API功能正常")
            print(f"   📊 相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            print(f"   📊 相似产品数: {data.get('data', {}).get('similar_count', 'N/A')}")
            print(f"   📊 缓存状态: {data.get('data', {}).get('from_cache', 'N/A')}")
            return True
        else:
            print(f"   ❌ API功能异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 直接API访问失败: {e}")
        return False

def check_consul_registration():
    """检查Consul服务注册"""
    print("\n🏛️ Consul服务注册检查:")
    try:
        response = requests.get("http://localhost:8500/v1/catalog/services", timeout=10)
        if response.status_code == 200:
            services = response.json()
            if 'product-similarity' in services:
                print("   ✅ 服务已注册到Consul")
                
                # 检查服务详情
                response = requests.get("http://localhost:8500/v1/catalog/service/product-similarity", timeout=10)
                if response.status_code == 200:
                    service_details = response.json()
                    if service_details:
                        service = service_details[0]
                        print(f"   📍 服务地址: {service.get('ServiceAddress', 'N/A')}:{service.get('ServicePort', 'N/A')}")
                        print(f"   🏷️ 服务标签: {service.get('ServiceTags', [])}")
                return True
            else:
                print("   ❌ 服务未注册到Consul")
                print(f"   📋 已注册服务: {list(services.keys())}")
                return False
        else:
            print(f"   ❌ Consul访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Consul检查失败: {e}")
        return False

def check_gateway_access():
    """检查网关访问"""
    print("\n🌐 网关访问测试:")
    try:
        response = requests.post(
            "http://localhost/api/product-similarity/keyword-matching",
            json={
                "keyword": "люстра",
                "target_product_id": 253486273
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 网关访问正常")
            print(f"   📊 相似度: {data.get('data', {}).get('avg_similarity', 'N/A')}")
            return True
        else:
            print(f"   ❌ 网关访问异常: {response.status_code}")
            print(f"   📄 响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ 网关访问失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🚀 最终状态检查")
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    results = []
    
    # 执行各项检查
    check_docker_containers()
    results.append(check_direct_api())
    results.append(check_consul_registration())
    results.append(check_gateway_access())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 检查结果汇总:")
    
    test_names = [
        "直接API访问",
        "Consul服务注册",
        "网关访问"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 正常" if result else "❌ 异常"
        print(f"   {i+1}. {name}: {status}")
    
    print(f"\n🎯 总体状态: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查通过！服务部署成功！")
        print("\n📝 服务访问信息:")
        print("   直接访问: http://localhost:8001/keyword-matching")
        print("   网关访问: http://localhost/api/product-similarity/keyword-matching")
        print("   健康检查: http://localhost:8001/health")
        print("   Consul UI: http://localhost:8501")
    else:
        print("⚠️ 部分检查失败，请检查相关配置")
    
    return passed == total

if __name__ == "__main__":
    main()
