#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高并发测试关键词匹配功能（包含图片描述）
使用test_keywords.py获取关键词与253486273进行匹配
保持10个任务同时进行并发，任务结束后补充新任务
"""

import asyncio
import aiohttp
import json
import time
import random
from datetime import datetime
from test_keywords import keyword_info

# 配置
API_BASE_URL = "http://localhost:8001"  # 直接访问业务服务
TARGET_PRODUCT_ID = 253486273
CONCURRENT_TASKS = 10  # 并发任务数量
TOTAL_TESTS = 100  # 总测试数量

class ConcurrencyTestManager:
    def __init__(self):
        self.completed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.cache_hit_count = 0
        self.start_time = None
        self.results = []
        self.active_tasks = set()
        self.keyword_index = 0
        
    async def make_request(self, session: aiohttp.ClientSession, keyword: str, task_id: int) -> dict:
        """发送关键词匹配请求"""
        url = f"{API_BASE_URL}/keyword-matching"
        data = {
            "keyword": keyword,
            "target_product_id": TARGET_PRODUCT_ID
        }
        
        start_time = time.time()
        try:
            async with session.post(url, json=data, timeout=aiohttp.ClientTimeout(total=120)) as response:
                if response.status == 200:
                    result = await response.json()
                    duration = time.time() - start_time
                    
                    if result.get("status") == "success":
                        data = result.get("data", {})
                        return {
                            "task_id": task_id,
                            "keyword": keyword,
                            "status": "success",
                            "duration": duration,
                            "avg_similarity": data.get("avg_similarity", 0),
                            "similar_count": data.get("similar_count", 0),
                            "competitor_count": data.get("competitor_count", 0),
                            "valid_scores": data.get("valid_scores", 0),
                            "from_cache": data.get("from_cache", False),
                            "search_products_count": data.get("search_products_count", 0),
                            "compared_products_count": data.get("compared_products_count", 0),
                            "detailed_results_count": len(data.get("detailed_results", []))
                        }
                    else:
                        return {
                            "task_id": task_id,
                            "keyword": keyword,
                            "status": "api_error",
                            "duration": duration,
                            "error": result.get("message", "Unknown API error")
                        }
                else:
                    return {
                        "task_id": task_id,
                        "keyword": keyword,
                        "status": "http_error",
                        "duration": time.time() - start_time,
                        "error": f"HTTP {response.status}"
                    }
                    
        except asyncio.TimeoutError:
            return {
                "task_id": task_id,
                "keyword": keyword,
                "status": "timeout",
                "duration": time.time() - start_time,
                "error": "Request timeout"
            }
        except Exception as e:
            return {
                "task_id": task_id,
                "keyword": keyword,
                "status": "exception",
                "duration": time.time() - start_time,
                "error": str(e)
            }
    
    def get_next_keyword(self) -> str:
        """获取下一个关键词"""
        if self.keyword_index >= len(keyword_info):
            self.keyword_index = 0  # 循环使用关键词
        
        keyword = keyword_info[self.keyword_index]["keyword"]
        self.keyword_index += 1
        return keyword
    
    def print_progress(self):
        """打印进度信息"""
        if self.start_time:
            elapsed = time.time() - self.start_time
            rate = self.completed_count / elapsed if elapsed > 0 else 0
            
            print(f"\r进度: {self.completed_count}/{TOTAL_TESTS} | "
                  f"成功: {self.success_count} | "
                  f"失败: {self.error_count} | "
                  f"缓存命中: {self.cache_hit_count} | "
                  f"活跃任务: {len(self.active_tasks)} | "
                  f"速率: {rate:.2f}/s", end="", flush=True)
    
    async def worker_task(self, session: aiohttp.ClientSession, task_id: int):
        """工作任务"""
        while self.completed_count < TOTAL_TESTS:
            keyword = self.get_next_keyword()
            
            # 执行请求
            result = await self.make_request(session, keyword, task_id)
            
            # 更新统计
            self.completed_count += 1
            self.results.append(result)
            
            if result["status"] == "success":
                self.success_count += 1
                if result.get("from_cache"):
                    self.cache_hit_count += 1
            else:
                self.error_count += 1
            
            # 打印详细结果
            if result["status"] == "success":
                cache_status = "🟢缓存" if result.get("from_cache") else "🔵新请求"
                print(f"\n[任务{task_id:02d}] ✅ {keyword[:20]}... | "
                      f"相似度:{result['avg_similarity']} | "
                      f"相似产品:{result['similar_count']} | "
                      f"竞品:{result['competitor_count']} | "
                      f"详细结果:{result['detailed_results_count']} | "
                      f"{cache_status} | "
                      f"{result['duration']:.2f}s")
            else:
                print(f"\n[任务{task_id:02d}] ❌ {keyword[:20]}... | "
                      f"错误: {result.get('error', 'Unknown')} | "
                      f"{result['duration']:.2f}s")
            
            self.print_progress()
            
            # 如果已完成所有测试，退出
            if self.completed_count >= TOTAL_TESTS:
                break
    
    async def run_test(self):
        """运行高并发测试"""
        print(f"开始高并发测试:")
        print(f"目标产品ID: {TARGET_PRODUCT_ID}")
        print(f"并发任务数: {CONCURRENT_TASKS}")
        print(f"总测试数量: {TOTAL_TESTS}")
        print(f"关键词总数: {len(keyword_info)}")
        print("=" * 80)
        
        self.start_time = time.time()
        
        # 创建HTTP会话
        connector = aiohttp.TCPConnector(limit=50, limit_per_host=20)
        async with aiohttp.ClientSession(connector=connector) as session:
            # 创建并发任务
            tasks = []
            for i in range(CONCURRENT_TASKS):
                task = asyncio.create_task(self.worker_task(session, i + 1))
                tasks.append(task)
                self.active_tasks.add(task)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks, return_exceptions=True)
        
        # 计算最终统计
        total_time = time.time() - self.start_time
        success_rate = (self.success_count / self.completed_count * 100) if self.completed_count > 0 else 0
        cache_hit_rate = (self.cache_hit_count / self.success_count * 100) if self.success_count > 0 else 0
        avg_duration = sum(r.get('duration', 0) for r in self.results) / len(self.results) if self.results else 0
        
        print(f"\n\n" + "=" * 80)
        print("测试完成!")
        print("=" * 80)
        print(f"总耗时: {total_time:.2f}秒")
        print(f"完成任务: {self.completed_count}/{TOTAL_TESTS}")
        print(f"成功率: {success_rate:.1f}% ({self.success_count}/{self.completed_count})")
        print(f"失败数: {self.error_count}")
        print(f"缓存命中率: {cache_hit_rate:.1f}% ({self.cache_hit_count}/{self.success_count})")
        print(f"平均响应时间: {avg_duration:.2f}秒")
        print(f"整体吞吐量: {self.completed_count/total_time:.2f} 请求/秒")
        
        # 分析错误类型
        error_types = {}
        for result in self.results:
            if result["status"] != "success":
                error_type = result["status"]
                error_types[error_type] = error_types.get(error_type, 0) + 1
        
        if error_types:
            print(f"\n错误类型分析:")
            for error_type, count in error_types.items():
                print(f"  {error_type}: {count}")
        
        # 显示相似度分布
        similarities = [r.get('avg_similarity', 0) for r in self.results if r["status"] == "success"]
        if similarities:
            print(f"\n相似度分析:")
            print(f"  最高相似度: {max(similarities)}")
            print(f"  最低相似度: {min(similarities)}")
            print(f"  平均相似度: {sum(similarities)/len(similarities):.1f}")
        
        # 显示前5个最佳结果
        successful_results = [r for r in self.results if r["status"] == "success"]
        if successful_results:
            top_results = sorted(successful_results, key=lambda x: x.get('avg_similarity', 0), reverse=True)[:5]
            print(f"\n前5个最佳匹配:")
            for i, result in enumerate(top_results, 1):
                cache_status = "缓存" if result.get("from_cache") else "新请求"
                print(f"  {i}. {result['keyword'][:30]}... | "
                      f"相似度:{result['avg_similarity']} | "
                      f"相似产品:{result['similar_count']} | "
                      f"竞品:{result['competitor_count']} | "
                      f"{cache_status}")

async def main():
    """主函数"""
    if not keyword_info:
        print("❌ 错误: test_keywords.py 中没有找到关键词数据")
        return
    
    print(f"加载了 {len(keyword_info)} 个关键词")
    print("开始高并发测试...")
    
    manager = ConcurrencyTestManager()
    await manager.run_test()

if __name__ == "__main__":
    asyncio.run(main())
