"""
专注测试 keywordMatching 函数
测试要点：
1. 检查产品信息中是否有 images_description_text 字段且不为空
2. 测试20个并发请求
3. 使用真实数据进行测试
"""
import asyncio
import sys
import os
import json
import time
import random
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.keyword_matching import keywordMatching
from product_similarity.db import init_pool, close_pool
from test_keywords import keyword_info
from test_product_ids import nm_ids

class KeywordMatchingTest:
    def __init__(self, concurrent_limit=20, test_count=20):
        self.concurrent_limit = concurrent_limit
        self.test_count = test_count
        self.results = []
        self.success_count = 0
        self.error_count = 0
        self.image_desc_success_count = 0
        self.semaphore = asyncio.Semaphore(concurrent_limit)
    
    async def test_single_request(self, keyword: str, target_product_id: int, test_index: int):
        """测试单个请求"""
        async with self.semaphore:
            print(f"🧪 测试 #{test_index}: '{keyword[:40]}...' -> {target_product_id}")
            
            start_time = time.time()
            try:
                # 调用 keywordMatching 函数
                result = await keywordMatching(keyword, target_product_id)
                duration = time.time() - start_time
                
                # 检查图片描述字段
                image_check = self.check_image_descriptions(result)
                
                print(f"  ✅ 成功 ({duration:.2f}秒)")
                print(f"  📊 相似度: {result.get('avg_similarity', 'N/A')}")
                print(f"  🖼️ 目标产品图片描述: {'有' if image_check['target_has_desc'] else '无'} ({image_check['target_desc_length']}字符)")
                print(f"  🔍 相似产品图片描述: {image_check['similar_with_desc']}/{image_check['total_similar']}")
                
                if image_check['target_has_desc']:
                    self.image_desc_success_count += 1
                
                self.success_count += 1
                self.results.append({
                    "test_index": test_index,
                    "keyword": keyword,
                    "target_product_id": target_product_id,
                    "status": "success",
                    "duration": duration,
                    "image_check": image_check,
                    "result": result
                })
                
            except Exception as e:
                duration = time.time() - start_time
                print(f"  ❌ 失败 ({duration:.2f}秒): {str(e)[:100]}...")
                
                self.error_count += 1
                self.results.append({
                    "test_index": test_index,
                    "keyword": keyword,
                    "target_product_id": target_product_id,
                    "status": "error",
                    "duration": duration,
                    "error": str(e)
                })
    
    def check_image_descriptions(self, result: dict) -> dict:
        """检查图片描述字段"""
        check = {
            "target_has_desc": False,
            "target_desc_length": 0,
            "similar_with_desc": 0,
            "total_similar": 0
        }
        
        # 检查目标产品
        target_product = result.get("target_product", {})
        if target_product:
            product_info = target_product.get("product_info", {})
            desc_text = product_info.get("images_description_text", "")
            if desc_text and desc_text.strip():
                check["target_has_desc"] = True
                check["target_desc_length"] = len(desc_text)
        
        # 检查相似产品
        similar_products = result.get("similar_products", [])
        check["total_similar"] = len(similar_products)
        
        for product in similar_products:
            product_info = product.get("product_info", {})
            desc_text = product_info.get("images_description_text", "")
            if desc_text and desc_text.strip():
                check["similar_with_desc"] += 1
        
        return check
    
    async def run_test(self):
        """运行测试"""
        print(f"🚀 keywordMatching 函数测试")
        print(f"测试数量: {self.test_count}")
        print(f"并发数量: {self.concurrent_limit}")
        print(f"关键词库: {len(keyword_info)} 个")
        print(f"产品ID库: {len(nm_ids)} 个")
        print("=" * 60)
        
        # 初始化数据库
        try:
            await init_pool()
            print("✅ 数据库连接成功")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return
        
        start_time = time.time()
        
        # 准备测试数据
        test_cases = []
        for i in range(self.test_count):
            keyword = random.choice(keyword_info)
            target_product_id = int(random.choice(nm_ids))
            test_cases.append((keyword, target_product_id, i + 1))
        
        # 创建并发任务
        tasks = []
        for keyword, target_product_id, test_index in test_cases:
            task = asyncio.create_task(
                self.test_single_request(keyword, target_product_id, test_index)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        total_duration = time.time() - start_time
        
        # 关闭数据库
        try:
            await close_pool()
        except:
            pass
        
        # 打印总结
        self.print_summary(total_duration)
    
    def print_summary(self, total_duration: float):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        
        # 基本统计
        print(f"总测试数量: {self.test_count}")
        print(f"成功数量: {self.success_count}")
        print(f"失败数量: {self.error_count}")
        print(f"成功率: {(self.success_count/self.test_count)*100:.1f}%")
        print(f"总耗时: {total_duration:.2f}秒")
        print(f"平均耗时: {total_duration/self.test_count:.2f}秒/个")
        
        # 图片描述检查结果
        print(f"\n🖼️ 图片描述字段检查:")
        print(f"目标产品有描述: {self.image_desc_success_count}/{self.success_count}")
        if self.success_count > 0:
            desc_rate = (self.image_desc_success_count / self.success_count) * 100
            print(f"图片描述成功率: {desc_rate:.1f}%")
        
        # 并发测试结果
        print(f"\n🔄 并发测试结果:")
        print(f"并发数量: {self.concurrent_limit}")
        if self.success_count == self.test_count:
            print(f"✅ 所有 {self.concurrent_limit} 个并发请求都成功")
        else:
            print(f"⚠️ {self.error_count} 个请求失败")
        
        # 性能统计
        successful_results = [r for r in self.results if r["status"] == "success"]
        if successful_results:
            durations = [r["duration"] for r in successful_results]
            print(f"\n⚡ 性能统计:")
            print(f"平均响应时间: {sum(durations)/len(durations):.2f}秒")
            print(f"最快响应: {min(durations):.2f}秒")
            print(f"最慢响应: {max(durations):.2f}秒")
        
        # 显示部分成功案例
        print(f"\n✅ 成功案例示例:")
        success_cases = [r for r in self.results if r["status"] == "success"][:5]
        for case in success_cases:
            image_check = case["image_check"]
            print(f"  #{case['test_index']}: {case['keyword'][:30]}... -> {case['target_product_id']}")
            print(f"    图片描述: {'✅' if image_check['target_has_desc'] else '❌'} ({image_check['target_desc_length']}字符)")
        
        # 显示失败案例
        failed_cases = [r for r in self.results if r["status"] == "error"]
        if failed_cases:
            print(f"\n❌ 失败案例:")
            for case in failed_cases[:3]:
                print(f"  #{case['test_index']}: {case['keyword'][:30]}... -> {case['target_product_id']}")
                print(f"    错误: {case['error'][:80]}...")
        
        # 最终结论
        print(f"\n" + "=" * 60)
        if self.success_count == self.test_count and self.image_desc_success_count > 0:
            print("🎉 测试完全成功！")
            print("✅ 所有并发请求都通过")
            print("✅ images_description_text 字段正常工作")
            print("✅ 使用真实数据测试")
        elif self.success_count > 0:
            print("⚠️ 测试部分成功")
            if self.image_desc_success_count == 0:
                print("❌ 图片描述字段可能有问题")
            if self.error_count > 0:
                print(f"❌ 有 {self.error_count} 个请求失败")
        else:
            print("❌ 测试失败")
        print("=" * 60)

async def main():
    """主函数"""
    # 测试参数
    CONCURRENT_LIMIT = 20  # 20个并发
    TEST_COUNT = 20        # 20个测试
    
    print("🔥 keywordMatching 函数专项测试")
    print(f"并发数量: {CONCURRENT_LIMIT}")
    print(f"测试数量: {TEST_COUNT}")
    
    # 创建测试实例
    tester = KeywordMatchingTest(
        concurrent_limit=CONCURRENT_LIMIT,
        test_count=TEST_COUNT
    )
    
    # 运行测试
    await tester.run_test()

if __name__ == "__main__":
    asyncio.run(main())
