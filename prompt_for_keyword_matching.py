#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键词匹配分析的Prompt构建器
基于test_tasks.py和keywordMatching函数中的compare_products_by_ids函数
处理前50个任务
"""

import json
from typing import Dict, Any, List

# 从test_tasks.py导入任务数据
tasks = [
    {"keyword": "органайзер для проводов", "target_product_id": 303244457}, 
    {"keyword": "держатель для проводов", "target_product_id": 303244457}, 
    {"keyword": "органайзер для проводов под стол", "target_product_id": 303244457}, 
    {"keyword": "кабельный органайзер", "target_product_id": 303244457}, 
    {"keyword": "держатель проводов", "target_product_id": 303244457}, 
    {"keyword": "держатель для проводов на стол", "target_product_id": 303244457}, 
    {"keyword": "крепление проводов", "target_product_id": 303244457}, 
    {"keyword": "держатель кабеля", "target_product_id": 303244457}, 
    {"keyword": "зажим для проводов", "target_product_id": 303244457}, 
    {"keyword": "органайзер на стол", "target_product_id": 303244457}, 
    {"keyword": "держатели для проводов и кабелей", "target_product_id": 303244457}, 
    {"keyword": "для проводов под стол", "target_product_id": 303244457}, 
    {"keyword": "крепление для провода", "target_product_id": 303244457}, 
    {"keyword": "для проводов органайзер настольный", "target_product_id": 303244457}, 
    {"keyword": "органайзер для краски", "target_product_id": 303244457}, 
    {"keyword": "хранение проводов", "target_product_id": 303244457}, 
    {"keyword": "держатель для проводов под стол", "target_product_id": 303244457}, 
    {"keyword": "органайзер держатель для проводов", "target_product_id": 303244457}, 
    {"keyword": "отверстие для проводов в стол", "target_product_id": 303244457}, 
    {"keyword": "крепление для проводов под стол", "target_product_id": 303244457}, 
    {"keyword": "держатель для розетки", "target_product_id": 303244457}, 
    {"keyword": "крепление для стола", "target_product_id": 303244457}, 
    {"keyword": "лоток для проводов под стол", "target_product_id": 303244457}, 
    {"keyword": "органайзер", "target_product_id": 303244457}, 
    {"keyword": "для проводов и кабелей органайзер", "target_product_id": 303244457}, 
    {"keyword": "зажим проводов", "target_product_id": 303244457}, 
    {"keyword": "контейнер для проводов", "target_product_id": 303244457}, 
    {"keyword": "организация проводов", "target_product_id": 303244457}, 
    {"keyword": "крепление для проводов на стол", "target_product_id": 303244457}, 
    {"keyword": "органайзер для кабеля под стол", "target_product_id": 303244457}, 
    {"keyword": "держатель для проводов черный", "target_product_id": 303244457}, 
    {"keyword": "кабельный органайзер для проводов", "target_product_id": 303244457}, 
    {"keyword": "держали для проводов", "target_product_id": 303244457}, 
    {"keyword": "зажим кабеля", "target_product_id": 303244457}, 
    {"keyword": "органайзер для проводов под стол держатель для проводов", "target_product_id": 303244457}, 
    {"keyword": "держатель проводов под стол", "target_product_id": 303244457}, 
    {"keyword": "кабельный держатель", "target_product_id": 303244457}, 
    {"keyword": "органайзер для розеток и проводов", "target_product_id": 303244457}, 
    {"keyword": "крепеж для проводов", "target_product_id": 303244457}, 
    {"keyword": "держатель органайзер", "target_product_id": 303244457}, 
    {"keyword": "крепление кабеля к столу", "target_product_id": 303244457}, 
    {"keyword": "подкладка под розетку", "target_product_id": 303244457}, 
    {"keyword": "органайзер для проводов черный", "target_product_id": 303244457}, 
    {"keyword": "лоток для кабелей под стол", "target_product_id": 303244457}, 
    {"keyword": "зажим кабельный", "target_product_id": 303244457}, 
    {"keyword": "скоба для кабеля", "target_product_id": 303244457}, 
    {"keyword": "крепление к столу", "target_product_id": 303244457}, 
    {"keyword": "крепление для проводов и кабелей", "target_product_id": 303244457}, 
    {"keyword": "органайзер железный", "target_product_id": 303244457}, 
    {"keyword": "держатель кабелей под стол", "target_product_id": 303244457}
]

# 系统提示词模板 - 来自similarity.py中的SYSTEM_PROMPT_TEMPLATE
SYSTEM_PROMPT_TEMPLATE = """ 
 #任务
-将两个产品的介绍进行对比,按照我的评分规则得出综合相似评分,总分数范围1-100:
1,判断两个产品的1级类目是否相同,分数5-10分
2,判断两个产品的3级类目是否相似,分数15-25分
3,判断两个产品的标题相似性,分数15-25分
4,判断两个产品的价格相似性,分数10-15分
5,判断两个产的图片描述相似性,分数20-35分

##思考过程
###产品文案参数思考过程
-如果不在1级类目中,大概率产品相似性不相关,应该给予较低的相识评价
-2级类目是产品的细分类,考虑到一个产品可能有多个二级类目,所以你需要理解两个产品都适合彼此的2级类目
-标题中涉及到产品名字,参数,功能,风格,使用场景,产品名字是首要思考的对象,其他参数其次
-如果两个产品的产品名字一致,参数偏差10%视为相似,偏差太大,需要考虑价格偏差,价格偏差15%以内视为相似.反之先观察价格再观察参数也成立
-图片描述主要查看形状，功能,使用场景,风格等维度考虑.与文案描述结合综合判断

##不相似产品定义
-如果两个产品一级类目无相似关系,价格和材质不同，安装方式不同，功能描述不同，参数差异差50%以上的产品,视为不相关产品,判定为1

##扣减规则
-如果两个产品一级类目不同，判定为20分
-如果两个产品一级类目相似，三级类目相似,但是参数偏差20%以上或者安装方式不同视为不相似,材质差异大,价格偏差40%以上视为不相似,判定为20-40分
-如果两个产品一级类目相似,形状差异大,如圆形与长条型,圆盘型与圆柱型,圆形与不规则型,扣10-20分
-如果两个产品使用场景差异大,如大厅顶灯与桌面台灯,室外路灯与室内灯具,先查看3级类目是否类似,如果不类似,扣15-25分.否则不扣分

##产品信息:
###产品1:
    {target_product}

###产品2:
    {product}

##输出要求
只需要输出1-100的综合相似值,不需要输出任何的推理思考过程.1为完全不相似,100为完全相似

##Output format:
{{"similar_scores":相似值,"reson":中文分析说明}}

##示例
{{"similar_scores":40,"reson":中文分析说明}}
""".strip()

# Root提示词 - 基于keywordMatching函数的工作流程
ROOT_PROMPT_TEMPLATE = """
# 关键词匹配分析系统

## 系统概述
这是一个基于产品相似度比较的关键词匹配分析系统。系统通过以下步骤工作：

1. **关键词搜索**: 使用给定关键词在Wildberries平台搜索相关产品
2. **产品筛选**: 提取搜索结果中的前50个产品ID
3. **相似度比较**: 将每个搜索到的产品与目标产品进行相似度比较
4. **统计分析**: 分析相似度分数，计算平均相似度、相似产品数量等统计指标
5. **结果保存**: 将分析结果保存到数据库中

## 核心功能模块

### 1. 产品相似度比较 (compare_products_by_ids)
- **功能**: 比较两个产品的相似度
- **输入**: 两个产品ID (product_id1, product_id2)
- **输出**: 相似度评分 (1-100分) 和分析说明
- **评分标准**:
  - 1级类目相同: 5-10分
  - 3级类目相似: 15-25分  
  - 标题相似性: 15-25分
  - 价格相似性: 10-15分
  - 图片描述相似性: 20-35分

### 2. 关键词匹配分析 (keywordMatching)
- **功能**: 分析关键词与目标产品的关联度
- **工作流程**:
  1. 检查缓存中是否已有分析结果
  2. 通过关键词搜索获取前50个相关产品
  3. 使用信号量控制并发，与目标产品进行相似度比较
  4. 统计分析结果并保存到数据库
- **输出指标**:
  - 平均相似度 (avg_similarity)
  - 相似产品数量 (similar_count, >65分)
  - 竞品数量 (competitor_count, >80分)
  - 有效评分数量 (valid_scores)

## 任务数据
处理前50个任务，主要围绕以下产品类别：
- 目标产品ID: 303244457 (线缆整理相关产品)
- 关键词类型: 主要为俄语关键词，涉及线缆整理、桌面收纳等相关产品

## 使用场景
1. **电商平台关键词优化**: 分析关键词与产品的匹配度
2. **竞品分析**: 识别同类竞争产品
3. **产品定位**: 了解产品在市场中的位置
4. **搜索引擎优化**: 优化产品在搜索结果中的表现

## 技术特点
- **异步处理**: 使用asyncio进行高效的并发处理
- **缓存机制**: 避免重复计算，提高系统效率
- **错误处理**: 完善的异常处理和日志记录
- **数据持久化**: 结果自动保存到数据库
- **并发控制**: 使用信号量限制并发请求数量

## 输出格式
```json
{
    "status": "success",
    "data": {
        "keyword": "关键词",
        "target_product_id": 产品ID,
        "avg_similarity": 平均相似度,
        "similar_count": 相似产品数量,
        "competitor_count": 竞品数量,
        "valid_scores": 有效评分数量,
        "created_at": "创建时间",
        "from_cache": false,
        "search_products_count": 搜索到的产品数量,
        "compared_products_count": 实际比较的产品数量
    }
}
```
"""

def generate_prompt_for_task(task_index: int) -> Dict[str, str]:
    """
    为指定任务生成完整的prompt
    
    Args:
        task_index: 任务索引 (0-49)
        
    Returns:
        包含system和root prompt的字典
    """
    if task_index >= len(tasks) or task_index < 0:
        raise ValueError(f"任务索引超出范围: {task_index}, 有效范围: 0-{len(tasks)-1}")
    
    task = tasks[task_index]
    
    return {
        "task_info": {
            "index": task_index + 1,
            "keyword": task["keyword"],
            "target_product_id": task["target_product_id"]
        },
        "system_prompt": SYSTEM_PROMPT_TEMPLATE,
        "root_prompt": ROOT_PROMPT_TEMPLATE,
        "combined_prompt": f"""
{ROOT_PROMPT_TEMPLATE}

---

## 当前任务
- 任务编号: {task_index + 1}/50
- 关键词: {task["keyword"]}
- 目标产品ID: {task["target_product_id"]}

---

## 产品比较系统提示词
{SYSTEM_PROMPT_TEMPLATE}
        """.strip()
    }

def generate_all_prompts() -> List[Dict[str, str]]:
    """
    生成前50个任务的所有prompt
    
    Returns:
        包含所有任务prompt的列表
    """
    return [generate_prompt_for_task(i) for i in range(min(50, len(tasks)))]

def save_prompts_to_files():
    """
    将所有prompt保存到单独的文件中
    """
    import os

    # 创建输出目录
    output_dir = "generated_prompts"
    os.makedirs(output_dir, exist_ok=True)

    # 生成所有prompt
    all_prompts = generate_all_prompts()

    for i, prompt_data in enumerate(all_prompts):
        task_info = prompt_data["task_info"]
        filename = f"task_{i+1:02d}_{task_info['keyword'][:20].replace(' ', '_')}.txt"
        # 清理文件名中的特殊字符
        filename = "".join(c for c in filename if c.isalnum() or c in "._-")

        filepath = os.path.join(output_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"任务 {i+1}/50\n")
            f.write(f"关键词: {task_info['keyword']}\n")
            f.write(f"目标产品ID: {task_info['target_product_id']}\n")
            f.write("=" * 80 + "\n\n")
            f.write(prompt_data["combined_prompt"])

        print(f"已保存: {filepath}")

    print(f"\n所有prompt已保存到 {output_dir} 目录")

def export_tasks_summary():
    """
    导出任务摘要信息
    """
    summary = {
        "total_tasks": min(50, len(tasks)),
        "target_product_ids": list(set(task["target_product_id"] for task in tasks[:50])),
        "keywords_by_product": {}
    }

    # 按产品ID分组关键词
    for task in tasks[:50]:
        pid = task["target_product_id"]
        if pid not in summary["keywords_by_product"]:
            summary["keywords_by_product"][pid] = []
        summary["keywords_by_product"][pid].append(task["keyword"])

    # 保存摘要
    with open("tasks_summary.json", 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)

    print("任务摘要已保存到 tasks_summary.json")
    return summary

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "save":
            # 保存所有prompt到文件
            save_prompts_to_files()
        elif sys.argv[1] == "summary":
            # 导出任务摘要
            summary = export_tasks_summary()
            print("\n=== 任务摘要 ===")
            print(json.dumps(summary, ensure_ascii=False, indent=2))
        elif sys.argv[1].isdigit():
            # 显示指定任务的prompt
            task_index = int(sys.argv[1]) - 1
            if 0 <= task_index < min(50, len(tasks)):
                prompt_data = generate_prompt_for_task(task_index)
                print("=== 任务信息 ===")
                print(json.dumps(prompt_data["task_info"], ensure_ascii=False, indent=2))
                print("\n=== 完整Prompt ===")
                print(prompt_data["combined_prompt"])
            else:
                print(f"错误: 任务编号超出范围 (1-{min(50, len(tasks))})")
        else:
            print("用法:")
            print("  python prompt_for_keyword_matching.py          # 显示第一个任务")
            print("  python prompt_for_keyword_matching.py <数字>   # 显示指定任务")
            print("  python prompt_for_keyword_matching.py save     # 保存所有prompt到文件")
            print("  python prompt_for_keyword_matching.py summary  # 导出任务摘要")
    else:
        # 默认：显示第一个任务的prompt
        first_task_prompt = generate_prompt_for_task(0)

        print("=== 任务信息 ===")
        print(json.dumps(first_task_prompt["task_info"], ensure_ascii=False, indent=2))

        print("\n=== 完整Prompt ===")
        print(first_task_prompt["combined_prompt"])

        print(f"\n=== 总任务数量 ===")
        print(f"前50个任务已准备完成，共 {min(50, len(tasks))} 个任务")

        print("\n=== 使用说明 ===")
        print("运行 'python prompt_for_keyword_matching.py save' 可将所有prompt保存到文件")
        print("运行 'python prompt_for_keyword_matching.py summary' 可查看任务摘要")
