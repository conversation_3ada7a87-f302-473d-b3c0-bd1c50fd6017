# 产品相似度业务服务部署脚本 (PowerShell版本)
# 用于将业务服务接入到现有的微服务集群

param(
    [Parameter(Position=0)]
    [ValidateSet("deploy", "stop", "restart", "logs", "status")]
    [string]$Action = "deploy"
)

# 颜色函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 检查前置条件
function Test-Prerequisites {
    Write-Info "检查前置条件..."
    
    # 检查Docker
    try {
        $null = docker --version
        Write-Success "Docker 已安装"
    }
    catch {
        Write-Error "Docker 未安装，请先安装 Docker Desktop"
        exit 1
    }
    
    # 检查Docker Compose
    try {
        $null = docker-compose --version
        Write-Success "Docker Compose 已安装"
    }
    catch {
        Write-Error "Docker Compose 未安装"
        exit 1
    }
    
    Write-Success "前置条件检查通过"
}

# 检查主服务状态
function Test-Infrastructure {
    Write-Info "检查主服务（基础设施）状态..."
    
    # 检查Consul
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8500/v1/status/leader" -TimeoutSec 5 -ErrorAction Stop
        Write-Success "Consul 服务正常"
    }
    catch {
        Write-Error "Consul 服务不可访问，请确保主服务正在运行"
        Write-Info "启动主服务命令: docker-compose -f docker-compose.infrastructure.yml up -d"
        exit 1
    }
    
    # 检查网关
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/gateway/status" -TimeoutSec 5 -ErrorAction Stop
        Write-Success "网关服务正常"
    }
    catch {
        Write-Warning "网关服务不可访问，但不影响服务注册"
    }
}

# 检查网络
function Test-Network {
    Write-Info "检查微服务网络..."
    
    $networks = docker network ls --format "{{.Name}}"
    if ($networks -contains "microservices") {
        Write-Success "microservices 网络存在"
    }
    else {
        Write-Info "创建 microservices 网络..."
        docker network create microservices
        Write-Success "microservices 网络创建完成"
    }
}

# 配置环境变量
function Set-Environment {
    Write-Info "配置环境变量..."
    
    if (-not (Test-Path ".env")) {
        if (Test-Path ".env.business.example") {
            Copy-Item ".env.business.example" ".env"
            Write-Success "已复制环境变量模板到 .env"
            Write-Warning "请编辑 .env 文件，配置数据库连接、AI端点等信息"
            
            # 提示用户编辑配置
            $edit = Read-Host "是否现在编辑 .env 文件？(y/n)"
            if ($edit -eq "y" -or $edit -eq "Y") {
                notepad .env
            }
        }
        else {
            Write-Error ".env.business.example 文件不存在"
            exit 1
        }
    }
    else {
        Write-Success ".env 文件已存在"
    }
}

# 构建服务镜像
function Build-Service {
    Write-Info "构建产品相似度服务镜像..."
    
    if (Test-Path "Dockerfile") {
        docker-compose -f docker-compose.business.yml build
        if ($LASTEXITCODE -eq 0) {
            Write-Success "服务镜像构建完成"
        }
        else {
            Write-Error "服务镜像构建失败"
            exit 1
        }
    }
    else {
        Write-Error "Dockerfile 不存在"
        exit 1
    }
}

# 启动业务服务
function Start-Service {
    Write-Info "启动产品相似度业务服务..."
    
    docker-compose -f docker-compose.business.yml up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Info "等待服务启动..."
        Start-Sleep -Seconds 10
        
        # 检查服务状态
        $status = docker-compose -f docker-compose.business.yml ps --format "table {{.State}}"
        if ($status -match "Up") {
            Write-Success "业务服务启动成功"
        }
        else {
            Write-Error "业务服务启动失败"
            Write-Info "查看日志: docker-compose -f docker-compose.business.yml logs"
            exit 1
        }
    }
    else {
        Write-Error "服务启动失败"
        exit 1
    }
}

# 验证服务注册
function Test-Registration {
    Write-Info "验证服务注册..."
    
    Start-Sleep -Seconds 5
    
    # 检查Consul中的服务注册
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8500/v1/catalog/service/product-similarity" -TimeoutSec 10
        if ($response.Content -match "product-similarity") {
            Write-Success "服务已成功注册到 Consul"
        }
        else {
            Write-Warning "服务可能未注册到 Consul，请检查日志"
        }
    }
    catch {
        Write-Warning "无法检查服务注册状态"
    }
    
    # 检查健康状态
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -TimeoutSec 10
        if ($response.Content -match "healthy") {
            Write-Success "服务健康检查通过"
        }
        else {
            Write-Warning "服务健康检查失败"
        }
    }
    catch {
        Write-Warning "无法访问服务健康检查端点"
    }
}

# 测试网关路由
function Test-Gateway {
    Write-Info "测试网关路由..."
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/api/product-similarity/health" -TimeoutSec 10
        if ($response.Content -match "healthy") {
            Write-Success "网关路由测试通过"
        }
        else {
            Write-Warning "网关路由测试失败"
        }
    }
    catch {
        Write-Warning "网关路由测试失败，可能需要等待consul-template更新配置"
        Write-Info "可以直接访问服务: http://localhost:8000"
    }
}

# 显示部署信息
function Show-DeploymentInfo {
    Write-Success "部署完成！"
    Write-Host ""
    Write-Host "=== 服务访问信息 ===" -ForegroundColor Cyan
    Write-Host "直接访问: http://localhost:8000"
    Write-Host "网关访问: http://localhost/api/product-similarity"
    Write-Host "健康检查: http://localhost:8000/health"
    Write-Host "API文档: http://localhost:8000/docs"
    Write-Host "Consul UI: http://localhost:8500"
    Write-Host ""
    Write-Host "=== 常用命令 ===" -ForegroundColor Cyan
    Write-Host "查看服务状态: docker-compose -f docker-compose.business.yml ps"
    Write-Host "查看服务日志: docker-compose -f docker-compose.business.yml logs -f"
    Write-Host "停止服务: docker-compose -f docker-compose.business.yml down"
    Write-Host "重启服务: docker-compose -f docker-compose.business.yml restart"
    Write-Host ""
    Write-Host "=== 测试命令 ===" -ForegroundColor Cyan
    Write-Host "健康检查: curl http://localhost:8000/health"
    Write-Host "服务信息: curl http://localhost:8000/info"
    Write-Host "通过网关: curl http://localhost/api/product-similarity/health"
}

# 主函数
function Main {
    Write-Host "=== 产品相似度业务服务部署脚本 ===" -ForegroundColor Magenta
    Write-Host ""
    
    switch ($Action) {
        "deploy" {
            Test-Prerequisites
            Test-Infrastructure
            Test-Network
            Set-Environment
            Build-Service
            Start-Service
            Test-Registration
            Test-Gateway
            Show-DeploymentInfo
        }
        "stop" {
            Write-Info "停止业务服务..."
            docker-compose -f docker-compose.business.yml down
            Write-Success "业务服务已停止"
        }
        "restart" {
            Write-Info "重启业务服务..."
            docker-compose -f docker-compose.business.yml restart
            Write-Success "业务服务已重启"
        }
        "logs" {
            docker-compose -f docker-compose.business.yml logs -f
        }
        "status" {
            docker-compose -f docker-compose.business.yml ps
        }
    }
}

# 执行主函数
Main
