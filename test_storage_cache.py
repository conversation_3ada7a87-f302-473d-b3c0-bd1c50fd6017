#!/usr/bin/env python3
"""
测试存储和缓存机制
"""
import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.product_similarity.db import init_pool, close_pool
from src.product_similarity.services.keyword_matching import (
    keywordMatching, 
    get_keyword_analysis_result,
    insert_keyword_analysis_result
)


async def test_storage_and_cache():
    """测试存储和缓存机制"""
    print("🔍 开始测试存储和缓存机制")
    print("=" * 60)
    
    try:
        # 初始化数据库连接池
        await init_pool()
        print("✅ 数据库连接池初始化完成")
        
        # 测试参数
        test_keyword = "люстра потолочная тест"
        test_target_id = 253486273
        
        print(f"\n📊 测试参数:")
        print(f"  关键词: {test_keyword}")
        print(f"  目标产品ID: {test_target_id}")
        
        # 1. 检查是否已存在结果
        print(f"\n1️⃣ 检查数据库中是否已存在结果...")
        existing_result = await get_keyword_analysis_result(test_keyword, test_target_id)
        if existing_result:
            print(f"  ✅ 找到已存在的结果:")
            print(f"    - 平均相似度: {existing_result['avg_similarity']}")
            print(f"    - 相似产品数: {existing_result['similar_count']}")
            print(f"    - 竞品数: {existing_result['competitor_count']}")
            print(f"    - 创建时间: {existing_result['created_at']}")
        else:
            print(f"  ⚪ 数据库中未找到已存在的结果")
        
        # 2. 测试手动插入数据
        print(f"\n2️⃣ 测试手动插入数据...")
        insert_success = await insert_keyword_analysis_result(
            keyword=test_keyword,
            target_product_id=test_target_id,
            avg_similarity=75.5,
            similar_count=10,
            competitor_count=3,
            valid_scores=50
        )
        
        if insert_success:
            print(f"  ✅ 数据插入成功")
        else:
            print(f"  ❌ 数据插入失败")
        
        # 3. 验证插入的数据
        print(f"\n3️⃣ 验证插入的数据...")
        inserted_result = await get_keyword_analysis_result(test_keyword, test_target_id)
        if inserted_result:
            print(f"  ✅ 验证成功，插入的数据:")
            print(f"    - 平均相似度: {inserted_result['avg_similarity']}")
            print(f"    - 相似产品数: {inserted_result['similar_count']}")
            print(f"    - 竞品数: {inserted_result['competitor_count']}")
            print(f"    - 有效样本数: {inserted_result['valid_scores']}")
            print(f"    - 创建时间: {inserted_result['created_at']}")
        else:
            print(f"  ❌ 验证失败，未找到插入的数据")
        
        # 4. 测试 keywordMatching 函数的缓存机制
        print(f"\n4️⃣ 测试 keywordMatching 函数的缓存机制...")
        print(f"  第一次调用 keywordMatching (应该返回缓存结果)...")
        
        result1 = await keywordMatching(test_keyword, test_target_id)
        if result1['status'] == 'success':
            data1 = result1['data']
            print(f"  ✅ 第一次调用成功:")
            print(f"    - 平均相似度: {data1['avg_similarity']}")
            print(f"    - 来自缓存: {data1.get('from_cache', False)}")
            print(f"    - 搜索产品数: {data1.get('search_products_count', 'N/A')}")
            print(f"    - 对比产品数: {data1.get('compared_products_count', 'N/A')}")
        else:
            print(f"  ❌ 第一次调用失败: {result1.get('message', '未知错误')}")
        
        # 5. 测试新关键词（应该执行完整流程）
        print(f"\n5️⃣ 测试新关键词（应该执行完整流程）...")
        new_keyword = f"люстра тест {datetime.now().strftime('%H%M%S')}"
        print(f"  使用新关键词: {new_keyword}")
        
        result2 = await keywordMatching(new_keyword, test_target_id)
        if result2['status'] == 'success':
            data2 = result2['data']
            print(f"  ✅ 新关键词测试成功:")
            print(f"    - 平均相似度: {data2['avg_similarity']}")
            print(f"    - 来自缓存: {data2.get('from_cache', False)}")
            print(f"    - 搜索产品数: {data2.get('search_products_count', 'N/A')}")
            print(f"    - 对比产品数: {data2.get('compared_products_count', 'N/A')}")
            print(f"    - 相似产品数: {data2['similar_count']}")
            print(f"    - 竞品数: {data2['competitor_count']}")
        else:
            print(f"  ❌ 新关键词测试失败: {result2.get('message', '未知错误')}")
        
        # 6. 再次调用新关键词（应该返回缓存结果）
        print(f"\n6️⃣ 再次调用新关键词（应该返回缓存结果）...")
        
        result3 = await keywordMatching(new_keyword, test_target_id)
        if result3['status'] == 'success':
            data3 = result3['data']
            print(f"  ✅ 第二次调用成功:")
            print(f"    - 平均相似度: {data3['avg_similarity']}")
            print(f"    - 来自缓存: {data3.get('from_cache', False)}")
            print(f"    - 搜索产品数: {data3.get('search_products_count', 'N/A')}")
            print(f"    - 对比产品数: {data3.get('compared_products_count', 'N/A')}")
            
            if data3.get('from_cache', False):
                print(f"  🎯 缓存机制工作正常！")
            else:
                print(f"  ⚠️  缓存机制可能有问题，第二次调用没有使用缓存")
        else:
            print(f"  ❌ 第二次调用失败: {result3.get('message', '未知错误')}")
        
        print(f"\n" + "=" * 60)
        print(f"📈 测试总结:")
        print(f"  - 数据库连接: ✅")
        print(f"  - 手动插入数据: {'✅' if insert_success else '❌'}")
        print(f"  - 数据验证: {'✅' if inserted_result else '❌'}")
        print(f"  - keywordMatching 函数: {'✅' if result1['status'] == 'success' else '❌'}")
        print(f"  - 新关键词处理: {'✅' if result2['status'] == 'success' else '❌'}")
        print(f"  - 缓存机制: {'✅' if result3.get('data', {}).get('from_cache', False) else '⚠️'}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("🔒 数据库连接池已关闭")


if __name__ == "__main__":
    asyncio.run(test_storage_and_cache())
