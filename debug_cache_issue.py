#!/usr/bin/env python3
"""
调试缓存问题
"""
import asyncio
import sys
sys.path.append('.')

from src.product_similarity.crud import get_cache
from src.product_similarity.utils import generate_cache_key

async def debug_cache():
    """调试缓存数据格式"""
    
    print("🔍 调试缓存数据格式")
    print("=" * 50)
    
    # 测试关键词
    keyword = "люстра на потолок"
    cache_key = generate_cache_key("wb_search", keyword)
    
    print(f"缓存键: {cache_key}")
    
    try:
        # 获取缓存数据
        cached_data = await get_cache(cache_key)
        
        if cached_data is None:
            print("❌ 缓存中没有数据")
            return
        
        print(f"✅ 缓存数据类型: {type(cached_data)}")
        print(f"缓存数据长度: {len(str(cached_data))}")
        
        if isinstance(cached_data, dict):
            print("✅ 缓存数据是字典类型")
            print(f"字典键: {list(cached_data.keys())}")
            
            # 检查data字段
            if 'data' in cached_data:
                data = cached_data['data']
                print(f"data字段类型: {type(data)}")
                if isinstance(data, dict) and 'products' in data:
                    products = data['products']
                    print(f"products字段类型: {type(products)}")
                    print(f"products数量: {len(products) if isinstance(products, list) else 'N/A'}")
                else:
                    print("❌ data字段不包含products")
            else:
                print("❌ 缓存数据不包含data字段")
                
        elif isinstance(cached_data, str):
            print("❌ 缓存数据是字符串类型（这是问题所在）")
            print(f"字符串前100个字符: {cached_data[:100]}")
            
            # 尝试解析JSON
            import json
            try:
                parsed_data = json.loads(cached_data)
                print("✅ 字符串可以解析为JSON")
                print(f"解析后类型: {type(parsed_data)}")
                if isinstance(parsed_data, dict):
                    print(f"解析后字典键: {list(parsed_data.keys())}")
            except json.JSONDecodeError as e:
                print(f"❌ 字符串无法解析为JSON: {e}")
        else:
            print(f"❌ 缓存数据是未知类型: {type(cached_data)}")
            
    except Exception as e:
        print(f"❌ 获取缓存数据失败: {e}")

if __name__ == "__main__":
    asyncio.run(debug_cache())
