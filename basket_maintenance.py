#!/usr/bin/env python3
"""
Basket数据维护脚本
定期清理低置信度样本，重新计算范围映射
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.crud import cleanup_basket_samples, get_basket_stats
from product_similarity.db import init_pool, close_pool, execute_query
from product_similarity.logging import log_success, log_error, log_info, log_warning

async def cleanup_low_confidence_samples():
    """清理低置信度样本"""
    log_info("开始清理低置信度样本")
    
    try:
        # 清理30天前confidence < 0.5的样本
        cleaned_count = await cleanup_basket_samples()
        
        if cleaned_count > 0:
            log_success(f"清理低置信度样本完成: {cleaned_count} 条")
        else:
            log_info("没有需要清理的低置信度样本")
            
        return cleaned_count
        
    except Exception as e:
        log_error("清理低置信度样本失败", error=e)
        return 0

async def recalculate_basket_ranges():
    """重新计算basket范围映射"""
    log_info("开始重新计算basket范围映射")
    
    try:
        # 调用数据库函数重新计算范围
        updated_count = await execute_query("SELECT pj_similar.recalculate_basket_ranges()")
        
        if updated_count:
            log_success(f"重新计算basket范围完成: {updated_count} 个映射")
        else:
            log_warning("重新计算basket范围未返回结果")
            
        return updated_count
        
    except Exception as e:
        log_error("重新计算basket范围失败", error=e)
        return 0

async def generate_maintenance_report():
    """生成维护报告"""
    log_info("生成维护报告")
    
    try:
        stats = await get_basket_stats()
        
        report = f"""
=== Basket数据维护报告 ===
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

样本统计:
- 总样本数: {stats['sample_stats'].get('total_samples', 0)}
- 唯一basket数: {stats['sample_stats'].get('unique_baskets', 0)}
- 平均置信度: {stats['sample_stats'].get('avg_confidence', 0):.3f}
- 高置信度样本: {stats['sample_stats'].get('high_confidence_samples', 0)}
- 低置信度样本: {stats['sample_stats'].get('low_confidence_samples', 0)}

映射统计:
- 总映射数: {stats['mapping_stats'].get('total_mappings', 0)}
- 映射样本总数: {stats['mapping_stats'].get('total_mapped_samples', 0)}
- 平均映射置信度: {stats['mapping_stats'].get('avg_mapping_confidence', 0):.3f}

=== 报告结束 ===
        """
        
        log_info(report)
        return stats
        
    except Exception as e:
        log_error("生成维护报告失败", error=e)
        return {}

async def optimize_basket_data():
    """优化basket数据"""
    log_info("开始优化basket数据")
    
    try:
        # 删除重复的低置信度样本
        duplicate_query = """
        DELETE FROM pj_similar.basket_samples a 
        USING pj_similar.basket_samples b
        WHERE a.id > b.id 
        AND a.basket = b.basket 
        AND a.short_id = b.short_id 
        AND a.confidence < b.confidence
        """
        
        result = await execute_query(duplicate_query)
        log_info(f"删除重复低置信度样本: {result}")
        
        # 合并相近的样本（相同basket，相邻short_id）
        merge_query = """
        UPDATE pj_similar.basket_samples 
        SET confidence = GREATEST(confidence, 0.9)
        WHERE basket IN (
            SELECT basket 
            FROM pj_similar.basket_samples 
            GROUP BY basket 
            HAVING COUNT(*) >= 5 AND AVG(confidence) >= 0.8
        )
        AND confidence >= 0.8
        """
        
        result = await execute_query(merge_query)
        log_info(f"提升高质量样本置信度: {result}")
        
        log_success("basket数据优化完成")
        
    except Exception as e:
        log_error("优化basket数据失败", error=e)

async def full_maintenance():
    """完整维护流程"""
    log_info("开始完整维护流程")
    
    try:
        # 1. 生成维护前报告
        log_info("=== 维护前状态 ===")
        await generate_maintenance_report()
        
        # 2. 优化数据
        await optimize_basket_data()
        
        # 3. 清理低置信度样本
        cleaned = await cleanup_low_confidence_samples()
        
        # 4. 重新计算范围
        updated = await recalculate_basket_ranges()
        
        # 5. 生成维护后报告
        log_info("=== 维护后状态 ===")
        final_stats = await generate_maintenance_report()
        
        # 6. 总结
        log_success(f"完整维护流程完成 - 清理样本: {cleaned}, 更新映射: {updated}")
        
        return {
            "cleaned_samples": cleaned,
            "updated_mappings": updated,
            "final_stats": final_stats
        }
        
    except Exception as e:
        log_error("完整维护流程失败", error=e)
        return {}

async def quick_maintenance():
    """快速维护（仅清理和重新计算）"""
    log_info("开始快速维护")
    
    try:
        cleaned = await cleanup_low_confidence_samples()
        updated = await recalculate_basket_ranges()
        
        log_success(f"快速维护完成 - 清理样本: {cleaned}, 更新映射: {updated}")
        
        return {
            "cleaned_samples": cleaned,
            "updated_mappings": updated
        }
        
    except Exception as e:
        log_error("快速维护失败", error=e)
        return {}

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Basket数据维护脚本')
    parser.add_argument('--mode', choices=['full', 'quick', 'report'], default='quick',
                       help='维护模式: full=完整维护, quick=快速维护, report=仅生成报告')
    
    args = parser.parse_args()
    
    try:
        # 初始化数据库连接
        await init_pool()
        log_success("数据库连接初始化成功")
        
        # 根据模式执行不同的维护任务
        if args.mode == 'full':
            result = await full_maintenance()
        elif args.mode == 'quick':
            result = await quick_maintenance()
        elif args.mode == 'report':
            result = await generate_maintenance_report()
        
        log_success(f"维护任务完成: {args.mode}")
        
    except Exception as e:
        log_error("维护任务执行失败", error=e)
    finally:
        # 关闭数据库连接
        await close_pool()
        log_info("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(main())
