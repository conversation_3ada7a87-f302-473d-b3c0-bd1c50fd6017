# 产品相似度业务服务环境变量配置
# 复制此文件为 .env 并修改相应配置

# ==================== 基础设施连接配置 ====================
# Consul服务发现（连接到主服务）
CONSUL_HOST=localhost
CONSUL_PORT=8500

# 如果主服务在其他主机，修改为实际IP地址
# CONSUL_HOST=*************

# ==================== 服务配置 ====================
SERVICE_NAME=product-similarity
SERVICE_PORT=8001
SERVICE_TAGS=api,product,similarity,business
ENVIRONMENT=production

# 服务器配置
HOST=0.0.0.0
PORT=8001
LOG_LEVEL=INFO

# ==================== 数据库配置 ====================
# 选项1：使用主服务的PostgreSQL
# PG_HOST=postgres
# PG_PORT=5432
# PG_USER=admin
# PG_PASSWORD=admin123
# PG_DB=microservices

# 选项2：使用外部数据库
PG_HOST=************
PG_PORT=5432
PG_USER=lens
PG_PASSWORD=Ls.3956573
PG_DB=lens

# 选项3：使用独立数据库（需要在docker-compose中启用）
# PG_HOST=product-similarity-postgres
# PG_PORT=5432
# PG_USER=similarity_user
# PG_PASSWORD=similarity_password
# PG_DB=product_similarity

# ==================== Redis配置 ====================
# 选项1：使用主服务的Redis
# REDIS_HOST=redis
# REDIS_PORT=6379
# REDIS_PASSWORD=redis123
# REDIS_DB=0

# 选项2：使用外部Redis
REDIS_HOST=************
REDIS_PORT=6379
REDIS_PASSWORD=ls3956573
REDIS_DB=5

# ==================== AI模型配置 ====================
# 方式1：使用TEXT_ENDPOINTS（推荐，支持多个端点）
TEXT_ENDPOINTS=[{"url":"http://************:3000","api_key":"sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK","model":"deepseek-ai/DeepSeek-V3","is_multimodal":false},{"url":"http://************:3000","api_key":"sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK","model":"deepseek-ai/DeepSeek-V3","is_multimodal":false}]

# 方式2：使用OPENAI_CREDENTIALS（备用）
# OPENAI_CREDENTIALS=[{"url":"http://your-ai-endpoint:3000","api_key":"your-api-key","model":"your-model","is_multimodal":false}]

# ==================== 业务配置 ====================
# 并发请求限制
MAX_CONCURRENT_REQUESTS=10

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# 缓存TTL（秒）
CACHE_TTL=3600

# AI调用超时时间（秒）
AI_TIMEOUT=120

# AI模型温度参数
AI_TEMPERATURE=0.1

# ==================== 性能配置 ====================
# 数据库连接池配置
DB_POOL_MIN_SIZE=5
DB_POOL_MAX_SIZE=20

# Redis连接池配置
REDIS_POOL_MAX_CONNECTIONS=20

# ==================== 监控配置 ====================
# 健康检查配置
HEALTH_CHECK_INTERVAL=15
HEALTH_CHECK_TIMEOUT=10

# 指标收集
ENABLE_METRICS=true
METRICS_PORT=9090

# ==================== 安全配置 ====================
# API认证（如果需要）
# API_KEY=your-api-key-here
# JWT_SECRET=your-jwt-secret-here

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FORMAT=json

# 日志文件配置（可选）
# LOG_FILE=/app/logs/product-similarity.log
# LOG_MAX_SIZE=100MB
# LOG_BACKUP_COUNT=5

# ==================== 开发配置 ====================
# 调试模式
DEBUG=false

# 热重载（开发环境）
HOT_RELOAD=false

# 测试模式
TEST_MODE=false

# ==================== 跨主机部署配置 ====================
# 当业务服务与主服务不在同一主机时，需要配置以下项：

# 主服务主机IP（替换为实际IP）
# INFRASTRUCTURE_HOST=*************

# 基础设施服务连接
# CONSUL_HOST=*************
# PG_HOST=*************
# REDIS_HOST=*************

# 网络配置
# NETWORK_NAME=microservices

# ==================== 扩展配置 ====================
# 自定义业务配置
# CUSTOM_CONFIG_1=value1
# CUSTOM_CONFIG_2=value2

# 第三方服务配置
# EXTERNAL_API_URL=https://api.example.com
# EXTERNAL_API_KEY=your-external-api-key

# ==================== 示例配置说明 ====================
#
# 1. 同主机部署（默认）：
#    - 使用默认的服务名称（consul, postgres, redis）
#    - 所有服务在同一个microservices网络中
#
# 2. 跨主机部署：
#    - 修改 CONSUL_HOST, PG_HOST, REDIS_HOST 为主服务IP
#    - 确保网络连通性和防火墙配置
#
# 3. 使用外部服务：
#    - 配置外部数据库和Redis连接信息
#    - 确保外部服务的访问权限
#
# 4. 开发环境：
#    - 设置 ENVIRONMENT=development
#    - 启用 DEBUG=true 和 HOT_RELOAD=true
#    - 降低 LOG_LEVEL=DEBUG
#
# 5. 生产环境：
#    - 设置 ENVIRONMENT=production
#    - 配置合适的资源限制和超时时间
#    - 启用监控和日志收集
