#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署后API接口测试脚本
测试所有非批量接口功能
"""

import requests
import json
import time
import random
from typing import Dict, Any, List
from test_product_ids import nm_ids

# 测试配置
# BASE_URL = "http://localhost:8000"  # 直接访问
BASE_URL = "http://localhost/api/product-similarity"  # 网关访问

# 请求超时时间
TIMEOUT = 30

def make_request(method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
    """发送HTTP请求"""
    url = f"{BASE_URL}{endpoint}"
    try:
        response = requests.request(method, url, timeout=TIMEOUT, **kwargs)
        print(f"[{method}] {url} -> {response.status_code}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            return {
                "status_code": response.status_code,
                "data": response.json(),
                "success": response.status_code < 400
            }
        else:
            return {
                "status_code": response.status_code,
                "data": response.text,
                "success": response.status_code < 400
            }
    except Exception as e:
        print(f"请求失败: {e}")
        return {
            "status_code": 0,
            "data": {"error": str(e)},
            "success": False
        }

def test_health_check():
    """测试健康检查"""
    print("\n=== 测试健康检查 ===")
    result = make_request("GET", "/health")
    if result["success"]:
        print("✅ 健康检查通过")
        print(f"响应: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
    else:
        print("❌ 健康检查失败")
        print(f"错误: {result['data']}")
    return result["success"]

def test_service_info():
    """测试服务信息"""
    print("\n=== 测试服务信息 ===")
    result = make_request("GET", "/info")
    if result["success"]:
        print("✅ 服务信息获取成功")
        print(f"响应: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
    else:
        print("❌ 服务信息获取失败")
        print(f"错误: {result['data']}")
    return result["success"]

def test_statistics():
    """测试统计信息"""
    print("\n=== 测试统计信息 ===")
    result = make_request("GET", "/stats")
    if result["success"]:
        print("✅ 统计信息获取成功")
        print(f"响应: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
    else:
        print("❌ 统计信息获取失败")
        print(f"错误: {result['data']}")
    return result["success"]

def test_root_endpoint():
    """测试根路径"""
    print("\n=== 测试根路径 ===")
    result = make_request("GET", "/")
    if result["success"]:
        print("✅ 根路径访问成功")
        print(f"响应: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
    else:
        print("❌ 根路径访问失败")
        print(f"错误: {result['data']}")
    return result["success"]

def test_get_product():
    """测试获取单个产品信息"""
    print("\n=== 测试获取单个产品信息 ===")
    
    # 使用真实产品ID
    product_id = int(nm_ids[0])  # 使用第一个产品ID
    print(f"测试产品ID: {product_id}")
    
    # 测试正常获取
    result = make_request("GET", f"/product/{product_id}")
    if result["success"]:
        print("✅ 产品信息获取成功")
        data = result['data']
        if 'data' in data:
            product_info = data['data']
            print(f"产品名称: {product_info.get('name', 'N/A')}")
            print(f"产品价格: {product_info.get('price', 'N/A')}")
            print(f"产品品牌: {product_info.get('brand', 'N/A')}")
        print(f"缓存状态: {data.get('cached', False)}")
    else:
        print("❌ 产品信息获取失败")
        print(f"错误: {result['data']}")
        return False
    
    # 测试强制刷新
    print(f"\n测试强制刷新产品ID: {product_id}")
    result = make_request("GET", f"/product/{product_id}?force_refresh=true")
    if result["success"]:
        print("✅ 强制刷新产品信息成功")
        data = result['data']
        print(f"缓存状态: {data.get('cached', False)}")
    else:
        print("❌ 强制刷新产品信息失败")
        print(f"错误: {result['data']}")
    
    return True

def test_compare_products():
    """测试产品相似度比较"""
    print("\n=== 测试产品相似度比较 ===")
    
    # 使用真实产品ID
    product_id1 = int(nm_ids[0])
    product_id2 = int(nm_ids[1])
    print(f"比较产品: {product_id1} vs {product_id2}")
    
    # 测试文本模式比较
    payload = {
        "product_id1": product_id1,
        "product_id2": product_id2,
        "mode": "text",
        "convert": False
    }
    
    result = make_request("POST", "/compare", json=payload)
    if result["success"]:
        print("✅ 产品比较成功")
        data = result['data']
        if 'data' in data:
            comparison = data['data']
            print(f"相似度分数: {comparison.get('similarity_score', 'N/A')}")
            print(f"比较模式: {comparison.get('mode', 'N/A')}")
            print(f"处理时间: {comparison.get('processing_time', 'N/A')}秒")
    else:
        print("❌ 产品比较失败")
        print(f"错误: {result['data']}")
        return False
    
    return True

def test_product_similarities():
    """测试获取产品相似度列表"""
    print("\n=== 测试获取产品相似度列表 ===")
    
    # 使用真实产品ID
    product_id = int(nm_ids[0])
    print(f"获取产品 {product_id} 的相似度列表")
    
    result = make_request("GET", f"/product/{product_id}/similarities?limit=5")
    if result["success"]:
        print("✅ 产品相似度列表获取成功")
        data = result['data']
        if 'data' in data and 'similarities' in data['data']:
            similarities = data['data']['similarities']
            print(f"找到 {len(similarities)} 个相似产品")
            for i, sim in enumerate(similarities[:3]):  # 只显示前3个
                print(f"  {i+1}. 产品ID: {sim.get('product_id2', 'N/A')}, 相似度: {sim.get('similarity_score', 'N/A')}")
    else:
        print("❌ 产品相似度列表获取失败")
        print(f"错误: {result['data']}")
        return False
    
    return True

def test_top_similarities():
    """测试获取高相似度产品对"""
    print("\n=== 测试获取高相似度产品对 ===")
    
    result = make_request("GET", "/similarities/top?limit=5")
    if result["success"]:
        print("✅ 高相似度产品对获取成功")
        data = result['data']
        if 'data' in data and 'similarities' in data['data']:
            similarities = data['data']['similarities']
            print(f"找到 {len(similarities)} 个高相似度产品对")
            for i, sim in enumerate(similarities[:3]):  # 只显示前3个
                print(f"  {i+1}. {sim.get('product_id1', 'N/A')} vs {sim.get('product_id2', 'N/A')}, 相似度: {sim.get('similarity_score', 'N/A')}")
    else:
        print("❌ 高相似度产品对获取失败")
        print(f"错误: {result['data']}")
        return False
    
    return True

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试不存在的产品ID
    print("测试不存在的产品ID...")
    result = make_request("GET", "/product/999999999")
    if result["status_code"] == 404:
        print("✅ 不存在产品ID的错误处理正确")
    else:
        print(f"❌ 不存在产品ID的错误处理异常: {result['status_code']}")
    
    # 测试无效的比较请求
    print("测试无效的比较请求...")
    payload = {
        "product_id1": 999999999,
        "product_id2": 999999998,
        "mode": "text",
        "convert": False
    }
    result = make_request("POST", "/compare", json=payload)
    if result["status_code"] >= 400:
        print("✅ 无效比较请求的错误处理正确")
    else:
        print(f"❌ 无效比较请求的错误处理异常: {result['status_code']}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试部署后的API接口")
    print(f"测试目标: {BASE_URL}")
    print("=" * 50)
    
    # 测试结果统计
    test_results = []
    
    # 基础功能测试
    test_results.append(("健康检查", test_health_check()))
    test_results.append(("根路径", test_root_endpoint()))
    test_results.append(("服务信息", test_service_info()))
    test_results.append(("统计信息", test_statistics()))
    
    # 产品信息测试
    test_results.append(("获取产品信息", test_get_product()))
    
    # 产品比较测试
    test_results.append(("产品相似度比较", test_compare_products()))
    
    # 相似度查询测试
    test_results.append(("产品相似度列表", test_product_similarities()))
    test_results.append(("高相似度产品对", test_top_similarities()))
    
    # 错误处理测试
    test_results.append(("错误处理", test_error_handling()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
    else:
        print(f"⚠️  有 {total - passed} 个测试失败")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
