#!/usr/bin/env python3
"""
检查数据库存储情况
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.product_similarity.db import init_pool, close_pool, get_pool


async def check_database_storage():
    """检查数据库存储情况"""
    print("🔍 检查数据库存储情况")
    print("=" * 60)
    
    try:
        await init_pool()
        pool = await get_pool()
        async with pool.acquire() as conn:
            
            # 1. 检查表是否存在
            print("1️⃣ 检查表是否存在...")
            table_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'pj_similar' 
                    AND table_name = 'product_analyze_similar_result'
                )
            """)
            print(f"  表是否存在: {table_exists}")
            
            if not table_exists:
                print("  ❌ 表不存在，需要创建表")
                return
            
            # 2. 检查表结构
            print("\n2️⃣ 检查表结构...")
            columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'pj_similar' 
                AND table_name = 'product_analyze_similar_result'
                ORDER BY ordinal_position
            """)
            
            print("  表结构:")
            for col in columns:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                print(f"    - {col['column_name']}: {col['data_type']} {nullable}{default}")
            
            # 3. 检查表中的数据
            print("\n3️⃣ 检查表中的数据...")
            count = await conn.fetchval('SELECT COUNT(*) FROM pj_similar.product_analyze_similar_result')
            print(f"  总记录数: {count}")
            
            if count > 0:
                # 查看最近的几条记录
                print("\n  最近的10条记录:")
                recent_records = await conn.fetch("""
                    SELECT keyword, target_product_id, avg_similarity, similar_count, 
                           competitor_count, valid_scores, created_at
                    FROM pj_similar.product_analyze_similar_result 
                    ORDER BY created_at DESC 
                    LIMIT 10
                """)
                
                for i, record in enumerate(recent_records, 1):
                    keyword = record['keyword']
                    target_id = record['target_product_id']
                    avg_sim = record['avg_similarity']
                    created_at = record['created_at']
                    print(f"    {i:2d}. '{keyword}' -> {target_id} | 相似度: {avg_sim}分 | 时间: {created_at}")
                
                # 按关键词统计
                print("\n  按关键词统计:")
                keyword_stats = await conn.fetch("""
                    SELECT keyword, COUNT(*) as count, MAX(created_at) as latest
                    FROM pj_similar.product_analyze_similar_result 
                    GROUP BY keyword 
                    ORDER BY count DESC, latest DESC
                    LIMIT 10
                """)
                
                for stat in keyword_stats:
                    print(f"    - '{stat['keyword']}': {stat['count']}次 | 最新: {stat['latest']}")
            else:
                print("  ⚠️ 表中没有数据")
            
            # 4. 检查今天的数据
            print("\n4️⃣ 检查今天的数据...")
            today_count = await conn.fetchval("""
                SELECT COUNT(*) 
                FROM pj_similar.product_analyze_similar_result 
                WHERE DATE(created_at) = CURRENT_DATE
            """)
            print(f"  今天新增记录数: {today_count}")
            
            if today_count > 0:
                today_records = await conn.fetch("""
                    SELECT keyword, target_product_id, avg_similarity, created_at
                    FROM pj_similar.product_analyze_similar_result 
                    WHERE DATE(created_at) = CURRENT_DATE
                    ORDER BY created_at DESC
                """)
                
                print("  今天的记录:")
                for record in today_records:
                    keyword = record['keyword']
                    target_id = record['target_product_id']
                    avg_sim = record['avg_similarity']
                    created_at = record['created_at']
                    print(f"    - '{keyword}' -> {target_id} | 相似度: {avg_sim}分 | 时间: {created_at}")
    
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await close_pool()
        print("\n🔒 数据库连接池已关闭")


if __name__ == "__main__":
    asyncio.run(check_database_storage())
