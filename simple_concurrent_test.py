#!/usr/bin/env python3
"""
使用test_keywords.py中的关键词进行持续并发测试
保持20个任务同时运行，任务结束后立即补充新任务
"""
import asyncio
import aiohttp
import time
import random
from typing import List, Dict, Any
import sys
sys.path.append('.')

# 导入关键词数据
from test_keywords import keyword_info

class SimpleConcurrentTester:
    def __init__(self, target_product_id: int = 253486273, max_concurrent: int = 20):
        self.target_product_id = target_product_id
        self.max_concurrent = max_concurrent
        self.base_url = "http://localhost:8001"
        
        # 统计数据
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.start_time = time.time()
        
        # 关键词队列
        self.keywords = [item["keyword"] for item in keyword_info]
        random.shuffle(self.keywords)
        self.current_index = 0
        
    def get_next_keyword(self) -> str:
        """获取下一个关键词，循环使用"""
        if self.current_index >= len(self.keywords):
            random.shuffle(self.keywords)
            self.current_index = 0
            print(f"🔄 关键词队列重新开始")
        
        keyword = self.keywords[self.current_index]
        self.current_index += 1
        return keyword
    
    async def test_single_keyword(self, session: aiohttp.ClientSession, keyword: str, task_id: int):
        """测试单个关键词"""
        start_time = time.time()
        
        try:
            payload = {
                "keyword": keyword,
                "target_product_id": self.target_product_id,
                "limit": 50
            }
            
            url = f"{self.base_url}/keyword-matching"
            
            async with session.post(url, json=payload, timeout=aiohttp.ClientTimeout(total=60)) as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    self.successful_requests += 1

                    # 正确解析API响应结构
                    if result.get("status") == "success" and "data" in result:
                        data = result["data"]
                        avg_similarity = data.get("avg_similarity", 0)
                        similar_count = data.get("similar_count", 0)
                        competitor_count = data.get("competitor_count", 0)
                        is_cached = data.get("from_cache", False)
                    else:
                        avg_similarity = 0
                        similar_count = 0
                        competitor_count = 0
                        is_cached = False

                    cache_indicator = "🟢缓存" if is_cached else "🔵新请求"
                    
                    print(f"✅ 任务{task_id:2d} | {keyword[:25]:25s} | {duration:5.2f}s | "
                          f"相似度:{avg_similarity:2.0f} | 相似:{similar_count:2d} | "
                          f"竞品:{competitor_count:2d} | {cache_indicator}")
                    
                    return True
                else:
                    error_text = await response.text()
                    self.failed_requests += 1
                    print(f"❌ 任务{task_id:2d} | {keyword[:25]:25s} | {duration:5.2f}s | "
                          f"HTTP {response.status}: {error_text[:50]}")
                    return False
                    
        except Exception as e:
            duration = time.time() - start_time
            self.failed_requests += 1
            print(f"❌ 任务{task_id:2d} | {keyword[:25]:25s} | {duration:5.2f}s | 异常: {str(e)[:50]}")
            return False
    
    async def worker_task(self, session: aiohttp.ClientSession, task_id: int):
        """工作任务"""
        while True:
            try:
                keyword = self.get_next_keyword()
                await self.test_single_keyword(session, keyword, task_id)
                self.total_requests += 1
                
                # 每20个请求打印统计
                if self.total_requests % 20 == 0:
                    elapsed = time.time() - self.start_time
                    success_rate = (self.successful_requests / self.total_requests) * 100
                    avg_rps = self.total_requests / elapsed
                    print(f"\n📊 统计 | 总请求:{self.total_requests} | 成功:{self.successful_requests} | "
                          f"失败:{self.failed_requests} | 成功率:{success_rate:.1f}% | "
                          f"平均RPS:{avg_rps:.2f}\n")
                
                # 短暂延迟
                await asyncio.sleep(0.5)
                
            except Exception as e:
                print(f"❌ 任务{task_id} 异常: {e}")
                await asyncio.sleep(2)
    
    async def run_test(self):
        """运行测试"""
        print(f"🚀 开始持续并发测试")
        print(f"📋 目标产品ID: {self.target_product_id}")
        print(f"🔢 并发任务数: {self.max_concurrent}")
        print(f"📚 关键词总数: {len(self.keywords)}")
        print(f"🌐 测试地址: {self.base_url}")
        print("=" * 100)
        print(f"{'任务':4s} | {'关键词':25s} | {'时间':5s} | {'相似度':4s} | {'相似':2s} | {'竞品':2s} | {'状态':6s}")
        print("-" * 100)
        
        connector = aiohttp.TCPConnector(limit=30, limit_per_host=25)
        timeout = aiohttp.ClientTimeout(total=60)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            tasks = []
            for i in range(self.max_concurrent):
                task = asyncio.create_task(self.worker_task(session, i + 1))
                tasks.append(task)
            
            try:
                await asyncio.gather(*tasks)
            except KeyboardInterrupt:
                print(f"\n\n🛑 收到中断信号，正在停止测试...")
                
                for task in tasks:
                    task.cancel()
                
                await asyncio.gather(*tasks, return_exceptions=True)
                
                elapsed = time.time() - self.start_time
                success_rate = (self.successful_requests / self.total_requests) * 100 if self.total_requests > 0 else 0
                avg_rps = self.total_requests / elapsed if elapsed > 0 else 0
                
                print(f"\n📊 最终统计:")
                print(f"   总请求数: {self.total_requests}")
                print(f"   成功请求: {self.successful_requests}")
                print(f"   失败请求: {self.failed_requests}")
                print(f"   成功率: {success_rate:.2f}%")
                print(f"   平均RPS: {avg_rps:.2f}")
                print(f"   总运行时间: {elapsed:.2f}秒")

async def main():
    print(f"✅ 成功加载 {len(keyword_info)} 个关键词")
    
    tester = SimpleConcurrentTester(
        target_product_id=253486273,
        max_concurrent=20
    )
    
    await tester.run_test()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
