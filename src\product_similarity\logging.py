import logging
import sys
from typing import Optional
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)

# 颜色常量
RED = Fore.RED
GREEN = Fore.GREEN
YELLOW = Fore.YELLOW
BLUE = Fore.BLUE
MAGENTA = Fore.MAGENTA
CYAN = Fore.CYAN
WHITE = Fore.WHITE
RESET = Style.RESET_ALL

class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""
    
    COLORS = {
        'DEBUG': CYAN,
        'INFO': GREEN,
        'WARNING': YELLOW,
        'ERROR': RED,
        'CRITICAL': RED + Style.BRIGHT,
    }
    
    def format(self, record):
        # 获取原始格式化结果
        log_message = super().format(record)
        
        # 添加颜色
        level_color = self.COLORS.get(record.levelname, WHITE)
        
        # 格式化时间和级别
        if hasattr(record, 'asctime'):
            # 如果有时间信息，给时间加上蓝色
            log_message = log_message.replace(
                record.asctime, 
                f"{BLUE}{record.asctime}{RESET}"
            )
        
        # 给级别名称加上颜色
        log_message = log_message.replace(
            record.levelname,
            f"{level_color}{record.levelname}{RESET}"
        )
        
        return log_message

def setup_logger(
    name: str = "product_similarity",
    level: str = "INFO",
    format_string: Optional[str] = None
) -> logging.Logger:
    """设置并返回配置好的logger"""
    
    if format_string is None:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 创建logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 避免重复添加handler
    if logger.handlers:
        return logger
    
    # 创建控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    
    # 创建格式化器
    formatter = ColoredFormatter(format_string)
    console_handler.setFormatter(formatter)
    
    # 添加handler到logger
    logger.addHandler(console_handler)
    
    return logger

# 创建默认logger实例
logger = setup_logger()

def get_logger(name: str, level: str = "INFO") -> logging.Logger:
    """获取指定名称的logger"""
    return setup_logger(name, level)

# 便捷的日志函数
def log_success(message: str, service_name: str = "product_similarity"):
    """记录成功日志"""
    logger.info(GREEN + f"✓ [{service_name}] {message}")

def log_error(message: str, service_name: str = "product_similarity", error: Optional[Exception] = None):
    """记录错误日志"""
    if error:
        logger.error(RED + f"✗ [{service_name}] {message}: {error}")
    else:
        logger.error(RED + f"✗ [{service_name}] {message}")

def log_warning(message: str, service_name: str = "product_similarity"):
    """记录警告日志"""
    logger.warning(YELLOW + f"⚠ [{service_name}] {message}")

def log_info(message: str, service_name: str = "product_similarity"):
    """记录信息日志"""
    logger.info(f"ℹ [{service_name}] {message}")

def log_debug(message: str, service_name: str = "product_similarity"):
    """记录调试日志"""
    logger.debug(CYAN + f"🔍 [{service_name}] {message}")

# 导出常用的颜色和logger
__all__ = [
    'logger', 'get_logger', 'setup_logger',
    'log_success', 'log_error', 'log_warning', 'log_info', 'log_debug',
    'RED', 'GREEN', 'YELLOW', 'BLUE', 'MAGENTA', 'CYAN', 'WHITE', 'RESET'
]
