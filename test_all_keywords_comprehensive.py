#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有关键词的匹配功能
使用test_keywords.py中的所有999个关键词与253486273进行匹配
保持20个任务同时进行并发，测试完所有关键词
"""

import asyncio
import aiohttp
import json
import time
import random
from datetime import datetime
from test_keywords import keyword_info

# 配置
API_BASE_URL = "http://localhost:8001"  # 直接访问业务服务
TARGET_PRODUCT_ID = 253486273
CONCURRENT_TASKS = 20  # 并发任务数量
TOTAL_KEYWORDS = len(keyword_info)  # 测试所有关键词

class AllKeywordsTestManager:
    def __init__(self):
        self.completed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.cache_hit_count = 0
        self.new_request_count = 0
        self.start_time = None
        self.results = []
        self.active_tasks = set()
        self.keyword_queue = asyncio.Queue()
        self.similarity_stats = {
            'high_similarity': 0,  # >70
            'medium_similarity': 0,  # 50-70
            'low_similarity': 0,  # <50
        }
        
    async def initialize_queue(self):
        """初始化关键词队列"""
        for i, kw_info in enumerate(keyword_info):
            await self.keyword_queue.put({
                'index': i + 1,
                'keyword': kw_info['keyword'],
                'count': kw_info.get('count', 0),
                'hasCluster': kw_info.get('hasCluster', False)
            })
        
    async def make_request(self, session: aiohttp.ClientSession, keyword_data: dict, task_id: int) -> dict:
        """发送关键词匹配请求"""
        url = f"{API_BASE_URL}/keyword-matching"
        data = {
            "keyword": keyword_data['keyword'],
            "target_product_id": TARGET_PRODUCT_ID
        }
        
        start_time = time.time()
        try:
            async with session.post(url, json=data, timeout=aiohttp.ClientTimeout(total=180)) as response:
                if response.status == 200:
                    result = await response.json()
                    duration = time.time() - start_time
                    
                    if result.get("status") == "success":
                        data_result = result.get("data", {})
                        return {
                            "task_id": task_id,
                            "keyword_index": keyword_data['index'],
                            "keyword": keyword_data['keyword'],
                            "keyword_count": keyword_data['count'],
                            "keyword_hasCluster": keyword_data['hasCluster'],
                            "status": "success",
                            "duration": duration,
                            "avg_similarity": data_result.get("avg_similarity", 0),
                            "similar_count": data_result.get("similar_count", 0),
                            "competitor_count": data_result.get("competitor_count", 0),
                            "valid_scores": data_result.get("valid_scores", 0),
                            "from_cache": data_result.get("from_cache", False),
                            "search_products_count": data_result.get("search_products_count", 0),
                            "compared_products_count": data_result.get("compared_products_count", 0),
                            "detailed_results_count": len(data_result.get("detailed_results", []))
                        }
                    else:
                        return {
                            "task_id": task_id,
                            "keyword_index": keyword_data['index'],
                            "keyword": keyword_data['keyword'],
                            "status": "api_error",
                            "duration": duration,
                            "error": result.get("message", "Unknown API error")
                        }
                else:
                    return {
                        "task_id": task_id,
                        "keyword_index": keyword_data['index'],
                        "keyword": keyword_data['keyword'],
                        "status": "http_error",
                        "duration": time.time() - start_time,
                        "error": f"HTTP {response.status}"
                    }
                    
        except asyncio.TimeoutError:
            return {
                "task_id": task_id,
                "keyword_index": keyword_data['index'],
                "keyword": keyword_data['keyword'],
                "status": "timeout",
                "duration": time.time() - start_time,
                "error": "Request timeout"
            }
        except Exception as e:
            return {
                "task_id": task_id,
                "keyword_index": keyword_data['index'],
                "keyword": keyword_data['keyword'],
                "status": "exception",
                "duration": time.time() - start_time,
                "error": str(e)
            }
    
    def update_similarity_stats(self, similarity: int):
        """更新相似度统计"""
        if similarity > 70:
            self.similarity_stats['high_similarity'] += 1
        elif similarity >= 50:
            self.similarity_stats['medium_similarity'] += 1
        else:
            self.similarity_stats['low_similarity'] += 1
    
    def print_progress(self):
        """打印进度信息"""
        if self.start_time:
            elapsed = time.time() - self.start_time
            rate = self.completed_count / elapsed if elapsed > 0 else 0
            progress_percent = (self.completed_count / TOTAL_KEYWORDS * 100) if TOTAL_KEYWORDS > 0 else 0
            
            print(f"\r进度: {self.completed_count}/{TOTAL_KEYWORDS} ({progress_percent:.1f}%) | "
                  f"成功: {self.success_count} | "
                  f"失败: {self.error_count} | "
                  f"缓存: {self.cache_hit_count} | "
                  f"新请求: {self.new_request_count} | "
                  f"活跃任务: {len(self.active_tasks)} | "
                  f"速率: {rate:.2f}/s", end="", flush=True)
    
    async def worker_task(self, session: aiohttp.ClientSession, task_id: int):
        """工作任务"""
        while True:
            try:
                # 从队列获取关键词
                keyword_data = await asyncio.wait_for(self.keyword_queue.get(), timeout=1.0)
            except asyncio.TimeoutError:
                # 队列为空，退出
                break
            
            # 执行请求
            result = await self.make_request(session, keyword_data, task_id)
            
            # 更新统计
            self.completed_count += 1
            self.results.append(result)
            
            if result["status"] == "success":
                self.success_count += 1
                if result.get("from_cache"):
                    self.cache_hit_count += 1
                else:
                    self.new_request_count += 1
                
                # 更新相似度统计
                self.update_similarity_stats(result['avg_similarity'])
                
                # 打印成功结果
                cache_status = "🟢缓存" if result.get("from_cache") else "🔵新请求"
                print(f"\n[任务{task_id:02d}] ✅ [{result['keyword_index']:3d}] {result['keyword'][:25]}... | "
                      f"相似度:{result['avg_similarity']:2d} | "
                      f"相似:{result['similar_count']:2d} | "
                      f"竞品:{result['competitor_count']:2d} | "
                      f"搜索量:{result['keyword_count']:5d} | "
                      f"{cache_status} | "
                      f"{result['duration']:.2f}s")
            else:
                self.error_count += 1
                print(f"\n[任务{task_id:02d}] ❌ [{result['keyword_index']:3d}] {result['keyword'][:25]}... | "
                      f"错误: {result.get('error', 'Unknown')[:30]} | "
                      f"{result['duration']:.2f}s")
            
            self.print_progress()
            
            # 标记任务完成
            self.keyword_queue.task_done()
    
    async def run_test(self):
        """运行全关键词测试"""
        print(f"开始全关键词测试:")
        print(f"目标产品ID: {TARGET_PRODUCT_ID}")
        print(f"并发任务数: {CONCURRENT_TASKS}")
        print(f"总关键词数量: {TOTAL_KEYWORDS}")
        print("=" * 100)
        
        # 初始化关键词队列
        await self.initialize_queue()
        
        self.start_time = time.time()
        
        # 创建HTTP会话
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)
        async with aiohttp.ClientSession(connector=connector) as session:
            # 创建并发任务
            tasks = []
            for i in range(CONCURRENT_TASKS):
                task = asyncio.create_task(self.worker_task(session, i + 1))
                tasks.append(task)
                self.active_tasks.add(task)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks, return_exceptions=True)
        
        # 计算最终统计
        total_time = time.time() - self.start_time
        success_rate = (self.success_count / self.completed_count * 100) if self.completed_count > 0 else 0
        cache_hit_rate = (self.cache_hit_count / self.success_count * 100) if self.success_count > 0 else 0
        new_request_rate = (self.new_request_count / self.success_count * 100) if self.success_count > 0 else 0
        avg_duration = sum(r.get('duration', 0) for r in self.results) / len(self.results) if self.results else 0
        
        print(f"\n\n" + "=" * 100)
        print("🎉 全关键词测试完成!")
        print("=" * 100)
        print(f"总耗时: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
        print(f"完成任务: {self.completed_count}/{TOTAL_KEYWORDS}")
        print(f"成功率: {success_rate:.1f}% ({self.success_count}/{self.completed_count})")
        print(f"失败数: {self.error_count}")
        print(f"缓存命中率: {cache_hit_rate:.1f}% ({self.cache_hit_count}/{self.success_count})")
        print(f"新请求率: {new_request_rate:.1f}% ({self.new_request_count}/{self.success_count})")
        print(f"平均响应时间: {avg_duration:.2f}秒")
        print(f"整体吞吐量: {self.completed_count/total_time:.2f} 请求/秒")
        
        # 相似度分布分析
        print(f"\n📊 相似度分布分析:")
        total_success = self.success_count
        if total_success > 0:
            print(f"  高相似度 (>70): {self.similarity_stats['high_similarity']} ({self.similarity_stats['high_similarity']/total_success*100:.1f}%)")
            print(f"  中等相似度 (50-70): {self.similarity_stats['medium_similarity']} ({self.similarity_stats['medium_similarity']/total_success*100:.1f}%)")
            print(f"  低相似度 (<50): {self.similarity_stats['low_similarity']} ({self.similarity_stats['low_similarity']/total_success*100:.1f}%)")
        
        # 分析错误类型
        error_types = {}
        for result in self.results:
            if result["status"] != "success":
                error_type = result["status"]
                error_types[error_type] = error_types.get(error_type, 0) + 1
        
        if error_types:
            print(f"\n❌ 错误类型分析:")
            for error_type, count in error_types.items():
                print(f"  {error_type}: {count}")
        
        # 显示相似度统计
        similarities = [r.get('avg_similarity', 0) for r in self.results if r["status"] == "success"]
        if similarities:
            print(f"\n📈 相似度统计:")
            print(f"  最高相似度: {max(similarities)}")
            print(f"  最低相似度: {min(similarities)}")
            print(f"  平均相似度: {sum(similarities)/len(similarities):.1f}")
            print(f"  中位数相似度: {sorted(similarities)[len(similarities)//2]}")
        
        # 显示前10个最佳结果
        successful_results = [r for r in self.results if r["status"] == "success"]
        if successful_results:
            top_results = sorted(successful_results, key=lambda x: x.get('avg_similarity', 0), reverse=True)[:10]
            print(f"\n🏆 前10个最佳匹配:")
            for i, result in enumerate(top_results, 1):
                cache_status = "缓存" if result.get("from_cache") else "新请求"
                print(f"  {i:2d}. [{result['keyword_index']:3d}] {result['keyword'][:40]}... | "
                      f"相似度:{result['avg_similarity']:2d} | "
                      f"相似:{result['similar_count']:2d} | "
                      f"竞品:{result['competitor_count']:2d} | "
                      f"搜索量:{result['keyword_count']:5d} | "
                      f"{cache_status}")
        
        # 显示搜索量最高的匹配结果
        if successful_results:
            high_volume_results = sorted(successful_results, key=lambda x: x.get('keyword_count', 0), reverse=True)[:5]
            print(f"\n🔥 搜索量最高的5个匹配:")
            for i, result in enumerate(high_volume_results, 1):
                cache_status = "缓存" if result.get("from_cache") else "新请求"
                print(f"  {i}. [{result['keyword_index']:3d}] {result['keyword'][:40]}... | "
                      f"搜索量:{result['keyword_count']:5d} | "
                      f"相似度:{result['avg_similarity']:2d} | "
                      f"{cache_status}")

async def main():
    """主函数"""
    if not keyword_info:
        print("❌ 错误: test_keywords.py 中没有找到关键词数据")
        return
    
    print(f"📋 加载了 {len(keyword_info)} 个关键词")
    print("🚀 开始全关键词测试...")
    
    manager = AllKeywordsTestManager()
    await manager.run_test()

if __name__ == "__main__":
    asyncio.run(main())
