#!/usr/bin/env python3
"""
关键词匹配详细测试 - 显示每个产品的相似度对比
"""
import asyncio
import time
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.product_similarity.db import init_pool, close_pool

# 测试关键词列表（前5个）
KEYWORDS = [
    "люстра потолочная для кухни современная",
    "лампы светодиодные для кухни", 
    "люстра светодиодная на потолок",
    "люстры",
    "люстра на кухню"
]

# 目标产品ID
TARGET_PRODUCT_ID = 253486273


async def test_keyword_with_details(keyword: str, target_id: int):
    """测试单个关键词并显示详细的相似度对比"""
    print(f"🔍 开始测试关键词: '{keyword}'")
    print(f"🎯 目标产品ID: {target_id}")
    print("-" * 60)
    
    try:
        # 导入需要的函数
        from src.product_similarity.services.wildberries_search import search_wildberries, extract_product_ids_from_search
        from src.product_similarity.services.similarity import compare_products_by_ids
        
        # 1. 搜索产品
        print("📦 正在搜索产品...")
        search_result = await search_wildberries(keyword)
        if not search_result:
            print("❌ 搜索失败")
            return
        
        # 2. 提取产品ID
        product_ids = extract_product_ids_from_search(search_result, limit=50)
        print(f"✅ 找到 {len(product_ids)} 个产品")
        
        # 3. 过滤掉目标产品ID
        comparison_ids = [pid for pid in product_ids if pid != target_id]
        print(f"🔄 将与 {len(comparison_ids)} 个产品进行相似度对比")
        print()
        
        # 4. 逐个进行相似度对比
        similarity_scores = []
        similar_products = []  # >65分
        competitor_products = []  # >80分
        
        print("📊 详细相似度对比结果:")
        print("=" * 60)
        
        for i, product_id in enumerate(comparison_ids, 1):
            try:
                comparison_result = await compare_products_by_ids(target_id, product_id)
                if comparison_result and 'similar_scores' in comparison_result:
                    score = comparison_result.get('similar_scores', 0)
                    similarity_scores.append(score)
                    
                    # 分类产品
                    if score >= 80:
                        status_icon = "🔥"
                        status_text = "竞品"
                        competitor_products.append((product_id, score))
                    elif score >= 65:
                        status_icon = "✅"
                        status_text = "相似"
                        similar_products.append((product_id, score))
                    else:
                        status_icon = "⚪"
                        status_text = "普通"
                    
                    print(f"{i:2d}. {status_icon} 产品ID: {product_id:>10} -> 相似度: {score:>3}分 ({status_text})")
                else:
                    print(f"{i:2d}. ❌ 产品ID: {product_id:>10} -> 对比失败")
            except Exception as e:
                print(f"{i:2d}. ❌ 产品ID: {product_id:>10} -> 错误: {str(e)}")
        
        print("=" * 60)
        
        # 5. 统计结果
        if similarity_scores:
            avg_similarity = sum(similarity_scores) / len(similarity_scores)
            
            print(f"📈 统计结果:")
            print(f"  - 平均相似度: {avg_similarity:.1f}分")
            print(f"  - 相似产品(>65分): {len(similar_products)}个")
            print(f"  - 竞品产品(>80分): {len(competitor_products)}个")
            print(f"  - 有效对比数: {len(similarity_scores)}个")
            
            # 显示高相似度产品
            if competitor_products:
                print(f"\n🔥 竞品产品 (>80分):")
                for product_id, score in sorted(competitor_products, key=lambda x: x[1], reverse=True):
                    print(f"  - 产品ID: {product_id} -> {score}分")
            
            if similar_products:
                print(f"\n✅ 相似产品 (65-80分):")
                for product_id, score in sorted(similar_products, key=lambda x: x[1], reverse=True):
                    print(f"  - 产品ID: {product_id} -> {score}分")
        else:
            print("❌ 没有有效的相似度对比结果")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def run_detailed_test():
    """运行详细测试"""
    print("🚀 开始关键词匹配详细测试")
    print(f"📊 测试关键词数量: {len(KEYWORDS)}")
    print(f"🎯 目标产品ID: {TARGET_PRODUCT_ID}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 初始化数据库连接池
        await init_pool()
        print("✅ 数据库连接池初始化完成\n")
        
        # 逐个测试关键词
        for i, keyword in enumerate(KEYWORDS, 1):
            print(f"\n{'='*80}")
            print(f"[{i}/{len(KEYWORDS)}] 关键词测试")
            print(f"{'='*80}")
            
            await test_keyword_with_details(keyword, TARGET_PRODUCT_ID)
            
            if i < len(KEYWORDS):
                print(f"\n⏳ 等待3秒后继续下一个关键词...")
                await asyncio.sleep(3)
        
        print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 详细测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("🔒 数据库连接池已关闭")


if __name__ == "__main__":
    asyncio.run(run_detailed_test())
