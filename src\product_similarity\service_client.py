"""
微服务调用客户端
用于调用其他微服务
"""
import asyncio
import random
from typing import Dict, Any, List, Optional, Union
import aiohttp

from .consul_service import consul_service
from .logging import log_success, log_error, log_warning, log_debug

class ServiceClient:
    """微服务调用客户端"""
    
    def __init__(self):
        self.consul_service = consul_service
        self.session: Optional[aiohttp.ClientSession] = None
        self._service_cache: Dict[str, List[Dict[str, Any]]] = {}
        self._cache_ttl = 30  # 服务发现缓存TTL（秒）
        self._last_discovery: Dict[str, float] = {}
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """关闭HTTP会话"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def _discover_service_instances(self, service_name: str, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """发现服务实例（带缓存）"""
        current_time = asyncio.get_event_loop().time()
        
        # 检查缓存是否有效
        if not force_refresh and service_name in self._service_cache:
            last_discovery = self._last_discovery.get(service_name, 0)
            if current_time - last_discovery < self._cache_ttl:
                return self._service_cache[service_name]
        
        # 从Consul发现服务
        instances = await self.consul_service.discover_service(service_name)
        
        if instances:
            self._service_cache[service_name] = instances
            self._last_discovery[service_name] = current_time
            log_debug(f"发现服务实例: {service_name}, 数量: {len(instances)}")
        else:
            log_warning(f"未发现服务实例: {service_name}")
        
        return instances
    
    def _select_instance(self, instances: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """选择服务实例（随机负载均衡）"""
        if not instances:
            return None
        return random.choice(instances)
    
    async def call_service(
        self,
        service_name: str,
        endpoint: str,
        method: str = "GET",
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None,
        retries: int = 3
    ) -> Optional[Dict[str, Any]]:
        """
        调用微服务
        
        Args:
            service_name: 服务名称
            endpoint: API端点
            method: HTTP方法
            data: 请求体数据
            params: 查询参数
            headers: 请求头
            timeout: 超时时间
            retries: 重试次数
            
        Returns:
            响应数据或None
        """
        for attempt in range(retries + 1):
            try:
                # 发现服务实例
                instances = await self._discover_service_instances(
                    service_name, 
                    force_refresh=(attempt > 0)
                )
                
                if not instances:
                    log_error(f"未找到服务实例: {service_name}")
                    return None
                
                # 选择实例
                instance = self._select_instance(instances)
                if not instance:
                    log_error(f"无法选择服务实例: {service_name}")
                    return None
                
                # 构建请求URL
                base_url = f"http://{instance['address']}:{instance['port']}"
                url = f"{base_url}{endpoint}"
                
                # 准备请求参数
                session = await self._get_session()
                request_kwargs = {
                    "url": url,
                    "method": method.upper(),
                    "params": params,
                    "headers": headers or {}
                }
                
                if data is not None:
                    if method.upper() in ["POST", "PUT", "PATCH"]:
                        request_kwargs["json"] = data
                    else:
                        request_kwargs["params"] = {**(params or {}), **data}
                
                if timeout:
                    request_kwargs["timeout"] = aiohttp.ClientTimeout(total=timeout)
                
                # 发送请求
                async with session.request(**request_kwargs) as response:
                    if response.status == 200:
                        result = await response.json()
                        log_debug(f"服务调用成功: {service_name}{endpoint}")
                        return result
                    elif response.status in [503, 502, 504]:
                        # 服务不可用，尝试重试
                        error_text = await response.text()
                        log_warning(f"服务暂时不可用: {service_name}, 状态: {response.status}, 重试: {attempt + 1}")
                        if attempt < retries:
                            await asyncio.sleep(1 * (attempt + 1))  # 指数退避
                            continue
                    else:
                        error_text = await response.text()
                        log_error(f"服务调用失败: {service_name}{endpoint}, 状态: {response.status}, 响应: {error_text}")
                        return None
                        
            except asyncio.TimeoutError:
                log_warning(f"服务调用超时: {service_name}{endpoint}, 重试: {attempt + 1}")
                if attempt < retries:
                    await asyncio.sleep(1 * (attempt + 1))
                    continue
            except Exception as e:
                log_error(f"服务调用异常: {service_name}{endpoint}", error=e)
                if attempt < retries:
                    await asyncio.sleep(1 * (attempt + 1))
                    continue
        
        log_error(f"服务调用最终失败: {service_name}{endpoint}")
        return None
    
    # ==================== 便捷方法 ====================
    
    async def get(
        self,
        service_name: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> Optional[Dict[str, Any]]:
        """GET请求"""
        return await self.call_service(
            service_name, endpoint, "GET", 
            params=params, headers=headers, timeout=timeout
        )
    
    async def post(
        self,
        service_name: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> Optional[Dict[str, Any]]:
        """POST请求"""
        return await self.call_service(
            service_name, endpoint, "POST", 
            data=data, headers=headers, timeout=timeout
        )
    
    async def put(
        self,
        service_name: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> Optional[Dict[str, Any]]:
        """PUT请求"""
        return await self.call_service(
            service_name, endpoint, "PUT", 
            data=data, headers=headers, timeout=timeout
        )
    
    async def delete(
        self,
        service_name: str,
        endpoint: str,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> Optional[Dict[str, Any]]:
        """DELETE请求"""
        return await self.call_service(
            service_name, endpoint, "DELETE", 
            headers=headers, timeout=timeout
        )
    
    # ==================== 特定服务的便捷方法 ====================
    
    async def get_user_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """调用用户服务获取用户信息"""
        return await self.get(
            service_name="user-service",
            endpoint=f"/users/{user_id}"
        )
    
    async def create_order(self, order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用订单服务创建订单"""
        return await self.post(
            service_name="order-service",
            endpoint="/orders",
            data=order_data
        )
    
    async def get_product_info(self, product_id: int) -> Optional[Dict[str, Any]]:
        """调用产品服务获取产品信息"""
        return await self.get(
            service_name="product-service",
            endpoint=f"/products/{product_id}"
        )
    
    async def compare_products(
        self, 
        product_id1: int, 
        product_id2: int, 
        mode: str = "text"
    ) -> Optional[Dict[str, Any]]:
        """调用产品相似度服务比较产品"""
        return await self.post(
            service_name="product-similarity",
            endpoint="/compare",
            data={
                "product_id1": product_id1,
                "product_id2": product_id2,
                "mode": mode
            }
        )
    
    async def health_check_service(self, service_name: str) -> Optional[Dict[str, Any]]:
        """检查指定服务的健康状态"""
        return await self.get(
            service_name=service_name,
            endpoint="/health",
            timeout=5
        )
    
    async def get_service_info(self, service_name: str) -> Optional[Dict[str, Any]]:
        """获取指定服务的信息"""
        return await self.get(
            service_name=service_name,
            endpoint="/info",
            timeout=5
        )

# 创建全局服务客户端实例
service_client = ServiceClient()
