#!/usr/bin/env python3
"""
直接检查数据库中的缓存数据
"""
import asyncio
import sys
sys.path.append('.')

from src.product_similarity.db import get_pool
from src.product_similarity.utils import generate_cache_key

async def check_db_cache():
    """检查数据库中的缓存数据"""
    
    print("🔍 检查数据库中的缓存数据")
    print("=" * 50)
    
    keyword = "люстра на потолок"
    cache_key = generate_cache_key("wb_search", keyword)
    
    print(f"缓存键: {cache_key}")
    
    try:
        pool = await get_pool()
        async with pool.acquire() as conn:
            
            # 查询缓存数据
            row = await conn.fetchrow("""
                SELECT 
                    cache_key,
                    cache_value,
                    jsonb_typeof(cache_value) as value_type,
                    length(cache_value::text) as value_length,
                    created_at,
                    expires_at
                FROM pj_similar.cache 
                WHERE cache_key = $1
            """, cache_key)
            
            if row:
                print(f"✅ 找到缓存数据:")
                print(f"  键: {row['cache_key']}")
                print(f"  值类型: {row['value_type']}")
                print(f"  值长度: {row['value_length']}")
                print(f"  创建时间: {row['created_at']}")
                print(f"  过期时间: {row['expires_at']}")
                
                # 获取实际的cache_value
                cache_value = row['cache_value']
                print(f"  Python中的类型: {type(cache_value)}")
                
                if isinstance(cache_value, str):
                    print(f"  前100个字符: {cache_value[:100]}")
                elif isinstance(cache_value, dict):
                    print(f"  字典键: {list(cache_value.keys())}")
                else:
                    print(f"  值: {cache_value}")
                    
                # 检查所有缓存数据的类型分布
                print("\n📊 所有缓存数据类型分布:")
                type_stats = await conn.fetch("""
                    SELECT 
                        jsonb_typeof(cache_value) as value_type,
                        COUNT(*) as count
                    FROM pj_similar.cache 
                    GROUP BY jsonb_typeof(cache_value)
                    ORDER BY count DESC
                """)
                
                for stat in type_stats:
                    print(f"  {stat['value_type']}: {stat['count']} 条")
                    
            else:
                print("❌ 没有找到缓存数据")
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_db_cache())
