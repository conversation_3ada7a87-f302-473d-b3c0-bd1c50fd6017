#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示完整的产品信息结构
"""

import asyncio
import json
from src.product_similarity.services.product import get_product_detail
from src.product_similarity.db import init_pool, close_pool
from test_product_ids import nm_ids

async def show_complete_product_info():
    """显示完整的产品信息结构"""
    try:
        # 初始化数据库连接池
        await init_pool()
        print("数据库连接池初始化完成")
        
        # 使用真实的产品ID进行测试
        test_product_id = int(nm_ids[0])  # 使用第一个真实产品ID
        
        print(f"获取产品信息: {test_product_id}")
        print("=" * 80)
        
        # 获取产品信息（包含图片描述）
        product_info = await get_product_detail(test_product_id, force_refresh=True)
        
        if product_info:
            print("✅ 产品信息获取成功!")
            print("=" * 80)
            print("完整产品信息结构:")
            print("=" * 80)
            
            # 输出完整的JSON结构
            print(json.dumps(product_info, ensure_ascii=False, indent=2))
            
        else:
            print("❌ 产品信息获取失败!")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接池
        await close_pool()
        print("\n" + "=" * 80)
        print("数据库连接池已关闭")

if __name__ == "__main__":
    asyncio.run(show_complete_product_info())
