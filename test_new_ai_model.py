#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新AI模型(kimi-k2-0711-preview)的keywordMatching功能
"""

import asyncio
import aiohttp
import json
import time

# 测试配置
SERVICE_URL = "http://localhost:8001"

async def test_new_ai_model():
    """测试新AI模型的keywordMatching功能"""
    print("🤖 测试新AI模型 (kimi-k2-0711-preview)")
    print("=" * 60)
    
    # 测试用例：使用一个新的关键词，确保不会命中缓存
    test_cases = [
        {
            "keyword": "современная люстра для гостиной",
            "target_product_id": 253486273,
            "description": "现代客厅吊灯"
        },
        {
            "keyword": "светодиодный потолочный светильник квадратный",
            "target_product_id": 316527894,
            "description": "方形LED吸顶灯"
        },
        {
            "keyword": "настольная лампа с регулировкой яркости",
            "target_product_id": 253486274,
            "description": "可调亮度台灯"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}/3: {test_case['description']}")
        print(f"关键词: {test_case['keyword']}")
        print(f"目标产品ID: {test_case['target_product_id']}")
        
        try:
            start_time = time.time()
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{SERVICE_URL}/keyword-matching",
                    json={
                        "keyword": test_case["keyword"],
                        "target_product_id": test_case["target_product_id"]
                    },
                    timeout=aiohttp.ClientTimeout(total=600)  # 10分钟超时
                ) as response:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        result = data.get('data', {})
                        
                        print(f"✅ 测试成功:")
                        print(f"   • 相似度: {result.get('avg_similarity', 'N/A')}")
                        print(f"   • 相似产品数: {result.get('similar_count', 'N/A')}")
                        print(f"   • 竞争产品数: {result.get('competitor_count', 'N/A')}")
                        print(f"   • 有效评分数: {result.get('valid_scores', 'N/A')}")
                        print(f"   • 处理时间: {processing_time:.2f}秒")
                        print(f"   • 缓存状态: {result.get('from_cache', 'N/A')}")
                        
                        # 判断是否使用了新的AI模型（非缓存结果通常处理时间较长）
                        if not result.get('from_cache', True):
                            print(f"   🤖 使用了新AI模型进行分析")
                        else:
                            print(f"   💾 使用了缓存结果")
                        
                        results.append({
                            'success': True,
                            'similarity': result.get('avg_similarity', 0),
                            'processing_time': processing_time,
                            'from_cache': result.get('from_cache', True)
                        })
                        
                    else:
                        error_text = await response.text()
                        print(f"❌ 测试失败: {response.status}")
                        print(f"   错误信息: {error_text[:200]}")
                        results.append({
                            'success': False,
                            'error': f"HTTP {response.status}",
                            'processing_time': processing_time
                        })
                        
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append({
                'success': False,
                'error': str(e),
                'processing_time': 0
            })
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r['success'])
    total_tests = len(results)
    
    print(f"📈 成功率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count > 0:
        successful_results = [r for r in results if r['success']]
        avg_similarity = sum(r.get('similarity', 0) for r in successful_results) / len(successful_results)
        avg_time = sum(r['processing_time'] for r in successful_results) / len(successful_results)
        cache_hits = sum(1 for r in successful_results if r.get('from_cache', True))
        
        print(f"📊 平均相似度: {avg_similarity:.1f}")
        print(f"⏱️ 平均处理时间: {avg_time:.2f}秒")
        print(f"💾 缓存命中: {cache_hits}/{len(successful_results)} ({cache_hits/len(successful_results)*100:.1f}%)")
        
        # 检查是否有使用新AI模型的测试
        new_ai_tests = [r for r in successful_results if not r.get('from_cache', True)]
        if new_ai_tests:
            print(f"🤖 新AI模型测试: {len(new_ai_tests)}次")
            avg_new_ai_time = sum(r['processing_time'] for r in new_ai_tests) / len(new_ai_tests)
            print(f"🤖 新AI模型平均处理时间: {avg_new_ai_time:.2f}秒")
        else:
            print("💾 所有测试都命中了缓存，未使用新AI模型")
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过！新AI模型部署成功！")
    elif success_count > 0:
        print(f"\n⚠️ 部分测试通过，成功率: {success_count/total_tests*100:.1f}%")
    else:
        print("\n🚨 所有测试失败，请检查服务状态")

if __name__ == "__main__":
    asyncio.run(test_new_ai_model())
