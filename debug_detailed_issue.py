#!/usr/bin/env python3
"""
详细调试缓存和搜索问题
"""
import asyncio
import sys
sys.path.append('.')

from src.product_similarity.services.wildberries_search import search_wildberries, extract_product_ids_from_search
from src.product_similarity.utils import generate_cache_key
from src.product_similarity.crud import get_cache

async def debug_detailed_issue():
    """详细调试问题"""
    
    print("🔍 详细调试缓存和搜索问题")
    print("=" * 60)
    
    # 测试关键词
    keyword = "люстра на потолок"
    
    print(f"测试关键词: {keyword}")
    print()
    
    # 1. 检查缓存键
    cache_key = generate_cache_key("wb_search", keyword)
    print(f"1️⃣ 缓存键: {cache_key}")
    
    # 2. 直接从数据库获取缓存
    print("2️⃣ 直接从数据库获取缓存:")
    try:
        cached_data = await get_cache(cache_key)
        if cached_data:
            print(f"   ✅ 缓存数据类型: {type(cached_data)}")
            if isinstance(cached_data, dict):
                print(f"   ✅ 字典键: {list(cached_data.keys())}")
                if 'data' in cached_data:
                    data = cached_data['data']
                    print(f"   ✅ data类型: {type(data)}")
                    if isinstance(data, dict) and 'products' in data:
                        products = data['products']
                        print(f"   ✅ products类型: {type(products)}")
                        print(f"   ✅ products数量: {len(products)}")
                    else:
                        print(f"   ❌ data内容: {data}")
                else:
                    print(f"   ❌ 缓存数据内容: {cached_data}")
            else:
                print(f"   ❌ 缓存数据内容: {cached_data[:200]}...")
        else:
            print("   ❌ 没有缓存数据")
    except Exception as e:
        print(f"   ❌ 获取缓存失败: {e}")
    
    print()
    
    # 3. 调用search_wildberries函数
    print("3️⃣ 调用search_wildberries函数:")
    try:
        search_result = await search_wildberries(keyword)
        if search_result:
            print(f"   ✅ 搜索结果类型: {type(search_result)}")
            if isinstance(search_result, dict):
                print(f"   ✅ 搜索结果键: {list(search_result.keys())}")
                if 'data' in search_result:
                    data = search_result['data']
                    print(f"   ✅ data类型: {type(data)}")
                    if isinstance(data, dict) and 'products' in data:
                        products = data['products']
                        print(f"   ✅ products类型: {type(products)}")
                        print(f"   ✅ products数量: {len(products)}")
                    else:
                        print(f"   ❌ data内容: {data}")
                else:
                    print(f"   ❌ 搜索结果内容: {search_result}")
            else:
                print(f"   ❌ 搜索结果内容: {search_result[:200]}...")
        else:
            print("   ❌ 搜索失败")
    except Exception as e:
        print(f"   ❌ 搜索异常: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # 4. 测试extract_product_ids_from_search函数
    print("4️⃣ 测试extract_product_ids_from_search函数:")
    try:
        if 'search_result' in locals() and search_result:
            print(f"   输入数据类型: {type(search_result)}")
            product_ids = extract_product_ids_from_search(search_result, limit=10)
            print(f"   ✅ 提取的产品ID: {product_ids[:5]}... (共{len(product_ids)}个)")
        else:
            print("   ❌ 没有搜索结果可供测试")
    except Exception as e:
        print(f"   ❌ 提取产品ID失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_detailed_issue())
