"""
Wildberries 搜索服务
提供 Wildberries 商品搜索功能
"""
import asyncio
import json
from typing import Dict, Any, List, Optional
from urllib.parse import quote
import httpx

from ..logging import log_success, log_error, log_warning, log_debug
from ..crud import get_cache, set_cache
from ..utils import generate_cache_key


async def search_wildberries(keyword: str) -> Optional[Dict[str, Any]]:
    """搜索Wildberries商品
    
    参数:
        keyword: 搜索关键词（例如："浴室壁灯"）
        
    返回:
        包含搜索结果的字典（失败时返回None）
        数据结构示例:
        {
            "metadata": {
                "name": "搜索关键词",
                "catalog_type": "类目类型",
                ...
            },
            "data": {
                "products": [商品列表],
                "total": 总商品数
            },
            ...
        }
    """
    # 生成缓存键
    cache_key = generate_cache_key("wb_search", keyword)
    
    # 先检查缓存（2天TTL）
    cached_result = await get_cache(cache_key)
    if cached_result:
        log_debug(f"从缓存获取搜索结果: {keyword}")
        return cached_result
    
    # 固定请求头
    headers = {
        "accept": "application/json",  # 声明接受JSON格式响应
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",  # 固定用户代理
        "referer": f"https://www.wildberries.ru/catalog/0/search.aspx?search={quote(keyword)}"  # 来源页（包含URL编码后的关键词）
    }
    
    # 搜索参数
    params = {
        "ab_testing": "false",  # 禁用A/B测试
        "appType": "64",  # 应用类型标识
        "curr": "rub",  # 货币类型（卢布）
        "dest": "-1257786",  # 目标区域ID
        "lang": "ru",  # 语言（俄语）
        "page": "1",  # 页码（第一页）
        "query": keyword,  # 搜索关键词
        "resultset": "catalog",  # 结果集类型（商品目录）
        "sort": "popular",  # 排序方式（按人气）
        "spp": "30",  # 服务参数（可能影响价格计算）
        "suppressSpellcheck": "false"  # 禁用拼写检查
    }
    
    # 重试机制：最多重试3次
    for attempt in range(3):
        try:
            async with httpx.AsyncClient(timeout=10) as client:
                # 发送GET请求
                response = await client.get(
                    "https://search.wb.ru/exactmatch/ru/common/v9/search",  # Wildberries搜索API地址
                    headers=headers,
                    params=params
                )
                response.raise_for_status()  # 如果请求失败抛出异常
                result = response.json()  # 返回解析后的JSON数据
                
                # 缓存结果（2天）
                try:
                    await set_cache(cache_key, result, ttl=2 * 24 * 3600)
                    log_success(f"搜索成功并缓存: {keyword}")
                except Exception as cache_error:
                    log_warning(f"缓存搜索结果失败: {keyword}, 错误: {cache_error}")

                return result
                
        except httpx.RequestError as e:
            log_warning(f"搜索请求失败 (尝试 {attempt + 1}/3): {keyword}, 错误: {e}")
            if attempt < 2:  # 不是最后一次尝试
                await asyncio.sleep(1 * (attempt + 1))  # 递增延迟
                continue
        except httpx.HTTPStatusError as e:
            log_warning(f"搜索HTTP错误 (尝试 {attempt + 1}/3): {keyword}, 状态码: {e.response.status_code}")
            if attempt < 2:  # 不是最后一次尝试
                await asyncio.sleep(1 * (attempt + 1))  # 递增延迟
                continue
        except Exception as e:
            log_error(f"搜索异常 (尝试 {attempt + 1}/3): {keyword}", error=e)
            if attempt < 2:  # 不是最后一次尝试
                await asyncio.sleep(1 * (attempt + 1))  # 递增延迟
                continue
    
    log_error(f"搜索最终失败: {keyword}")
    return None  # 发生任何请求错误时返回None


def extract_product_ids_from_search(search_result: Dict[str, Any], limit: int = 50) -> List[int]:
    """从搜索结果中提取产品ID
    
    参数:
        search_result: search_wildberries 返回的搜索结果
        limit: 提取的产品ID数量限制，默认50
        
    返回:
        产品ID列表
    """
    try:
        products = search_result.get('data', {}).get('products', [])
        product_ids = []
        
        for product in products[:limit]:
            product_id = product.get('id')
            if product_id:
                product_ids.append(product_id)
        
        log_debug(f"提取产品ID成功: {len(product_ids)}/{len(products)}")
        return product_ids
        
    except Exception as e:
        log_error("提取产品ID失败", error=e)
        return []
