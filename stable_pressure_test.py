#!/usr/bin/env python3
"""
稳定版压力测试
专门针对照明相关关键词进行测试，避免跨类目干扰
"""

import asyncio
import aiohttp
import json
import time
import random
from test_keywords import keyword_info

async def stable_pressure_test():
    """稳定版压力测试"""
    
    print("🎯 稳定版压力测试")
    print("=" * 60)
    
    # 筛选照明相关关键词
    lighting_keywords = []
    lighting_terms = ['люстра', 'светильник', 'лампа', 'светодиод', 'освещение', 'плафон', 'бра', 'торшер', 'подсветка']
    
    for item in keyword_info:
        keyword = item['keyword'].lower()
        if any(term in keyword for term in lighting_terms):
            lighting_keywords.append(item['keyword'])
    
    print(f"📊 找到 {len(lighting_keywords)} 个照明相关关键词")
    
    # 测试配置
    test_keywords_count = min(50, len(lighting_keywords))  # 测试50个照明关键词
    max_concurrent = 10        # 10个并发
    target_product_id = 253486273
    
    # 随机选择关键词
    test_keywords = random.sample(lighting_keywords, test_keywords_count)
    
    print(f"📊 测试参数:")
    print(f"   关键词数量: {test_keywords_count}")
    print(f"   最大并发数: {max_concurrent}")
    print(f"   目标产品ID: {target_product_id}")
    print(f"   关键词类型: 照明相关")
    print("=" * 60)
    
    results = []
    start_time = time.time()
    
    async def test_single_keyword(session, keyword, test_id):
        """测试单个关键词"""
        request_data = {
            "keyword": keyword,
            "target_product_id": target_product_id
        }
        
        request_start = time.time()
        
        try:
            async with session.post(
                "http://localhost:8001/keyword-matching",
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时
            ) as response:
                request_end = time.time()
                processing_time = request_end - request_start
                
                if response.status == 200:
                    result = await response.json()
                    data = result.get('data', {})
                    
                    avg_similarity = data.get('avg_similarity', 0)
                    valid_scores = data.get('valid_scores', 0)
                    from_cache = data.get('from_cache', False)
                    
                    # 判断数据是否有效
                    data_valid = avg_similarity > 0 and valid_scores > 0
                    
                    status_icon = "✅" if data_valid else "⚠️"
                    cache_icon = "💾" if from_cache else "🆕"
                    
                    print(f"{status_icon} [{test_id:2d}] {keyword[:35]:<35} | 相似度: {avg_similarity:2d} | 有效评分: {valid_scores:2d} | 耗时: {processing_time:5.1f}s | {cache_icon}")
                    
                    return {
                        'test_id': test_id,
                        'keyword': keyword,
                        'success': True,
                        'processing_time': processing_time,
                        'avg_similarity': avg_similarity,
                        'valid_scores': valid_scores,
                        'similar_count': data.get('similar_count', 0),
                        'competitor_count': data.get('competitor_count', 0),
                        'from_cache': from_cache,
                        'data_valid': data_valid
                    }
                else:
                    error_text = await response.text()
                    print(f"❌ [{test_id:2d}] {keyword[:35]:<35} | 错误: HTTP {response.status} - {error_text[:50]}")
                    return {
                        'test_id': test_id,
                        'keyword': keyword,
                        'success': False,
                        'processing_time': processing_time,
                        'error': f"HTTP {response.status}: {error_text}",
                        'data_valid': False
                    }
                    
        except asyncio.TimeoutError:
            request_end = time.time()
            processing_time = request_end - request_start
            print(f"⏰ [{test_id:2d}] {keyword[:35]:<35} | 超时: {processing_time:.1f}s")
            return {
                'test_id': test_id,
                'keyword': keyword,
                'success': False,
                'processing_time': processing_time,
                'error': "Timeout",
                'data_valid': False
            }
        except Exception as e:
            request_end = time.time()
            processing_time = request_end - request_start
            print(f"❌ [{test_id:2d}] {keyword[:35]:<35} | 异常: {str(e)[:50]}")
            return {
                'test_id': test_id,
                'keyword': keyword,
                'success': False,
                'processing_time': processing_time,
                'error': str(e),
                'data_valid': False
            }
    
    # 创建会话
    connector = aiohttp.TCPConnector(limit=20, limit_per_host=20)
    async with aiohttp.ClientSession(connector=connector) as session:
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def controlled_test(keyword, test_id):
            async with semaphore:
                return await test_single_keyword(session, keyword, test_id)
        
        print("🚀 开始执行测试...")
        print(f"{'状态':<2} {'序号':<4} {'关键词':<35} | {'相似度':<6} | {'有效评分':<8} | {'耗时':<7} | {'类型'}")
        print("-" * 90)
        
        # 分批执行任务，避免一次性创建太多任务
        batch_size = 10
        for i in range(0, len(test_keywords), batch_size):
            batch_keywords = test_keywords[i:i+batch_size]
            batch_tasks = [
                controlled_test(keyword, i + j + 1)
                for j, keyword in enumerate(batch_keywords)
            ]
            
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, Exception):
                    print(f"❌ 批次异常: {result}")
                else:
                    results.append(result)
            
            # 批次间隔
            if i + batch_size < len(test_keywords):
                print(f"⏳ 批次完成，等待2秒...")
                await asyncio.sleep(2)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 分析结果
    print("\n" + "=" * 60)
    print("📊 稳定版压力测试结果分析")
    print("=" * 60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    valid_data_tests = sum(1 for r in results if r.get('data_valid', False))
    cached_results = sum(1 for r in results if r.get('from_cache', False))
    new_requests = total_tests - cached_results
    
    processing_times = [r['processing_time'] for r in results if r['success']]
    avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
    
    similarities = [r.get('avg_similarity', 0) for r in results if r.get('data_valid', False)]
    avg_similarity = sum(similarities) / len(similarities) if similarities else 0
    
    valid_scores_list = [r.get('valid_scores', 0) for r in results if r.get('data_valid', False)]
    avg_valid_scores = sum(valid_scores_list) / len(valid_scores_list) if valid_scores_list else 0
    
    print(f"📈 基础统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   成功请求: {successful_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"   有效数据: {valid_data_tests} ({valid_data_tests/total_tests*100:.1f}%)")
    print(f"   新请求数: {new_requests}")
    print(f"   缓存命中: {cached_results} ({cached_results/total_tests*100:.1f}%)")
    
    print(f"\n⏱️  性能统计:")
    print(f"   总耗时: {total_time:.1f} 秒")
    print(f"   平均处理时间: {avg_processing_time:.1f} 秒")
    print(f"   吞吐量: {total_tests/total_time:.2f} 请求/秒")
    
    print(f"\n📊 数据质量:")
    print(f"   平均相似度: {avg_similarity:.1f}")
    print(f"   平均有效评分数: {avg_valid_scores:.1f}")
    
    # 判断测试结果
    success_rate = successful_tests/total_tests*100 if total_tests > 0 else 0
    validity_rate = valid_data_tests/total_tests*100 if total_tests > 0 else 0
    
    if success_rate >= 90 and validity_rate >= 70:
        print(f"\n🎉 稳定版压力测试通过！")
        print(f"✅ 高成功率: {success_rate:.1f}%")
        print(f"✅ 良好数据有效性: {validity_rate:.1f}%")
        print(f"✅ 服务稳定性良好")
        
        if new_requests > 0:
            new_processing_times = [r['processing_time'] for r in results if r['success'] and not r.get('from_cache', True)]
            avg_new_processing_time = sum(new_processing_times) / len(new_processing_times) if new_processing_times else 0
            print(f"✅ 新请求平均处理时间: {avg_new_processing_time:.1f}秒")
        
        print(f"\n🚀 可以进行更大规模的压力测试")
        
    else:
        print(f"\n⚠️  稳定版压力测试需要关注")
        if success_rate < 90:
            print(f"   - 成功率偏低: {success_rate:.1f}%")
        if validity_rate < 70:
            print(f"   - 数据有效性偏低: {validity_rate:.1f}%")
    
    # 显示失败的测试
    failed_tests = [r for r in results if not r['success']]
    if failed_tests:
        print(f"\n❌ 失败的测试 ({len(failed_tests)}个):")
        for test in failed_tests[:10]:  # 只显示前10个
            print(f"   - {test['keyword']}: {test.get('error', 'Unknown error')}")
    
    # 显示无效数据的测试
    invalid_data_tests = [r for r in results if r['success'] and not r.get('data_valid', False)]
    if invalid_data_tests:
        print(f"\n⚠️  数据无效的测试 ({len(invalid_data_tests)}个，可能是旧缓存):")
        for test in invalid_data_tests[:5]:  # 只显示前5个
            print(f"   - {test['keyword']}: 相似度={test.get('avg_similarity', 0)}, 有效评分={test.get('valid_scores', 0)}")

async def main():
    """主函数"""
    print("🎯 keywordMatching 稳定版压力测试")
    print("📝 专门测试照明相关关键词，确保数据质量")
    
    await stable_pressure_test()

if __name__ == "__main__":
    asyncio.run(main())
