任务 46/50
关键词: скоба для кабеля
目标产品ID: 303244457
================================================================================

# 关键词匹配分析系统

## 系统概述
这是一个基于产品相似度比较的关键词匹配分析系统。系统通过以下步骤工作：

1. **关键词搜索**: 使用给定关键词在Wildberries平台搜索相关产品
2. **产品筛选**: 提取搜索结果中的前50个产品ID
3. **相似度比较**: 将每个搜索到的产品与目标产品进行相似度比较
4. **统计分析**: 分析相似度分数，计算平均相似度、相似产品数量等统计指标
5. **结果保存**: 将分析结果保存到数据库中

## 核心功能模块

### 1. 产品相似度比较 (compare_products_by_ids)
- **功能**: 比较两个产品的相似度
- **输入**: 两个产品ID (product_id1, product_id2)
- **输出**: 相似度评分 (1-100分) 和分析说明
- **评分标准**:
  - 1级类目相同: 5-10分
  - 3级类目相似: 15-25分  
  - 标题相似性: 15-25分
  - 价格相似性: 10-15分
  - 图片描述相似性: 20-35分

### 2. 关键词匹配分析 (keywordMatching)
- **功能**: 分析关键词与目标产品的关联度
- **工作流程**:
  1. 检查缓存中是否已有分析结果
  2. 通过关键词搜索获取前50个相关产品
  3. 使用信号量控制并发，与目标产品进行相似度比较
  4. 统计分析结果并保存到数据库
- **输出指标**:
  - 平均相似度 (avg_similarity)
  - 相似产品数量 (similar_count, >65分)
  - 竞品数量 (competitor_count, >80分)
  - 有效评分数量 (valid_scores)

## 任务数据
处理前50个任务，主要围绕以下产品类别：
- 目标产品ID: 303244457 (线缆整理相关产品)
- 关键词类型: 主要为俄语关键词，涉及线缆整理、桌面收纳等相关产品

## 使用场景
1. **电商平台关键词优化**: 分析关键词与产品的匹配度
2. **竞品分析**: 识别同类竞争产品
3. **产品定位**: 了解产品在市场中的位置
4. **搜索引擎优化**: 优化产品在搜索结果中的表现

## 技术特点
- **异步处理**: 使用asyncio进行高效的并发处理
- **缓存机制**: 避免重复计算，提高系统效率
- **错误处理**: 完善的异常处理和日志记录
- **数据持久化**: 结果自动保存到数据库
- **并发控制**: 使用信号量限制并发请求数量

## 输出格式
```json
{
    "status": "success",
    "data": {
        "keyword": "关键词",
        "target_product_id": 产品ID,
        "avg_similarity": 平均相似度,
        "similar_count": 相似产品数量,
        "competitor_count": 竞品数量,
        "valid_scores": 有效评分数量,
        "created_at": "创建时间",
        "from_cache": false,
        "search_products_count": 搜索到的产品数量,
        "compared_products_count": 实际比较的产品数量
    }
}
```


---

## 当前任务
- 任务编号: 46/50
- 关键词: скоба для кабеля
- 目标产品ID: 303244457

---

## 产品比较系统提示词
#任务
-将两个产品的介绍进行对比,按照我的评分规则得出综合相似评分,总分数范围1-100:
1,判断两个产品的1级类目是否相同,分数5-10分
2,判断两个产品的3级类目是否相似,分数15-25分
3,判断两个产品的标题相似性,分数15-25分
4,判断两个产品的价格相似性,分数10-15分
5,判断两个产的图片描述相似性,分数20-35分

##思考过程
###产品文案参数思考过程
-如果不在1级类目中,大概率产品相似性不相关,应该给予较低的相识评价
-2级类目是产品的细分类,考虑到一个产品可能有多个二级类目,所以你需要理解两个产品都适合彼此的2级类目
-标题中涉及到产品名字,参数,功能,风格,使用场景,产品名字是首要思考的对象,其他参数其次
-如果两个产品的产品名字一致,参数偏差10%视为相似,偏差太大,需要考虑价格偏差,价格偏差15%以内视为相似.反之先观察价格再观察参数也成立
-图片描述主要查看形状，功能,使用场景,风格等维度考虑.与文案描述结合综合判断

##不相似产品定义
-如果两个产品一级类目无相似关系,价格和材质不同，安装方式不同，功能描述不同，参数差异差50%以上的产品,视为不相关产品,判定为1

##扣减规则
-如果两个产品一级类目不同，判定为20分
-如果两个产品一级类目相似，三级类目相似,但是参数偏差20%以上或者安装方式不同视为不相似,材质差异大,价格偏差40%以上视为不相似,判定为20-40分
-如果两个产品一级类目相似,形状差异大,如圆形与长条型,圆盘型与圆柱型,圆形与不规则型,扣10-20分
-如果两个产品使用场景差异大,如大厅顶灯与桌面台灯,室外路灯与室内灯具,先查看3级类目是否类似,如果不类似,扣15-25分.否则不扣分

##产品信息:
###产品1:
    {target_product}

###产品2:
    {product}

##输出要求
只需要输出1-100的综合相似值,不需要输出任何的推理思考过程.1为完全不相似,100为完全相似

##Output format:
{{"similar_scores":相似值,"reson":中文分析说明}}

##示例
{{"similar_scores":40,"reson":中文分析说明}}